// Модуль работы с маркерами поиска

// Отображение результата поиска
function displaySearchResult(lat, lon, displayName) {
    console.log('displaySearchResult called with:', { lat, lon, displayName });
    
    // Удаление предыдущего маркера
    if (window.searchMarker) {
        window.mymap.removeLayer(window.searchMarker);
        window.searchMarker = null;
    }

    // Создание нового маркера
    window.searchMarker = L.marker([lat, lon], {
        icon: L.icon({
            iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
            iconSize: [25, 41],
            iconAnchor: [12, 41],
            popupAnchor: [1, -34],
            shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
            shadowSize: [41, 41]
        })
    }).addTo(window.mymap);

    // displayName ни маркерга сақлаймиз
    window.searchMarker.displayName = displayName || 'Танланган жой';

    // Добавление popup с информацией
    const currentLang = window.currentLanguage || 'ru';
    const trans = window.translations[currentLang];
    const searchResultText = trans['search_result'] || 'Search Result';
    const coordinatesText = trans['coordinates'] || 'Coordinates';
    const unknownPlaceText = trans['unknown_place'] || 'Unknown place';
    
    const popupContent = `
        <div style="min-width: 200px;">
            <b>${searchResultText}:</b><br>
            <div style="margin-top: 5px;">${displayName || unknownPlaceText}</div>
            <div style="margin-top: 5px; font-size: 12px; color: #666;">
                ${coordinatesText}: ${lat.toFixed(6)}, ${lon.toFixed(6)}
            </div>
        </div>
    `;
    
    window.searchMarker.bindPopup(popupContent, {
        maxWidth: 300,
        minWidth: 200
    }).openPopup();

    // Центрирование карты с анимацией
    window.mymap.flyTo([lat, lon], 14, {
        duration: 1.5,
        easeLinearity: 0.5
    });
    
    // Попапни анимация тугагандан кейин қайта очиш
    window.mymap.once('moveend', function() {
        if (window.searchMarker) {
            window.searchMarker.openPopup();
        }
    });
}

// Функция очистки маркера поиска
function clearSearchMarker() {
    if (window.searchMarker) {
        window.mymap.removeLayer(window.searchMarker);
        window.searchMarker = null;
    }
}

// Экспорт функций
window.SearchMarker = {
    displaySearchResult,
    clearSearchMarker
};

// Для обратной совместимости
window.displaySearchResult = displaySearchResult; 