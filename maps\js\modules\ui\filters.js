// Модуль работы с фильтрами

// Сохранение состояния фильтров
window.saveFilterState = function () {
    const filterState = {
        region: document.getElementById('region').value,
        area: document.getElementById('area').value,
        bsSearch: document.getElementById('bs-search').value,
        status: document.getElementById('status').value,
        durationFilter: document.getElementById('duration-filter').value
    };
    localStorage.setItem('filterState', JSON.stringify(filterState));
}

// Фильтрация БС по имени или номеру
window.filterByBsName = function (stations, searchText) {
    searchText = searchText.toLowerCase();
    return stations.filter(station => {
        const bsName = (station.bsName || station.name || '').toLowerCase();
        const bsNumber = (station.bsNumber || station.number || '').toString().toLowerCase();
        return bsName.includes(searchText) || bsNumber.includes(searchText);
    });
}

// Применение фильтра по статусу и отображение точек
window.applyStatusFilter = function (pointsToFilter, statusValue, isRefresh = false, useSelects = true) {
    // Всегда получаем значения фильтров
    const regionId = document.getElementById('region').value;
    const areaId = document.getElementById('area').value;
    const durationFilter = document.getElementById('duration-filter').value;



    // Кластерларни тозалаш
    if (window.bsMarkerCluster) {
        window.clearClusters();
    }
    
    // Эски маркерларни ҳам тозалаш (backward compatibility учун)
    window.pointLayers.forEach(marker => window.mymap.removeLayer(marker));
    window.pointLayers.length = 0;

    let displayPoints = [...pointsToFilter];

    // Фильтр по статусу
    if (statusValue === 'online') {
        displayPoints = pointsToFilter.filter(point => {
            return point.status === false || point.status === 'false' || point.status === 0 || point.status === '0';
        });
    } else if (statusValue === 'offline') {
        displayPoints = pointsToFilter.filter(point => {
            return point.status === true || point.status === 'true' || point.status === 1 || point.status === '1';
        });
    }

    // Фильтр по региону
    if (regionId) {
        displayPoints = displayPoints.filter(point => point.region_id == regionId);
    }

    // Фильтр по району
    if (areaId) {
        displayPoints = displayPoints.filter(point => point.area_id == areaId);
    }

    // Фильтр по продолжительности аварии
    // Duration фильтри фақат downtime legend дан танланганда ишлайди
    if (durationFilter && durationFilter !== '') {
        // Аввал status фильтрини текширамиз
        const currentStatus = document.getElementById('status').value;
        
        if (durationFilter === 'all') {
            // "All" танланганда:
            // - Агар бу downtime legend дан бўлса (status='all'), фақат offline БС ларни кўрсатамиз
            // - Агар вилоят жадвалидан бўлса (status='online'/'offline'), status фильтрига мувофиқ кўрсатамиз
            if (currentStatus === 'all' || currentStatus === '') {
                // Downtime legend дан келган - фақат offline БС лар
                displayPoints = displayPoints.filter(point => {
                    return point.status === true || point.status === 'true' || point.status === 1 || point.status === '1';
                });
            }
            // Акс ҳолда status фильтри ўз ишини бажарган
        } else {
            // Конкретный duration танланганда - фақат шу duration даги offline БС лар
            displayPoints = displayPoints.filter(point => {
                // Проверяем, что БС в аварии
                const isOffline = point.status === true || point.status === 'true' || point.status === 1 || point.status === '1';
                if (!isOffline) return false;
                
                // Машина времени режимида алоҳида функцияни ишлатиш
                let pointDurationGroup;
                if (window.isTimeMachineEnabled && window.getDowntimeDurationForTimeMachine) {
                    pointDurationGroup = window.getDowntimeDurationForTimeMachine(point);
                } else {
                    pointDurationGroup = window.getDowntimeDuration(point);
                }
                
                return pointDurationGroup == parseInt(durationFilter);
            });
        }
    }

    // Обновление статистики
    const totalDisplayed = displayPoints.length;
    const inactiveDisplayed = displayPoints.filter(point => {
        return point.status === true || point.status === 'true' || point.status === 1 || point.status === '1';
    }).length;
    const activeDisplayed = totalDisplayed - inactiveDisplayed;

    document.getElementById('total-bs-count').textContent = totalDisplayed;
    document.getElementById('active-bs-count').textContent = activeDisplayed;
    document.getElementById('inactive-bs-count').textContent = inactiveDisplayed;

    // Расчет статистики продолжительности для текущего фильтра
    let pointsForDurationStats = [...window.globalStations];
    if (regionId) {
        pointsForDurationStats = pointsForDurationStats.filter(point => point.region_id == regionId);
    }
    if (areaId) {
        pointsForDurationStats = pointsForDurationStats.filter(point => point.area_id == areaId);
    }
    window.calculateDurationStats(pointsForDurationStats);

    // Отображение отфильтрованных точек через updateMapMarkers
    
    // updateMapMarkers ишлатиш (логик кластерлаш учун)
    if (window.updateMapMarkers) {
        window.updateMapMarkers(displayPoints, false);
    } else {
        // Эски усул - фоллбэк
        if (displayPoints && displayPoints.length > 0) {
            const validMarkers = [];
            
            displayPoints.forEach(function (point) {
                const circleMarker = window.createBSMarker(point, statusValue, false);
                if (circleMarker) {
                    validMarkers.push(circleMarker);
                    window.pointLayers.push(circleMarker);
                }
            });
            
            // Кластерга қўшиш
            if (window.bsMarkerCluster && validMarkers.length > 0) {
                window.addMarkersToCluster(validMarkers);
            } else {
                validMarkers.forEach(marker => marker.addTo(window.mymap));
            }
        }
    }

    // Обновление визуального выделения региона
    if (isRefresh) {
        window.updateRegionsStats();
    }
    
    // РЦМУ маркерларини янгилаш
    if (window.refreshRcmuMarkersForFilters) {
        window.refreshRcmuMarkersForFilters();
    }
}

// Применение текущих фильтров
window.applyCurrentFilters = function () {
    // Other alarms режимида алоҳида логика
    if (window.AlarmManager && typeof window.AlarmManager.isEnabled === 'function' && window.AlarmManager.isEnabled()) {
        window.AlarmManager.refresh(true, true);
        return;
    }
    
    const regionId = document.getElementById('region').value;
    const areaId = document.getElementById('area').value;
    const bsSearchText = document.getElementById('bs-search').value;
    const statusValue = document.getElementById('status').value;

    let filteredPoints = [...window.globalStations];

    // Фильтр по региону
    if (regionId) {
        filteredPoints = filteredPoints.filter(point => point.region_id == regionId);
    }

    // Фильтр по району
    if (areaId) {
        filteredPoints = filteredPoints.filter(point => point.area_id == areaId);
    }

    // Фильтр по имени БС
    if (bsSearchText) {
        filteredPoints = window.filterByBsName(filteredPoints, bsSearchText);
    }

    // Применение фильтра статуса
    window.applyStatusFilter(filteredPoints, statusValue, false, false);
    
    // РЦМУ маркерларини янгилаш
    if (window.refreshRcmuMarkersForFilters) {
        window.refreshRcmuMarkersForFilters();
    }
}

// Сброс всех фильтров
window.resetAllFilters = function () {
    document.getElementById('region').value = '';
    document.getElementById('area').value = '';
    document.getElementById('bs-search').value = '';
    document.getElementById('status').value = 'all';
    // Duration фильтрни тўлиқ сброс қилиш
    document.getElementById('duration-filter').value = '';

    // Очистка районов
    const areaSelect = document.getElementById('area');
    areaSelect.innerHTML = '<option value="">' + window.translations[window.currentLanguage]['all_areas'] + '</option>';
    areaSelect.disabled = true;

    // Сброс активного региона в таблице ва "Все регионы" ни активлаштириш
    const activeRegion = document.querySelector('.region-row.active');
    if (activeRegion) {
        activeRegion.classList.remove('active');
    }
    
    // "Все регионы" ни активлаштириш
    const allRegionsRow = document.querySelector('.region-row[data-region-id=""]');
    if (allRegionsRow) {
        allRegionsRow.classList.add('active');
    }

    // Скрываем фильтр района
    const areaFilterGroup = document.querySelector('.filter-group:has(#area)');
    if (areaFilterGroup) {
        areaFilterGroup.classList.remove('show');
    }

    // Применение фильтров
    window.applyStatusFilter(window.globalStations, 'all', true);

    // Сохранение состояния - отключено, чтобы не сохранять фильтры
    // window.saveFilterState();

    // Сброс карты на общий вид
    window.mymap.setView([41.3, 69.3], 6);
    
    // РЦМУ маркерларини янгилаш
    if (window.refreshRcmuMarkersForFilters) {
        window.refreshRcmuMarkersForFilters();
    }
}

// Расчет статистики продолжительности
window.calculateDurationStats = function (filteredPoints) {
    // Сброс статистики
    window.durationStats = {
        '1': 0, '2': 0, '3': 0, '4': 0, '6': 0, '7': 0, '8': 0
    };

    // Фильтрация только аварийных БС
    const offlinePoints = filteredPoints.filter(point => {
        return point.status === true || point.status === 'true' || point.status === 1 || point.status === '1';
    });

    // Группировка по продолжительности
    offlinePoints.forEach(point => {
        let durationGroup;
        // Машина времени режимида алоҳида функцияни ишлатиш
        if (window.isTimeMachineEnabled && window.getDowntimeDurationForTimeMachine) {
            durationGroup = window.getDowntimeDurationForTimeMachine(point);
        } else {
            durationGroup = window.getDowntimeDuration(point);
        }
        
        if (durationGroup > 0) {
            window.durationStats[durationGroup]++;
        }
    });

    // Обновление отображения
    window.updateDurationStats();

    return window.durationStats;
}

// Обработчик изменения района
window.handleAreaChange = function (e) {
    // Применяем текущие фильтры
    window.applyCurrentFilters();
    
    // Обновляем статистику регионов
    window.updateRegionsStats();
    
    // Районлар статистикасини янгилаш
    const regionId = document.getElementById('region').value;
    if (regionId && window.updateAreasStats) {
        window.updateAreasStats(regionId);
    }
    
    // Центрирование карты на район (если есть координаты)
    const areaId = this.value;
    if (areaId && window.autoCenterEnabled !== false) {
        // Получаем БС выбранного района
        const areaStations = window.globalStations.filter(station => station.area_id == areaId);
        
        if (areaStations.length > 0) {
            // Вычисляем центр района
            const center = window.calculateCenter(areaStations);
            if (center) {
                window.mymap.setView([center.lat, center.lon], 12);
            }
        }
    }
    
    // Пересчет статистики продолжительности для выбранного района
    let pointsForDurationStats = [...window.globalStations];
    
    if (regionId) {
        pointsForDurationStats = pointsForDurationStats.filter(point => point.region_id == regionId);
    }
    
    if (areaId) {
        pointsForDurationStats = pointsForDurationStats.filter(point => point.area_id == areaId);
    }
    
    window.calculateDurationStats(pointsForDurationStats);
}

// Обновление статистики продолжительности
window.updateDurationStats = function () {
    // Машина времени режимида алоҳида функцияни ишлатиш
    if (window.isTimeMachineEnabled) {
        // Машина времени учун алоҳида статистика ҳисоблаш
        if (window.calculateDurationStatsForTimeMachine) {
            window.calculateDurationStatsForTimeMachine(window.globalStations);
        }
        return;
    }

    // Обновление счетчиков
    [1, 2, 3, 4, 6, 7, 8].forEach(i => {
        const countElement = document.getElementById('downtime-count-' + i);
        if (countElement) {
            countElement.textContent = window.durationStats[i] || 0;
        }
    });

    // Общее количество аварийных БС
    const totalOffline = Object.values(window.durationStats).reduce((acc, val) => acc + val, 0);
    document.getElementById('downtime-count-all').textContent = totalOffline;

    // Применение активного состояния
    const currentDurationFilter = document.getElementById('duration-filter').value;
    const durationItems = document.querySelectorAll('.downtime-item');
    durationItems.forEach(item => {
        const duration = item.getAttribute('data-duration');
        // Фақат duration фильтр танланган бўлса актив қилиш
        if (currentDurationFilter && currentDurationFilter !== '') {
            item.classList.toggle('active', duration === currentDurationFilter);
        } else {
            // Duration фильтр бўш бўлса, барчасини ноактив қилиш
            item.classList.remove('active');
        }
    });
} 