// UI компонентлари инициализацияси

// Инициализация loader
function initializeLoader() {
    setTimeout(function () {
        const loaderTextSpans = document.querySelectorAll('.loader-text span');
        loaderTextSpans.forEach(span => {
            span.style.opacity = '1';
            span.style.animation = 'none';
        });

        // Скрыть loader через 0.8 секунды
        setTimeout(function () {
            const loader = document.getElementById('loader');
            if (loader) {
                loader.style.opacity = '0';
                loader.style.visibility = 'hidden';
            }
        }, 800);
    }, 800);

    // Ждем загрузки всех ресурсов
    window.addEventListener('load', function () {
        setTimeout(function () {
            const loader = document.getElementById('loader');
            if (loader) {
                loader.style.opacity = '0';
                loader.style.visibility = 'hidden';
            }
        }, 800);
    });
}

// Инициализация управления языком
function initializeLanguageControls() {
    const langRu = document.getElementById('lang-ru');
    const langEn = document.getElementById('lang-en');

    if (langRu) {
        langRu.addEventListener('click', function () {
            window.changeLanguage('ru');
        });
    }

    if (langEn) {
        langEn.addEventListener('click', function () {
            window.changeLanguage('en');
        });
    }

    // Установка начального языка
    window.changeLanguage(window.currentLanguage);
}

// Инициализация фильтров
function initializeFilters() {
    // Сброс фильтров
    document.getElementById('reset-filters').addEventListener('click', window.resetAllFilters);

    // Поиск БС
    document.getElementById('bs-search').addEventListener('input', function () {
        window.applyCurrentFilters();
    });

    // Изменение региона
    document.getElementById('region').addEventListener('change', window.handleRegionChange);

    // Изменение района
    document.getElementById('area').addEventListener('change', window.handleAreaChange);

    // Изменение статуса
    document.getElementById('status').addEventListener('change', function () {
        window.applyCurrentFilters();
        // Обновляем статистику регионов
        window.updateRegionsStats();
    });

    // Фильтр продолжительности аварий
    document.getElementById('duration-filter').addEventListener('change', function () {
        window.applyCurrentFilters();
    });
}

// Инициализация контролов карты
function initializeMapControls() {
    // Режим карты/спутник
    document.getElementById('map-button').addEventListener('click', function () {
        window.isMapMode = true;
        this.classList.add('active');
        document.getElementById('satellite-button').classList.remove('active');

        if (window.isYandexMode && window.yandexMap) {
            window.yandexMap.setType('yandex#map');
        }

        updateMapLayer();
    });

    document.getElementById('satellite-button').addEventListener('click', function () {
        window.isMapMode = false;
        this.classList.add('active');
        document.getElementById('map-button').classList.remove('active');

        if (window.isYandexMode && window.yandexMap) {
            window.yandexMap.setType('yandex#satellite');
        }

        updateMapLayer();
    });

    // Источники карты
    document.getElementById('osm-source-button').addEventListener('click', handleOSMSourceClick);
    document.getElementById('yandex-source-button').addEventListener('click', handleYandexSourceClick);
    document.getElementById('google-source-button').addEventListener('click', handleGoogleSourceClick);

    // Автоматик марказлаштириш toggle
    const autoCenterToggle = document.getElementById('auto-center-toggle');
    if (autoCenterToggle) {
        // Бошланғич ҳолатни localStorage дан ўқиш
        const savedState = localStorage.getItem('autoCenterEnabled');
        if (savedState !== null) {
            autoCenterToggle.checked = savedState === 'true';
            window.autoCenterEnabled = savedState === 'true';
        } else {
            // Бошланғич ҳолат - ёқилган
            window.autoCenterEnabled = true;
            autoCenterToggle.checked = true;
            localStorage.setItem('autoCenterEnabled', 'true');
        }

        // Toggle ўзгарганда
        autoCenterToggle.addEventListener('change', function (e) {
            window.autoCenterEnabled = this.checked;
            // Ҳолатни сақлаш
            localStorage.setItem('autoCenterEnabled', this.checked.toString());
        });
    }

    // Ўзбекистонга қайтиш тугмаси
    const returnToUzbekistanBtn = document.getElementById('return-to-uzbekistan');
    if (returnToUzbekistanBtn) {
        returnToUzbekistanBtn.addEventListener('click', function () {
            // Ўзбекистон марказига қайтариш
            window.mymap.flyTo([41.3, 69.3], 5, {
                duration: 1.5
            });
            // Тугмани яшириш
            this.style.display = 'none';
        });
    }
}

// Инициализация легенды
function initializeLegend() {
    const durationItems = document.querySelectorAll('.downtime-item');
    durationItems.forEach(item => {
        item.addEventListener('click', function (e) {
            e.preventDefault();

            // Авария режимида бўлса, AlarmManager орқали ишлаймиз
            if (window.AlarmManager && typeof window.AlarmManager.isEnabled === 'function' && window.AlarmManager.isEnabled()) {
                // AlarmManager ўзи handle қилади
                return;
            }

            // Normal режим учун
            const duration = this.getAttribute('data-duration');

            // Актив классни янгилаш
            document.querySelectorAll('.downtime-item').forEach(el => el.classList.remove('active'));
            this.classList.add('active');

            // Фильтрни қўллаш
            window.filterByDowntimeDuration(duration);
        });
    });
}

// Export функций для использования в других модулях
window.initializeLoader = initializeLoader;
window.initializeLanguageControls = initializeLanguageControls;
window.initializeFilters = initializeFilters;
window.initializeMapControls = initializeMapControls;
window.initializeLegend = initializeLegend; 