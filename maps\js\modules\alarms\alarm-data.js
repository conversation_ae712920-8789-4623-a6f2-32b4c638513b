// Alarm Data Module - Alarm Manager модулининг маълумот юклаш функциялари

window.AlarmData = (function() {
    'use strict';
    
    // Приватные переменные
    let alarmTypesCache = null;
    let currentAlarmData = [];
    let originalAlarmData = [];
    
    // Авария турларини юклаш
    async function loadAlarmTypes() {
        try {
            const response = await fetch('/map/alarms/api/alarm-types/');
            if (!response.ok) throw new Error('Failed to load alarm types');
            
            const data = await response.json();
            alarmTypesCache = data.alarm_types;
            
            // UI ни янгилаш
            if (window.AlarmUI && window.AlarmUI.updateAlarmTypesList) {
                window.AlarmUI.updateAlarmTypesList(alarmTypesCache, []);
            }
            
        } catch (error) {
            console.error('[AlarmData] Error loading alarm types:', error);
        }
    }
    
    // Авария маълумотларини юклаш
    async function loadAlarmData(selectedAlarmTypes, isManual = false, isFromFilter = false) {
        if (!selectedAlarmTypes || selectedAlarmTypes.length === 0) return;
        
        // Loader ни фақат manual режимда ва фильтрдан келмаганда кўрсатиш
        if (isManual && !isFromFilter && window.showLoader) {
            window.showLoader();
        }
        
        try {
            // Фильтрларни олиш
            const regionId = document.getElementById('region').value;
            const areaId = document.getElementById('area').value;
            
            // Duration filter қийматини олиш
            const selectedDuration = document.querySelector('.downtime-item.active')?.getAttribute('data-duration') || 'all';
            
            // URL параметрларни тайёрлаш
            const params = new URLSearchParams();
            selectedAlarmTypes.forEach(type => params.append('alarm_types[]', type));
            if (regionId) params.append('region_id', regionId);
            if (areaId) params.append('area_id', areaId);
            params.append('language', window.currentLanguage || 'ru');
            
            // Time Machine режими учун
            if (window.timeMachineDate) {
                params.append('datetime', window.timeMachineDate.toISOString());
                const response = await fetch(`/map/alarms/api/historical-alarms/?${params}`);
                currentAlarmData = await response.json();
            } else {
                // Жорий аварияларни олиш
                const response = await fetch(`/map/alarms/api/current-alarms/?${params}`);
                currentAlarmData = await response.json();
            }
            
            // Оригинал маълумотларни сақлаш
            originalAlarmData = JSON.parse(JSON.stringify(currentAlarmData));
            
            // Duration filter'ни қўллаш - оригинални ўзгартирмасдан
            let displayData = currentAlarmData;
            if (selectedDuration && selectedDuration !== 'all') {
                // Янги объект яратиш
                displayData = {
                    ...currentAlarmData,
                    stations: filterAlarmsByDuration(currentAlarmData.stations, selectedDuration)
                };
            }
            
            // Маркерларни янгилаш - фильтрланган маълумотлар билан
            if (window.AlarmMarkers && window.AlarmMarkers.updateAlarmMarkers) {
                window.AlarmMarkers.updateAlarmMarkers(displayData);
            }
            
            // Статистикани янгилаш - фильтрланган маълумотларни узатамиз
            if (window.AlarmStats && window.AlarmStats.updateAlarmStatistics) {
                window.AlarmStats.updateAlarmStatistics(selectedAlarmTypes, isManual, isFromFilter, displayData);
            }
            
        } catch (error) {
            console.error('[AlarmData] Error loading alarm data:', error);
        } finally {
            // Loader ни фақат manual режимда ва фильтрдан келмаганда яшириш
            if (isManual && !isFromFilter && window.hideLoader) {
                window.hideLoader();
            }
        }
    }
    
    // Авария режими учун duration filter функцияси
    function filterAlarmsByDuration(stations, durationGroup) {
        if (!stations || durationGroup === 'all') return stations;
        
        return stations.filter(station => {
            if (!station.other_alarms || station.other_alarms.length === 0) return false;
            
            // Энг узоқ давом этган аварияни топиш
            const maxDuration = Math.max(...station.other_alarms.map(a => a.duration_hours || 0));
            
            // Duration group га мос келишини текшириш
            switch (durationGroup) {
                case '1': // До 1 часа
                    return maxDuration <= 1;
                case '2': // До 2 часов
                    return maxDuration <= 2;
                case '3': // До 3 часов
                    return maxDuration <= 3;
                case '4': // До 4 часов
                    return maxDuration <= 4;
                case '6': // 4 часа - 1 день
                    return maxDuration > 4 && maxDuration <= 24;
                case '7': // 1 день - 7 дней
                    return maxDuration > 24 && maxDuration <= 168;
                case '8': // Более 7 дней
                    return maxDuration > 168;
                default:
                    return true;
            }
        });
    }
    
    // Авария турлари рўйхатини янгилаш
    function updateAlarmTypesList() {
        if (window.AlarmUI && window.AlarmUI.updateAlarmTypesList && alarmTypesCache) {
            window.AlarmUI.updateAlarmTypesList(alarmTypesCache, []);
        }
    }
    
    // Public API
    return {
        loadAlarmTypes,
        loadAlarmData,
        filterAlarmsByDuration,
        updateAlarmTypesList,
        getCurrentAlarmData: () => currentAlarmData,
        getOriginalAlarmData: () => originalAlarmData
    };
})(); 