// Муддат статистикаси модули

// Подсчет статистики продолжительности простоя
window.calculateDurationStats = function(stations) {
    // Танланган вилоят ва туманни олиш
    const regionElement = document.getElementById('region');
    const areaElement = document.getElementById('area');
    const regionId = regionElement ? regionElement.value : '';
    const areaId = areaElement ? areaElement.value : '';
    
    // Фильтрлаш
    let filteredStations = [...stations];
    if (regionId) {
        filteredStations = filteredStations.filter(station => station.region_id == regionId);
    }
    if (areaId) {
        filteredStations = filteredStations.filter(station => station.area_id == areaId);
    }
    
    // Сброс статистики - янги группалар қўшилди
    window.durationStats = { '1': 0, '2': 0, '3': 0, '4': 0, '6': 0, '7': 0, '8': 0 };

    // Фильтрация только аварийных БС
    const offlineStations = filteredStations.filter(station => {
        return station.status === true || station.status === 'true' || station.status === 1 || station.status === '1';
    });

    // Группировка по продолжительности
    offlineStations.forEach(station => {
        const durationGroup = getDowntimeDuration(station);
        if (durationGroup > 0) {
            window.durationStats[durationGroup]++;
        }
    });

    // Обновление отображения
    updateDurationDisplay();

    return window.durationStats;
}

// Определение группы продолжительности простоя
function getDowntimeDuration(station) {
    if (!station.status) return 0;

    let durationHours = 0;
    


    // 1. Попытка получить продолжительность из downtime_hours (машина времени)
    if (station.downtime_hours !== undefined && station.downtime_hours !== null) {
        durationHours = parseFloat(station.downtime_hours);
    }
    // 2. Проверяем alarms_by_tech (наиболее точный источник)
    else if (station.alarms_by_tech && Object.keys(station.alarms_by_tech).length > 0) {
        Object.values(station.alarms_by_tech).forEach(alarm => {
            let alarmDurationHours = 0;
            
            // Если есть duration_hours (машина времени)
            if (alarm.duration_hours !== undefined && alarm.duration_hours !== null) {
                alarmDurationHours = parseFloat(alarm.duration_hours);
            }
            // Если есть appeartime но нет cleartime (текущая авария в реальном времени)
            else if (alarm.appeartime && !alarm.cleartime) {
                const appearTime = new Date(alarm.appeartime);
                const currentTime = window.selectedDateTime || new Date();
                const duration = currentTime - appearTime;
                alarmDurationHours = duration / (1000 * 60 * 60);
            }
            // Если есть duration в строковом формате
            else if (alarm.duration) {
                if (typeof alarm.duration === 'string') {
                    const match = alarm.duration.match(/(\d+)\s*hours?\s*(\d+)?\s*minutes?/i);
                    if (match) {
                        alarmDurationHours = parseInt(match[1]) + (parseInt(match[2] || 0) / 60);
                    }
                } else if (typeof alarm.duration === 'number') {
                    alarmDurationHours = alarm.duration;
                }
            }
            
            durationHours = Math.max(durationHours, alarmDurationHours);
        });
    }
    // 3. Проверяем отдельные поля времени
    else if (station.alarm_appear_time || station.changedate) {
        const appearTime = new Date(station.alarm_appear_time || station.changedate);
        if (!isNaN(appearTime.getTime())) {
            const currentTime = window.selectedDateTime || new Date();
            const duration = currentTime - appearTime;
            durationHours = duration / (1000 * 60 * 60);
        }
    }
    // 4. Парсинг из calcTime строки (бэкенд расчет)
    else if (station.calcTime) {
        const calcTimeStr = String(station.calcTime);
        
        // Пробуем различные форматы
        let match = calcTimeStr.match(/(\d+)\s*hours?\s*(\d+)?\s*minutes?/i);
        if (match) {
            durationHours = parseInt(match[1]) + (parseInt(match[2] || 0) / 60);
        }
        // Формат "X:Y:Z" (часы:минуты:секунды)
        else {
            match = calcTimeStr.match(/(\d+):(\d+):(\d+)/);
            if (match) {
                durationHours = parseInt(match[1]) + parseInt(match[2])/60 + parseInt(match[3])/3600;
            }
            // Формат "X day(s), Y:Z:W"
            else {
                match = calcTimeStr.match(/(\d+)\s*days?,\s*(\d+):(\d+):(\d+)/i);
                if (match) {
                    durationHours = parseInt(match[1]) * 24 + parseInt(match[2]) + parseInt(match[3])/60 + parseInt(match[4])/3600;
                }
            }
        }
    }



    // Группировка по часам - янги группалар қўшилди
    if (durationHours <= 1) return 1;
    else if (durationHours <= 2) return 2;
    else if (durationHours <= 3) return 3;
    else if (durationHours <= 4) return 4;
    else if (durationHours <= 24) return 6;  // 4 соат - 1 кун
    else if (durationHours <= 168) return 7; // 1 кун - 7 кун (168 соат = 7 кун)
    else return 8; // 7 кундан кўп
}

// Обновление отображения статистики продолжительности
function updateDurationDisplay() {
    // Обновление счетчиков для каждой группы - 8 группа учун янгиланди
    [1, 2, 3, 4, 6, 7, 8].forEach(i => {
        const countElement = document.getElementById('downtime-count-' + i);
        if (countElement) {
            countElement.textContent = window.durationStats[i] || 0;
        }
    });

    // Общее количество аварийных БС
    const totalOffline = Object.values(window.durationStats).reduce((acc, val) => acc + val, 0);
    const countAllElement = document.getElementById('downtime-count-all');
    if (countAllElement) {
        countAllElement.textContent = totalOffline;
    }
}

// Получение продолжительности простоя в формате для отображения
window.getFormattedDowntime = function(station) {
    if (!station.status) return '';

    let durationHours = 0;
    let hasValidData = false;

    // 1. Получаем продолжительность из downtime_hours (машина времени)
    if (station.downtime_hours !== undefined && station.downtime_hours !== null) {
        durationHours = parseFloat(station.downtime_hours);
        hasValidData = true;
    }
    // 2. Проверяем alarms_by_tech
    else if (station.alarms_by_tech && Object.keys(station.alarms_by_tech).length > 0) {
        Object.values(station.alarms_by_tech).forEach(alarm => {
            let alarmDurationHours = 0;
            
            // Если есть duration_hours (машина времени)
            if (alarm.duration_hours !== undefined && alarm.duration_hours !== null) {
                alarmDurationHours = parseFloat(alarm.duration_hours);
                hasValidData = true;
            }
            // Если есть appeartime но нет cleartime (текущая авария в реальном времени)
            else if (alarm.appeartime && !alarm.cleartime) {
                const appearTime = new Date(alarm.appeartime);
                if (!isNaN(appearTime.getTime())) {
                    const currentTime = window.selectedDateTime || new Date();
                    const duration = currentTime - appearTime;
                    alarmDurationHours = duration / (1000 * 60 * 60);
                    hasValidData = true;
                }
            }
            // Если есть duration в строковом формате
            else if (alarm.duration) {
                if (typeof alarm.duration === 'string') {
                    const match = alarm.duration.match(/(\d+)\s*hours?\s*(\d+)?\s*minutes?/i);
                    if (match) {
                        alarmDurationHours = parseInt(match[1]) + (parseInt(match[2] || 0) / 60);
                        hasValidData = true;
                    }
                } else if (typeof alarm.duration === 'number') {
                    alarmDurationHours = alarm.duration;
                    hasValidData = true;
                }
            }
            
            durationHours = Math.max(durationHours, alarmDurationHours);
        });
    }
    // 3. Проверяем отдельные поля времени
    else if (station.alarm_appear_time || station.changedate) {
        const appearTime = new Date(station.alarm_appear_time || station.changedate);
        if (!isNaN(appearTime.getTime())) {
            const currentTime = window.selectedDateTime || new Date();
            const duration = currentTime - appearTime;
            durationHours = duration / (1000 * 60 * 60);
            hasValidData = true;
        }
    }
    // 4. Если есть готовая строка calcTime - используем её
    else if (station.calcTime) {
        return String(station.calcTime);
    }

    // Если нет данных, возвращаем пустую строку
    if (!hasValidData || durationHours <= 0) {
        return '';
    }

    // Форматируем для отображения
    if (durationHours < 1) {
        const minutes = Math.floor(durationHours * 60);
        return minutes + ' ' + (window.currentLanguage === 'ru' ? 'мин' : 'min');
    }

    const hours = Math.floor(durationHours);
    const minutes = Math.floor((durationHours - hours) * 60);

    if (hours < 24) {
        return minutes > 0 ?
            hours + ' ' + (window.currentLanguage === 'ru' ? 'ч' : 'h') + ' ' + minutes + ' ' + (window.currentLanguage === 'ru' ? 'мин' : 'min') :
            hours + ' ' + (window.currentLanguage === 'ru' ? 'ч' : 'h');
    }

    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;

    let result = days + ' ' + (window.currentLanguage === 'ru' ? 'д' : 'd');
    if (remainingHours > 0) {
        result += ' ' + remainingHours + ' ' + (window.currentLanguage === 'ru' ? 'ч' : 'h');
    }

    return result;
}

// Фильтрация по продолжительности простоя
window.filterByDowntimeDuration = function(durationGroup) {
    // Авария режимида бўлса, AlarmManager орқали ишлаймиз
    if (window.AlarmManager && typeof window.AlarmManager.isEnabled === 'function' && window.AlarmManager.isEnabled()) {
        // AlarmManager ўзи handle қилади, фақат event trigger қиламиз
        const event = new CustomEvent('durationFilterChanged', {
            detail: { duration: durationGroup }
        });
        window.dispatchEvent(event);
        return;
    }
    
    // Normal режим учун давом этамиз
    if (!window.globalStations) return;

    // Duration filter қийматини ўрнатиш
    const durationFilter = document.getElementById('duration-filter');
    if (durationFilter) {
        durationFilter.value = durationGroup;
    }

    // Фильтрларни қўллаш
    window.applyCurrentFilters();

    // Active классини янгилаш
    document.querySelectorAll('.downtime-item').forEach(item => {
        const duration = item.getAttribute('data-duration');
        item.classList.toggle('active', duration === durationGroup);
    });
} 