document.addEventListener('DOMContentLoaded', () => {
    console.log("RCMU Treez script started (v.<PERSON>tra<PERSON>).");

    // Helper function to get CSRF token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    const mainContainer = document.getElementById('bs-down-container');
    if (!mainContainer) {
        console.error('Error: Main container #bs-down-container not found.');
        return;
    }

    const isAdmin = mainContainer.dataset.isAdmin === 'true';
    const currentLang = mainContainer.dataset.languageCode || 'ru';

    // Bootstrap Modal Initialization
    const modalElement = document.getElementById('editReasonModal');
    if (!modalElement) {
        console.error("Bootstrap modal element #editReasonModal not found!");
        return;
    }
    const reasonModal = new bootstrap.Modal(modalElement);

    const editForm = document.getElementById('edit-reason-form');
    const editReasonIdInput = document.getElementById('edit-reason-id');
    const modalBody = modalElement.querySelector('.form-group-modal');
    const modalContext = document.getElementById('modal-cluster-context');

    // --- NEW DEBOUNCED FILTERING LOGIC ---
    let debounceTimer;
    const filterInputs = document.querySelectorAll('tr.filters input[type="text"]');

    filterInputs.forEach(input => {
        input.addEventListener('keyup', () => {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => {
                applyColumnFilters();
            }, 500); // Wait for 500ms after user stops typing
        });
    });

    function applyColumnFilters() {
        const url = new URL(window.location.href);
        url.searchParams.forEach((_, key) => url.searchParams.delete(key)); // Clear old params

        filterInputs.forEach(input => {
            if (input.value) {
                url.searchParams.set(input.name, input.value);
            }
        });

        window.location.href = url.toString();
    }
    
    // Button handling
    const refreshClustersBtn = document.getElementById('refresh-clusters-btn');
    if (refreshClustersBtn) {
        refreshClustersBtn.addEventListener('click', () => {
            window.location.href = '/map/?open_bs_down=true';
        });
    }
    
    // --- Event Delegation for table buttons ---
    const tableBody = mainContainer.querySelector('.reasons-table tbody');
    if (tableBody) {
        tableBody.addEventListener('click', (event) => {
            const target = event.target;
            
            // Кнопкани аниқлаш - icon тагидан ҳам ишлайди
            const buttonElement = target.closest('.btn');
            if (buttonElement) {
                // Кнопка босилган бўлса, модал очмасдан кнопка функцияни ишлатамиз
                event.preventDefault();
                event.stopPropagation();
                handleButtonClicks(buttonElement);
                return;
            }

            // Агар кнопка босилмаган бўлса, қатор учун модални очамиз
            const clickedRow = target.closest('.clickable-row');
            if (clickedRow) {
                openEditModal(clickedRow);
            }
        });
    }

    // Кнопкалар учун алоҳида функция
    function handleButtonClicks(buttonElement) {
        console.log('Button clicked:', buttonElement.className);
        
        // "Показать на карте" тугмаси
        if (buttonElement.classList.contains('view-on-map-btn')) {
            const lat = buttonElement.dataset.lat;
            const lon = buttonElement.dataset.lon;
            console.log('View on map clicked, lat:', lat, 'lon:', lon);
            if (lat && lon) {
                window.open(`/map/?lat=${lat}&lon=${lon}&zoom=14`, '_blank');
            } else {
                alert('Координаты не найдены для этой записи');
            }
            return;
        }

        // "Удалить" тугмаси
        if (buttonElement.classList.contains('delete-reason-btn')) {
            const reasonId = buttonElement.dataset.reasonId;
            console.log('Delete button clicked, reasonId:', reasonId);
            if (confirm('Вы уверены, что хотите удалить эту запись?')) {
                deleteReason(reasonId, buttonElement.closest('tr'));
            }
            return;
        }
    }

    function openEditModal(row) {
        const reasonId = row.dataset.reasonId;
        const reasonRu = row.dataset.reasonRu;
        const reasonEn = row.dataset.reasonEn;
        const stations = row.dataset.stations;
        
        const timestamp = row.querySelector('.timestamp')?.textContent || 'N/A';
        const location = row.querySelector('.location-info')?.textContent || 'N/A';
        const stationCount = row.querySelector('.station-count')?.textContent || 'N/A';
        
        if(modalContext) {
            modalContext.innerHTML = `
                <strong>Время:</strong> ${timestamp}<br>
                <strong>Локация:</strong> ${location}<br>
                <strong>Кол-во БС:</strong> ${stationCount}<br>
                <strong>Станции:</strong> <span style="font-style: italic;">${stations || 'N/A'}</span>
            `;
        }

        editReasonIdInput.value = reasonId;
        
        if (isAdmin) {
            modalBody.innerHTML = `
                <div class="mb-3">
                    <label for="edit-reason-text-ru" class="form-label">Причина аварии (рус)</label>
                    <textarea class="form-control" id="edit-reason-text-ru" required>${reasonRu || ''}</textarea>
                </div>
                <div class="mb-3">
                    <label for="edit-reason-text-en" class="form-label">Reason (eng)</label>
                    <textarea class="form-control" id="edit-reason-text-en">${reasonEn || ''}</textarea>
                </div>
            `;
        } else {
            const reasonText = currentLang === 'en' ? (reasonEn || reasonRu) : reasonRu;
            modalBody.innerHTML = `
                <div class="mb-3">
                    <label for="edit-reason-text" class="form-label">Причина аварии</label>
                    <textarea class="form-control" id="edit-reason-text" required>${reasonText || ''}</textarea>
                </div>
            `;
        }
        reasonModal.show();
    }
    
    // Form submission
    if(editForm) {
        editForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const reasonId = editReasonIdInput.value;
            let reasonData = {};

            try {
                if (isAdmin) {
                    const reasonRu = document.getElementById('edit-reason-text-ru').value.trim();
                    const reasonEn = document.getElementById('edit-reason-text-en').value.trim();
                    if (!reasonRu && !reasonEn) {
                        alert('Пожалуйста, введите причину хотя бы на одном языке.'); return;
                    }
                    reasonData = { ru: reasonRu, en: reasonEn };
                    await updateReason(reasonId, reasonData);
                } else {
                    const reasonText = document.getElementById('edit-reason-text').value.trim();
                    if (!reasonText) {
                        alert('Пожалуйста, введите причину.'); return;
                    }
                    reasonData[currentLang] = reasonText;
                    await updateReason(reasonId, reasonData);
                }
                
                reasonModal.hide();
                updateTableRow(reasonId, reasonData);
                alert('Причина успешно обновлена.');

            } catch (error) {
                console.error('Error:', error);
                alert(`Произошла ошибка при обновлении: ${error.message}`);
            }
        });
    }

    function updateReason(id, reasonData) {
        // reasonData объекти {ru: '...', en: '...'} ko'rinishida bo'ladi
        return fetch(`/map/rcmu/api/update-reason/${id}/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({ reason_data: reasonData }) // Backend'ga moslashtirish kerak
        }).then(response => {
            if (!response.ok) throw new Error('Network response was not ok.');
            return response.json();
        });
    }

    function updateTableRow(reasonId, reasonData) {
        const row = document.querySelector(`tr[data-reason-id='${reasonId}']`);
        if (row) {
            const reasonCell = row.querySelector('.reason-text');
            const newReasonText = reasonData[currentLang] || reasonData.ru || reasonData.en || '';
            
            if (newReasonText) {
                reasonCell.innerHTML = newReasonText;
                reasonCell.title = newReasonText;
                reasonCell.classList.remove('text-muted', 'fst-italic');
            } else {
                reasonCell.innerHTML = `<span class="text-muted fst-italic">Причина не указана...</span>`;
                reasonCell.title = 'Причина не указана';
            }
            
            // data-atributlarni ham yangilaymiz
            if(reasonData.ru) row.dataset.reasonRu = reasonData.ru;
            if(reasonData.en) row.dataset.reasonEn = reasonData.en;
        }
    }

    async function deleteReason(id, row) {
        try {
            const response = await fetch(`/map/rcmu/api/delete-reason/${id}/`, {
                method: 'DELETE',
                headers: { 'X-CSRFToken': getCookie('csrftoken') }
            });
            const data = await response.json();
            if (data.success) {
                alert('Причина успешно удалена');
                row.remove(); // Удаляем строку из таблицы
            } else {
                alert('Ошибка: ' + data.error);
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Произошла ошибка при удалении');
        }
    }

    // Add this new function for polling and updating row styles
    let currentOfflineBS = new Set(); // To store current offline BS names/numbers

    async function fetchCurrentOfflineBS() {
        try {
            const response = await fetch('/map/api/map-data/');
            const data = await response.json();
            currentOfflineBS.clear();
            data.points.forEach(point => {
                if (point.status === true) {
                    currentOfflineBS.add(point.bsName || point.name);
                }
            });
            updateRowStyles();
        } catch (error) {
            console.error('Error fetching map data:', error);
        }
    }

    function updateRowStyles() {
        const rows = document.querySelectorAll('.reasons-table tbody tr');
        rows.forEach(row => {
            const stationsStr = row.dataset.stations || '';
            const stations = stationsStr.split(',').map(s => s.trim()).filter(s => s);
            const checkbox = row.querySelector('.row-checkbox');
            
            let offlineCount = 0;
            stations.forEach(station => {
                if (currentOfflineBS.has(station)) {
                    offlineCount++;
                }
            });
            
            // Remove previous style classes
            row.classList.remove('table-danger', 'table-secondary');
            
            // Apply new class and manage checkbox state
            if (offlineCount >= 3) {
                row.classList.add('table-danger'); // Active: red
                if (checkbox) {
                    checkbox.checked = false; // Uncheck if it becomes active
                    checkbox.disabled = true;
                }
            } else {
                row.classList.add('table-secondary'); // Inactive: gray
                if (checkbox) {
                    checkbox.disabled = false;
                }
            }
        });
        
        updateDeleteButtonState(); // Update button after styles
        sortTableRows(); // Sort rows after styling
    }

    function sortTableRows() {
        const tbody = document.querySelector('.reasons-table tbody');
        if (!tbody) return;
        
        const allRows = Array.from(tbody.querySelectorAll('tr'));
        
        // Separate active and inactive
        const activeRows = allRows.filter(row => row.classList.contains('table-danger'));
        const inactiveRows = allRows.filter(row => !row.classList.contains('table-danger'));
        
        // Append active first, then inactive
        tbody.innerHTML = ''; // Clear
        activeRows.forEach(row => tbody.appendChild(row));
        inactiveRows.forEach(row => tbody.appendChild(row));
    }

    // --- NEW SELECTION AND DELETION LOGIC ---
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const rowCheckboxes = document.querySelectorAll('.row-checkbox');
    const deleteSelectedBtn = document.getElementById('delete-selected-reasons-btn');

    function updateDeleteButtonState() {
        if (!deleteSelectedBtn) return;
        const selectedCount = document.querySelectorAll('.row-checkbox:checked').length;
        if (selectedCount > 0) {
            deleteSelectedBtn.style.display = 'inline-block';
            deleteSelectedBtn.textContent = `Удалить выбранное (${selectedCount})`;
        } else {
            deleteSelectedBtn.style.display = 'none';
        }
    }

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', (e) => {
            const isChecked = e.target.checked;
            document.querySelectorAll('.row-checkbox:not(:disabled)').forEach(checkbox => {
                checkbox.checked = isChecked;
            });
            updateDeleteButtonState();
        });
    }

    if (rowCheckboxes) {
        rowCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                const totalEnabled = document.querySelectorAll('.row-checkbox:not(:disabled)').length;
                const totalChecked = document.querySelectorAll('.row-checkbox:checked').length;
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = totalEnabled > 0 && totalEnabled === totalChecked;
                }
                updateDeleteButtonState();
            });
        });
    }

    if (deleteSelectedBtn) {
        deleteSelectedBtn.addEventListener('click', async () => {
            const selectedIds = Array.from(document.querySelectorAll('.row-checkbox:checked'))
                                     .map(cb => cb.dataset.reasonId);
            
            if (selectedIds.length === 0) {
                alert('Пожалуйста, выберите хотя бы одну запись для удаления.');
                return;
            }

            const confirmation = confirm(`Вы уверены, что хотите удалить ${selectedIds.length} записей?`);
            if (confirmation) {
                await deleteSelectedReasons(selectedIds);
            }
        });
    }

    async function deleteSelectedReasons(ids) {
        try {
            const response = await fetch('/map/rcmu/api/reasons/delete_selected/', {
                method: 'POST', // Using POST to send a body with IDs
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({ ids: ids })
            });

            const data = await response.json();

            if (response.ok) {
                alert(`Успешно удалено: ${data.deleted_count} записей.`);
                window.location.reload();
            } else {
                throw new Error(data.error || 'Не удалось выполнить удаление.');
            }
        } catch (error) {
            console.error('Error deleting selected reasons:', error);
            alert(`Произошла ошибка: ${error.message}`);
        }
    }
    
    // Start polling
    fetchCurrentOfflineBS(); // Initial fetch
    setInterval(fetchCurrentOfflineBS, 30000); // Every 30 seconds

    // Force cursor style after everything else has loaded
    window.addEventListener('load', () => {
        const rows = document.querySelectorAll('.clickable-row');
        rows.forEach(row => {
            const cells = row.getElementsByTagName('td');
            for(let cell of cells) {
                // Кнопкалар учун курсор ўзгартирмаймиз
                if (!cell.querySelector('.btn')) {
                    cell.style.cursor = 'pointer';
                }
            }
        });
    });
}); 