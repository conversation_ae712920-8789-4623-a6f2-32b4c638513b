from django.urls import path, include
from . import views
from .geocoding import geocode_proxy, search_suggestions

app_name = 'maps'

urlpatterns = [
    # Асосий карта кўриниши
    path('', views.map_view, name='map'),

    # API эндпоинтлар
    path('api/map-data/', views.get_map_data, name='get_map_data'),
    path('api/regions/', views.get_all_regions, name='get_all_regions'),
    path('api/areas/', views.get_all_areas, name='get_all_areas'),
    path('api/areas/<int:region_id>/', views.get_areas_by_region, name='get_areas_by_region'),
    path('api/historical-alarms/', views.get_historical_alarms, name='get_historical_alarms'),

    # Геокодинг прокси эндпоинтлар
    path('api/geocode/', geocode_proxy, name='geocode_proxy'),
    path('api/search-suggestions/', search_suggestions, name='search_suggestions'),

    # Outage Reasons API
    path('api/save-outage-reason/', views.save_outage_reason, name='save_outage_reason'),
    path('api/outage-reasons/', views.get_outage_reasons, name='get_outage_reasons'),

    # Alarms модули URL лари
    path('alarms/', include('maps.alarms.urls')),

    # РЦМУ URL лари
    path('rcmu/', include('maps.rcmu_urls', namespace='rcmu')),
]