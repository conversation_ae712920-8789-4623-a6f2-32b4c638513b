/* Карта қидирув элементлари */

/* Управление картой */
.map-type-control {
    position: absolute;
    top: 80px;
    right: 10px;
    z-index: 1001;
    background: white;
    border-radius: 4px;
    padding: 3px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: row;
    gap: 3px;
    align-items: center;
}

/* Строка элементов управления */
.map-controls-row {
    display: flex;
    gap: 3px;
    align-items: center;
}

/* Контейнер поиска */
.search-container {
    position: relative;
    display: flex;
    align-items: center;
    min-width: 200px;
}

/* Поле поиска */
.search-input {
    flex: 1;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
    font-size: 13px;
    outline: none;
    transition: border-color 0.3s;
    height: 36px;
    box-sizing: border-box;
}

.search-input:focus {
    border-color: #4285F4;
}

/* Кнопка поиска */
.search-button {
    padding: 6px 10px;
    background: white;
    color: #555;
    border: 1px solid #ddd;
    border-left: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-button:hover {
    background: #f4f4f4;
}

.search-button:disabled {
    background: #f4f4f4;
    cursor: not-allowed;
    opacity: 0.6;
}

/* Результаты поиска */
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 0 0 4px 4px;
    max-height: 250px;
    overflow-y: auto;
    z-index: 1500;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin-top: -1px;
    animation: slideDown 0.2s ease-out;
    user-select: none;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.search-result-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    font-size: 13px;
    position: relative;
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.search-result-item:hover {
    background-color: #f8f8f8;
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background: transparent;
    transition: background 0.2s ease;
}

.search-result-item:hover::before {
    background: #2196F3;
}

/* Вилоят ва туман натижалари учун */
.search-result-item[data-type="region"],
.search-result-item[data-type="area"] {
    background: linear-gradient(to right, rgba(156, 39, 176, 0.05), transparent);
}

/* Жойлашув натижалари учун */
.search-result-item[data-type="location"] {
    background: linear-gradient(to right, rgba(76, 175, 80, 0.05), transparent);
}

/* Қидирув майдони фокусда бўлганда */
#location-search:focus {
    border-color: #2196F3;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

/* Қидирув тугмаси анимацияси */
#search-button:not(:disabled):hover {
    background-color: #1976D2;
    transform: scale(1.05);
}

#search-button:not(:disabled):active {
    transform: scale(0.95);
}

/* Юкланиш индикатори */
#search-button .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Узун матнлар учун */
.search-result-item {
    position: relative;
    overflow: visible !important;
}

.search-result-item > div:last-child {
    padding-right: 10px;
}

/* Матн эллипсис */
.search-result-item .text-ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

/* Hover да тўлиқ матнни кўрсатиш */
.search-result-item:hover {
    z-index: 10;
}

.search-result-item:hover::after {
    content: attr(data-full-text);
    position: absolute;
    left: 0;
    top: 100%;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: normal;
    max-width: 400px;
    z-index: 1000;
    pointer-events: none;
    display: none;
}

.search-result-item[data-full-text]:hover::after {
    display: block;
}

/* Қидирув контейнери */
.search-container {
    position: relative;
}

.search-container .search-results {
    position: absolute !important;
    word-wrap: break-word;
    word-break: break-word;
}

/* Ўзбекистонга қайтиш тугмаси */
.return-to-uzbekistan-btn {
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    margin-left: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.return-to-uzbekistan-btn:hover {
    background: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.return-to-uzbekistan-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.return-to-uzbekistan-btn i {
    font-size: 18px;
} 