/* Popup тугмалари ва амаллар */

.bs-down-zone-popup .popup-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.bs-down-zone-popup .popup-actions button {
    flex: 1;
    padding: 8px 12px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: background 0.2s;
}

.bs-down-zone-popup .popup-actions button:hover {
    background: #0056b3;
}

.bs-down-zone-popup .popup-actions .btn-edit-reason {
    background: #28a745;
}

.bs-down-zone-popup .popup-actions .btn-edit-reason:hover {
    background: #218838;
}

.bs-down-zone-popup .popup-actions .btn-manage-all {
    background: #ffc107;
    color: #333;
}

.bs-down-zone-popup .popup-actions .btn-manage-all:hover {
    background: #e0a800;
}

/* Reason формаси */
.bs-down-zone-popup .reason-form {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin: 15px 0;
    border: 1px solid #dee2e6;
}

.bs-down-zone-popup .reason-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 13px;
}

.bs-down-zone-popup .reason-form textarea {
    width: 100%;
    min-height: 80px;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 13px;
    line-height: 1.4;
    font-family: inherit;
    resize: vertical;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    box-sizing: border-box;
}

.bs-down-zone-popup .reason-form textarea:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.bs-down-zone-popup .reason-form .form-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
}

.bs-down-zone-popup .reason-form .btn-save {
    background: #28a745;
    color: white;
}

.bs-down-zone-popup .reason-form .btn-save:hover {
    background: #218838;
}

.bs-down-zone-popup .reason-form .btn-cancel {
    background: #6c757d;
    color: white;
}

.bs-down-zone-popup .reason-form .btn-cancel:hover {
    background: #5a6268;
} 