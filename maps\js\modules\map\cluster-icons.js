// Кластер иконкалари модули - Cluster icons and styling

// Кластер иконка яратиш - филиал/вилоят/туман номини кўрсатиш билан
window.createCustomClusterIcon = function(cluster) {
    const childCount = cluster.getChildCount();
    const markers = cluster.getAllChildMarkers();
    
    // Аварияли БС лар сонини ҳисоблаш
    const offlineCount = markers.filter(marker => {
        const pointData = marker.options.pointData;
        return pointData.status === true || pointData.status === 'true' || 
               pointData.status === 1 || pointData.status === '1';
    }).length;
    
    // Кластер номини аниқлаш
    let clusterName = '';
    let iconClass = 'cluster-icon';
    
    if (window.currentClusterMode === 'branches') {
        // Филиал номини аниқлаш
        const branchKey = window.getBranchForCluster(markers);
        
        if (branchKey && window.branches[branchKey]) {
            const branchData = window.branches[branchKey];
            
            // Жорий тилга қараб филиал номини танлаш
            if (window.currentLanguage === 'en') {
                clusterName = branchData.nameEn;
            } else {
                clusterName = branchData.shortName;
            }
        } else {
            clusterName = childCount.toString(); // Fallback
        }
        iconClass += ' cluster-branch';
    } else if (window.currentClusterMode === 'regions') {
        // Вилоят номини аниқлаш
        const regionName = window.getRegionForCluster(markers);
        
        if (regionName) {
            // Инглиз тилида вилоят номини таржима қилиш
            if (window.currentLanguage === 'en' && window.translateRegionName) {
                const translatedName = window.translateRegionName(regionName);
                clusterName = window.getShortRegionName(translatedName);
            } else {
                clusterName = window.getShortRegionName(regionName);
            }
        } else {
            clusterName = childCount.toString(); // Fallback
        }
        iconClass += ' cluster-region';
    } else if (window.currentClusterMode === 'areas') {
        // Туман номини аниқлаш
        const areaName = window.getAreaForCluster(markers);
        
        if (areaName) {
            // Инглиз тилида туман номини таржима қилиш
            if (window.currentLanguage === 'en' && window.translateAreaName) {
                const translatedName = window.translateAreaName(areaName);
                clusterName = window.getShortAreaName(translatedName);
            } else {
                clusterName = window.getShortAreaName(areaName);
            }
        } else {
            clusterName = childCount.toString(); // Fallback
        }
        iconClass += ' cluster-area';
    }
    
    // Авария мавжудлигига қараб ранг қўшиш
    if (offlineCount > 0) {
        iconClass += ' cluster-has-offline';
    }

    // Кластер ўлчамини белгилаш
    let iconSize = new L.Point(40, 40);
    if (window.currentClusterMode === 'branches') {
        iconSize = new L.Point(70, 70);
    } else if (window.currentClusterMode === 'regions') {
        iconSize = new L.Point(55, 55);
    }

    return new L.DivIcon({
        html: `<div class="${iconClass}">
                   <div class="cluster-name">${clusterName}</div>
                   <span class="cluster-count">${childCount}</span>
                   ${offlineCount > 0 ? `<span class="cluster-offline-count">${offlineCount}</span>` : ''}
               </div>`,
        className: 'custom-cluster-icon',
        iconSize: iconSize
    });
}

// Логик кластер маркери яратиш
window.createLogicalClusterMarker = function(markers, data, type) {
    
    // Марказий координатани ҳисоблаш ва координаталарни текшириш
    let centerLat = 0, centerLng = 0;
    let validMarkers = 0;
    
    markers.forEach((marker, index) => {
        const latlng = marker.getLatLng();
        
        if (latlng && !isNaN(latlng.lat) && !isNaN(latlng.lng)) {
            centerLat += latlng.lat;
            centerLng += latlng.lng;
            validMarkers++;
        }
    });
    
    if (validMarkers === 0) {
        return null;
    }
    
    centerLat /= validMarkers;
    centerLng /= validMarkers;
    
    // Аварияли БС лар сонини ҳисоблаш
    const offlineCount = markers.filter(marker => {
        const pointData = marker.options.pointData;
        return pointData.status === true || pointData.status === 'true' || 
               pointData.status === 1 || pointData.status === '1';
    }).length;
    
    // Кластер номини аниқлаш
    let clusterName = '';
    let iconClass = 'cluster-icon';
    let iconSize = new L.Point(40, 40);
    
    if (type === 'branch') {
        if (window.currentLanguage === 'en') {
            clusterName = data.nameEn || data.shortName;
        } else {
            clusterName = data.shortName || data.name;
        }
        iconClass += ' cluster-branch';
        iconSize = new L.Point(70, 70);
    } else if (type === 'region') {
        clusterName = data.shortName || data.displayName || data.name;
        iconClass += ' cluster-region';
        iconSize = new L.Point(55, 55);
    } else if (type === 'area') {
        clusterName = data.shortName || data.displayName || data.name;
        iconClass += ' cluster-area';
        iconSize = new L.Point(45, 45);
    }
    
    // Авария мавжудлигига қараб ранг қўшиш
    if (offlineCount > 0) {
        iconClass += ' cluster-has-offline';
    }
    
    const clusterIcon = new L.DivIcon({
        html: `<div class="${iconClass}">
                   <div class="cluster-name">${clusterName}</div>
                   <span class="cluster-count">${markers.length}</span>
                   ${offlineCount > 0 ? `<span class="cluster-offline-count">${offlineCount}</span>` : ''}
               </div>`,
        className: 'custom-cluster-icon',
        iconSize: iconSize
    });
    
    const clusterMarker = L.marker([centerLat, centerLng], { icon: clusterIcon });
    
    // Кластер учун tooltip матни яратиш
    let tooltipText = `${clusterName} - ${markers.length} BS`;
    if (offlineCount > 0) {
        tooltipText += ` (${offlineCount} offline)`;
    }
    
    // Ховер tooltip қўшиш - кора фон муаммосини ҳал қилиш
    clusterMarker.bindTooltip(tooltipText, {
        permanent: false,
        direction: 'top',
        className: 'cluster-tooltip',
        offset: [0, -20],
        opacity: 0.9
    });
    
    // Event handler'лар cluster-events.js дан юкланади
    window.addClusterEventHandlers(clusterMarker, markers, centerLat, centerLng, type);
    
    return clusterMarker;
}

// CSS стиллар алоҳида cluster.css файлидан юкланади 