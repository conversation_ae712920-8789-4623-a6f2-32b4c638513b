// Off Stations Panel утилита функциялари
(function() {
    'use strict';
    
    // Конфигурациядан маълумотларни олиш
    const { EARTH_RADIUS_METERS } = window.OffStationsConfig;

    // Икки нуқта орасидаги масофани ҳисоблаш (метрларда)
    function calculateDistance(lat1, lon1, lat2, lon2) {
        const φ1 = lat1 * Math.PI / 180;
        const φ2 = lat2 * Math.PI / 180;
        const Δφ = (lat2 - lat1) * Math.PI / 180;
        const Δλ = (lon2 - lon1) * Math.PI / 180;

        const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return EARTH_RADIUS_METERS * c; // метрларда
    }

    // Группа марказини ҳисоблаш
    function calculateGroupCenter(stations) {
        let totalLat = 0;
        let totalLon = 0;

        stations.forEach(station => {
            totalLat += parseFloat(station.lat);
            totalLon += parseFloat(station.lon);
        });

        return {
            lat: totalLat / stations.length,
            lon: totalLon / stations.length
        };
    }

    // Группа жойини аниқлаш
    function getGroupArea(stations) {
        // Кластердаги барча районларни йиғиш
        const areas = new Set();
        const regions = new Set();
        
        stations.forEach(station => {
            const areaName = station.area_name || station.area || 'Номаълум жой';
            const regionName = station.region_name || station.region || '';
            
            areas.add(areaName);
            regions.add(regionName);
        });

        const lang = window.currentLanguage || 'ru';
        
        // Районлар номларини бирлаштириш
        let areaText = Array.from(areas).join(', ');
        let regionText = Array.from(regions).join(', ');

        // Инглиз тилида таржима қилиш
        if (lang === 'en') {
            if (areaText.includes('Номаълум жой')) {
                areaText = areaText.replace(/Номаълум жой/g, 'Unknown location');
            }
            // Район номларини таржима қилиш (агар функция мавжуд бўлса)
            if (window.translateAreaName) {
                const translatedAreas = Array.from(areas).map(area => 
                    area === 'Номаълум жой' ? 'Unknown location' : window.translateAreaName(area)
                );
                areaText = translatedAreas.join(', ');
            }
            if (window.translateRegionName) {
                const translatedRegions = Array.from(regions).map(region => 
                    window.translateRegionName(region)
                );
                regionText = translatedRegions.join(', ');
            }
        }

        return {
            area: areaText,
            region: regionText,
            areaCount: areas.size,
            regionCount: regions.size
        };
    }

    // CSRF токен олиш
    function getCsrfToken() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return value;
            }
        }
        return '';
    }

    // Тилга қараб матнларни олиш
    function getTranslations() {
        const lang = window.currentLanguage || 'ru';
        const translations = window.translations && window.translations[lang] ? window.translations[lang] : {};
        
        return {
            title: lang === 'en' ? 'BS Down Zones ≥ 3 count' : 'Зоны БС ≥ 3 шт',
            emptyMessage: lang === 'en' ? 'No incidents currently' : 'Пока нет аварий',
            downtime: lang === 'en' ? 'Downtime' : 'Время аварии',
            hours: translations.hours || (lang === 'en' ? 'hours' : 'часов'),
            reasonLabel: translations.reason ? translations.reason + ': ' : (lang === 'en' ? 'Reason: ' : 'Причина: '),
            edit_reason: translations.edit_reason || (lang === 'en' ? 'Edit Reason' : 'Изменить причину'),
            add_reason: translations.add_reason || (lang === 'en' ? 'Add Reason' : 'Добавить причину'),
            viewLocation: lang === 'en' ? 'View Location' : 'Показать на карте',
            bs_count: translations.bs_count || (lang === 'en' ? 'base stations' : 'БС'),
            neighborBs: lang === 'en' ? 'Neighbor BS stations' : 'Соседние БС',
            and: lang === 'en' ? 'and' : 'и еще',
            newZoneDetected: lang === 'en' ? 'New BS Down Zone Detected' : 'Обнаружена новая зона аварий БС',
            modalTitle: lang === 'en' ? 'Add Reason for Outage' : 'Добавить причину аварии',
            location: lang === 'en' ? 'Location:' : 'Местоположение:',
            affectedStations: lang === 'en' ? 'Affected stations:' : 'Затронутые станции:',
            reasonPlaceholder: translations.enter_reason || (lang === 'en' ? 'Enter the reason for this outage...' : 'Введите причину данной аварии...'),
            cancel: translations.cancel || (lang === 'en' ? 'Cancel' : 'Отмена'),
            saveReason: translations.save || (lang === 'en' ? 'Save Reason' : 'Сохранить причину'),
            pleaseEnterReason: lang === 'en' ? 'Please enter a reason' : 'Пожалуйста, введите причину',
            bsDownZone: translations.bs_down_zone || (lang === 'en' ? 'BS Down Zone' : 'Зона БС'),
            districts: translations.districts || (lang === 'en' ? 'districts' : 'районов'),
            reason_specified: translations.reason_specified || (lang === 'en' ? 'Reason specified' : 'Причина указана'),
            reason: translations.reason || (lang === 'en' ? 'Reason' : 'Причина'),
            noReason: translations.no_reason || (lang === 'en' ? 'No reason specified' : 'Причина не указана'),
            enterReason: translations.enter_reason || (lang === 'en' ? 'Enter reason...' : 'Введите причину...'),
            save: translations.save || (lang === 'en' ? 'Save' : 'Сохранить'),
            manage_reasons: translations.manage_reasons || (lang === 'en' ? 'Manage reasons' : 'Все причины')
        };
    }

    // Группа ID яратиш
    function generateGroupId(group) {
        return `group-${Math.round(group.center.lat * 1000)}-${Math.round(group.center.lon * 1000)}-${group.stations.length}`;
    }

    // Нотификация учун хабар матни яратиш
    function createNotificationMessage(group, lang) {
        const stationCount = group.stations.length;
        const area = group.area.area;
        const region = group.area.region;

        if (lang === 'en') {
            return `${stationCount} base stations are down in ${area}, ${region}`;
        } else {
            return `${stationCount} базовых станций не работают в ${area}, ${region}`;
        }
    }
    
    // Глобал объектга экспорт қилиш
    window.OffStationsUtils = {
        calculateDistance: calculateDistance,
        calculateGroupCenter: calculateGroupCenter,
        getGroupArea: getGroupArea,
        getCsrfToken: getCsrfToken,
        getTranslations: getTranslations,
        generateGroupId: generateGroupId,
        createNotificationMessage: createNotificationMessage
    };
})(); 