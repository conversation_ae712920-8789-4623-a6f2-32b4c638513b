// Off Stations Panel конфигурацияси
(function() {
    'use strict';
    
    // Конфигурация объекти
    const CONFIG = {
        // Минимал БС сони - камида 3 та БС тушса кўрсатамиз
        MIN_STATIONS_TO_SHOW: 3,
        
        // Янгиланиш интервали - 10 секунд
        UPDATE_INTERVAL: 10000,
        
        // Панел позицияси
        PANEL_POSITION: {
            left: 280, // sidebar дан кейин
            top: 80
        },
        
        // Хабарлар конфигурацияси олиб ташланди
        
        // Координаталар аниқлиги
        COORDINATE_TOLERANCE: 0.001, // тахминан 100 метр
        
        // Локал сақлаш калитлари
        STORAGE_KEYS: {
            PANEL_VISIBLE: 'offStationsPanelVisible',
            REASON_PREFIX: 'reason_',
            RCMU_PERMISSION: 'rcmuPermission'
        },
        
        // CSS класслари
        CSS_CLASSES: {
            PANEL: 'off-stations-panel',
            PANEL_VISIBLE: 'visible',
            TOGGLE_ACTIVE: 'active',
            UPDATE_PAUSED: 'paused',
            NEW_ITEM: 'new-item pulse'
        },
        
        // DOM элемент ID лари
        DOM_IDS: {
            PANEL: 'off-stations-panel',
            PANEL_CONTENT: 'panel-content',
            PANEL_TITLE: 'panel-title',
            EMPTY_MESSAGE: 'empty-message',
            TOTAL_COUNT: 'total-off-count',
            GROUPS_CONTAINER: 'station-groups-container',
            UPDATE_INDICATOR: 'update-indicator',
            REASON_MODAL: 'reason-modal-overlay'
        }
    };

    // Рухсат берилган роллар
    const ALLOWED_ROLES = {
        IDS: [1, 2, 10], // Администратор, Оператор, rcmu
        NAMES: ['РЦМУ', 'RCMU', 'Админ', 'Admin', 'Администратор', 'Оператор', 'rcmu'] // рухсатли роллар
    };

    // Ер радиуси (метрларда)
    const EARTH_RADIUS_METERS = 6371e3;
    
    // Глобал объектга экспорт қилиш
    window.OffStationsConfig = {
        CONFIG: CONFIG,
        ALLOWED_ROLES: ALLOWED_ROLES,
        EARTH_RADIUS_METERS: EARTH_RADIUS_METERS
    };
})(); 