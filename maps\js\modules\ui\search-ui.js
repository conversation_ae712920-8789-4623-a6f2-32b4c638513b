// Модуль работы с UI поиска

// Показать результаты поиска
function showSearchResults(results) {
    hideSearchResults(); // Сначала скрываем предыдущие результаты

    const searchInput = document.getElementById('location-search');
    const container = searchInput.parentElement;
    
    const resultsDiv = document.createElement('div');
    resultsDiv.className = 'search-results';
    
    // Контейнер позициясини олиш
    const containerRect = container.getBoundingClientRect();
    const windowWidth = window.innerWidth;
    const maxWidth = Math.min(500, windowWidth - containerRect.left - 20); // 20px padding
    
    resultsDiv.style.cssText = `
        position: absolute;
        top: 100%;
        left: 0;
        right: auto;
        min-width: ${Math.min(350, maxWidth)}px;
        max-width: ${maxWidth}px;
        width: max-content;
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        max-height: 400px;
        overflow-y: auto;
        overflow-x: hidden;
        z-index: 1000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        margin-top: 4px;
    `;

    // Натижаларни турлари бўйича гуруҳлаш
    const groupedResults = {
        region: [],
        area: [],
        location: [],
        suggestion: []
    };

    results.forEach(result => {
        const type = result.type || 'suggestion';
        if (!groupedResults[type]) {
            groupedResults[type] = [];
        }
        groupedResults[type].push(result);
    });

    // Ҳар бир гуруҳ учун натижаларни кўрсатиш
    Object.entries(groupedResults).forEach(([type, items]) => {
        if (items.length === 0) return;

        // Гуруҳ контейнери
        const groupDiv = document.createElement('div');
        groupDiv.style.cssText = 'margin-bottom: 8px;';
        
        // Гуруҳ сарлавҳаси қўшиш
        const headerDiv = createGroupHeader(type);
        groupDiv.appendChild(headerDiv);

        // Гуруҳ элементлари
        items.forEach((result, index) => {
            const item = createResultItem(result, type);
            groupDiv.appendChild(item);
        });

        resultsDiv.appendChild(groupDiv);
    });

    // Агар ҳеч қандай натижа бўлмаса
    if (results.length === 0) {
        const noResults = createNoResultsElement();
        resultsDiv.appendChild(noResults);
    }

    container.style.position = 'relative';
    container.appendChild(resultsDiv);
    
    // Results контейнерига mousedown қўшиш
    resultsDiv.addEventListener('mousedown', (e) => {
        e.preventDefault(); // Focus йўқолишини олдини олиш
    });

    // Закрытие результатов при клике вне
    setTimeout(() => {
        document.addEventListener('click', handleOutsideClick);
    }, 100);
}

// Гуруҳ сарлавҳасини яратиш
function createGroupHeader(type) {
    let groupTitle = '';
    let groupIcon = '';
    
    const currentLang = window.currentLanguage || 'ru';
    const trans = window.translations[currentLang];
    
    switch(type) {
        case 'region':
            groupTitle = trans['regions'] || 'Regions';
            groupIcon = 'fa-map';
            break;
        case 'area':
            groupTitle = trans['districts'] || 'Districts';
            groupIcon = 'fa-map-marked-alt';
            break;
        case 'location':
            groupTitle = trans['places'] || 'Places';
            groupIcon = 'fa-map-marker-alt';
            break;
        default:
            groupTitle = trans['suggestions'] || 'Suggestions';
            groupIcon = 'fa-search';
    }

    const headerDiv = document.createElement('div');
    headerDiv.style.cssText = `
        padding: 8px 12px;
        background: #f8f9fa;
        font-size: 12px;
        font-weight: 600;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-bottom: 1px solid #e9ecef;
    `;
    headerDiv.innerHTML = `<i class="fas ${groupIcon}" style="margin-right: 6px;"></i>${groupTitle}`;
    
    return headerDiv;
}

// Натижа элементини яратиш
function createResultItem(result, type) {
    const item = document.createElement('div');
    item.className = 'search-result-item';
    item.style.cssText = `
        padding: 12px 16px;
        cursor: pointer;
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 12px;
    `;
    
    // Иконка
    const iconDiv = createResultIcon(type);
    
    // Матн қисми
    const textDiv = createResultText(result);
    
    // Масофа (агар мавжуд бўлса)
    if (result.distance) {
        const distanceDiv = document.createElement('div');
        distanceDiv.style.cssText = `
            font-size: 12px;
            color: #6c757d;
            flex-shrink: 0;
        `;
        distanceDiv.textContent = `${result.distance} км`;
        item.appendChild(distanceDiv);
    }
    
    item.appendChild(iconDiv);
    item.appendChild(textDiv);

    // Ҳовер эффектларини қўшиш
    addHoverEffects(item);
    
    // Клик ҳандлер қўшиш
    addClickHandler(item, result, textDiv);

    return item;
}

// Натижа иконкасини яратиш
function createResultIcon(type) {
    const iconDiv = document.createElement('div');
    iconDiv.style.cssText = `
        width: 36px;
        height: 36px;
        background: ${type === 'region' || type === 'area' ? '#f3e5f5' : '#e8f5e9'};
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    `;
    
    let icon = '';
    switch(type) {
        case 'region':
        case 'area':
            icon = '<i class="fas fa-map-marked-alt" style="color: #7b1fa2;"></i>';
            break;
        default:
            icon = '<i class="fas fa-map-marker-alt" style="color: #388e3c;"></i>';
    }
    iconDiv.innerHTML = icon;
    
    return iconDiv;
}

// Натижа матнини яратиш
function createResultText(result) {
    const textDiv = document.createElement('div');
    textDiv.style.cssText = 'flex: 1; min-width: 0;';
    
    // Асосий матн
    const mainText = document.createElement('div');
    mainText.style.cssText = `
        font-weight: 500;
        color: #212529;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    `;
    mainText.textContent = result.display_name ? result.display_name.split(',')[0] : result.text.split(',')[0];
    
    // Қўшимча маълумот
    const subText = document.createElement('div');
    subText.style.cssText = `
        font-size: 12px;
        color: #6c757d;
        margin-top: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    `;
    
    if (result.display_name) {
        const parts = result.display_name.split(',').slice(1);
        subText.textContent = parts.join(',').trim();
    } else if (result.text.includes(',')) {
        subText.textContent = result.text.split(',').slice(1).join(',').trim();
    }
    
    textDiv.appendChild(mainText);
    if (subText.textContent) {
        textDiv.appendChild(subText);
    }
    
    return textDiv;
}

// Ҳовер эффектларини қўшиш
function addHoverEffects(item) {
    item.addEventListener('mouseenter', () => {
        item.style.backgroundColor = '#f8f9fa';
        item.style.paddingLeft = '20px';
    });

    item.addEventListener('mouseleave', () => {
        item.style.backgroundColor = 'white';
        item.style.paddingLeft = '16px';
    });
}

// Клик ҳандлерини қўшиш
function addClickHandler(item, result, textDiv) {
    item.addEventListener('mousedown', (e) => {
        // mousedown ишлатамиз, чунки у blur дан олдин ишлайди
        e.preventDefault(); // Blur eventni тўхтатиш
        e.stopPropagation();
        
        console.log('Search result clicked:', result);
        console.log('Lat:', result.lat, 'Lon:', result.lon);
        
        // Ҳамма ҳолатда картани марказлаштириш
        if (result.lat && result.lon) {
            window.SearchMarker.displaySearchResult(result.lat, result.lon, result.text || result.display_name);
        } else {
            console.error('No coordinates found for result:', result);
        }
        
        // Қўшимча амаллар
        handleSpecialResultTypes(result);
        
        // Input га қиймат қўйиш
        const searchInput = document.getElementById('location-search');
        const mainText = textDiv.querySelector('div:first-child');
        searchInput.value = mainText.textContent;
        
        // Натижаларни яшириш
        setTimeout(() => {
            hideSearchResults();
        }, 50);
    });
}

// Махсус натижа турлари билан ишлаш
function handleSpecialResultTypes(result) {
    if (result.type === 'region' && result.region_id) {
        // Вилоятни танлаш ва фильтрлаш
        const regionSelect = document.getElementById('region');
        if (regionSelect) {
            regionSelect.value = result.region_id;
            regionSelect.dispatchEvent(new Event('change', { bubbles: true }));
        }
    } else if (result.type === 'area' && result.area_id) {
        // Туман учун ҳам марказлаштириш ишлайди
        // Келажакда туман фильтрини қўшиш мумкин
    }
}

// "Натижа топилмади" элементини яратиш
function createNoResultsElement() {
    const currentLang = window.currentLanguage || 'ru';
    const trans = window.translations[currentLang];
    const noResultsText = trans['nothing_found'] || 'Nothing found';
    
    const noResults = document.createElement('div');
    noResults.style.cssText = `
        padding: 20px;
        text-align: center;
        color: #6c757d;
        font-size: 14px;
    `;
    noResults.innerHTML = `<i class="fas fa-search" style="margin-right: 8px;"></i>${noResultsText}`;
    
    return noResults;
}

// Скрыть результаты поиска
function hideSearchResults() {
    const existingResults = document.querySelector('.search-results');
    if (existingResults) {
        existingResults.remove();
    }
    document.removeEventListener('click', handleOutsideClick);
}

// Обработчик клика вне результатов
function handleOutsideClick(e) {
    if (!e.target.closest('.search-container')) {
        hideSearchResults();
    }
}

// Экспорт функций
window.SearchUI = {
    showSearchResults,
    hideSearchResults,
    handleOutsideClick
}; 