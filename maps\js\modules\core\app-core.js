// Асосий модуль - Глобал ўзгарувчилар ва инициализация

// Глобальные переменные карты
window.mymap = null;
window.points = [];
window.globalStations = [];
window.globalRegions = [];

// Фойдаланувчи роли маълумотлари
window.userPrivilege = null;

// Автоматик марказлаштириш ҳолати
window.autoCenterEnabled = true; // Бошланғич ҳолат - ёқилган

// Переменные для слоев карты
// currentMapLayer, currentSatelliteLayer, isMapMode и isYandexMode объявлены в модуле map-layers.js

// Яндекс карта
// yandexMap, yandexMapContainer, isYandexAPILoaded объявлены в модуле map-layers.js
let yandexGeocoder = null;
// yandexMapLayer и yandexSatelliteLayer объявлены в модуле map-layers.js

// OSM и Google слои объявлены в модуле map-layers.js

// Функция для получения высоты navbar
function getNavbarHeight() {
    const navbar = document.querySelector('.navbar');
    return navbar ? navbar.offsetHeight : 60; // Аниқ 60px
}

// Главная функция инициализации
document.addEventListener('DOMContentLoaded', function () {
    // Фойдаланувчи роли маълумотларини юклаш
    const userPrivilegeData = document.getElementById('user-privilege');
    if (userPrivilegeData) {
        window.userPrivilege = JSON.parse(userPrivilegeData.textContent);
    }
    
    // Инициализация компонентов по порядку
    initializeLoader();
    initializeMap();

    // Инициализация кластерной системы
    if (typeof window.initializeClusterSystem === 'function') {
        window.initializeClusterSystem();
    }

    initializeMapLayers(window.mymap);

    // Инициализация Яндекс карты
    initYandexMap();

    initializeLanguageControls();
    initializeFilters();
    initializeMapControls();
    initializeLegend();

    // Инициализация поиска
    if (typeof window.initSearch === 'function') {
        window.initSearch();
    }

    // Инициализация машины времени
    if (typeof window.initTimeMachine === 'function') {
        window.initTimeMachine();
    }

    // Янги Time Machine контейнерининг display property ни ўрнатиш
    const timeMachineContainer = document.getElementById('time-machine-floating-container');
    if (timeMachineContainer) {
        timeMachineContainer.style.display = 'none';
    }

    // Инициализация Off Stations Panel
    if (typeof window.initOffStationsPanel === 'function') {
        window.initOffStationsPanel();
    }

    // Инициализация РЦМУ маркеров
    if (typeof window.initRcmuMarkers === 'function') {
        window.initRcmuMarkers();
    }

    setupDynamicStyles();

    // Карта размерини тўғрилаш учун қўшимча кутиш
    setTimeout(function () {
        if (window.mymap) {
            window.mymap.invalidateSize();
            // Марказни яна белгилаш
            window.mymap.setView([41.3, 69.3], 5);
        }
    }, 500);

    // Восстановление состояния фильтров
    window.restoreFilterState();

    // Ҳар доим API дан маълумот юклаш
    window.loadMapData();
});

// Настройка динамических стилей
function setupDynamicStyles() {
    // Создание элемента стилей
    const dynamicStyle = document.createElement('style');
    document.head.appendChild(dynamicStyle);

    // Получение высоты navbar и настройка стилей
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        const navbarHeight = navbar.offsetHeight;
        const stylesheet = dynamicStyle.sheet;

        // Динамические стили на основе высоты navbar
        stylesheet.insertRule(`#mapid { top: ${navbarHeight}px; height: calc(100vh - ${navbarHeight}px); }`, 0);
        stylesheet.insertRule(`.sidebar { top: ${navbarHeight}px; height: calc(100vh - ${navbarHeight}px); }`, 0);

        if (navbarHeight > 56) {
            stylesheet.insertRule(`.map-type-control { top: ${navbarHeight + 20}px; }`, 0);
        }
    }
}

// Export функции для других модулей
window.getNavbarHeight = getNavbarHeight;
window.setupDynamicStyles = setupDynamicStyles; 