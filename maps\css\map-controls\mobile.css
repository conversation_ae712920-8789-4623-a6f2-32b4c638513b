/* Map controls мобил адаптация */

/* Адаптивность */
@media (max-width: 768px) {
    .map-type-control {
        top: 70px;
        right: 5px;
        padding: 2px;
    }

    .search-container {
        min-width: 150px;
        max-width: 180px;
    }

    .search-input {
        width: 120px;
        font-size: 12px;
        padding: 5px 6px;
        height: 32px;
    }

    .search-button {
        padding: 5px 8px;
        font-size: 12px;
        height: 32px;
    }

    .map-source-buttons button,
    .map-type-buttons button {
        width: 32px;
        height: 32px;
        padding: 4px;
        font-size: 12px;
    }

    .map-source-logo {
        width: 20px;
        height: 20px;
    }
}

/* Мобил учун адаптация */
@media (max-width: 768px) {
    .auto-center-control {
        top: 120px;
        left: 10px;
        width: 30px;
        height: 30px;
    }
    
    .auto-center-slider {
        font-size: 16px;
    }
}

/* Мобил учун адаптив */
@media (max-width: 768px) {
    .search-results {
        min-width: 280px !important;
        max-width: calc(100vw - 40px) !important;
    }
}

/* Агар экран кичик бўлса */
@media (max-width: 600px) {
    .search-container .search-results {
        min-width: 280px !important;
        max-width: calc(100vw - 40px) !important;
        left: 0 !important;
        right: auto !important;
    }
} 