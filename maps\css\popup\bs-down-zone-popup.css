/* BS Down Zone попаплари */
.leaflet-popup.bs-down-zone-popup {
    z-index: 3000 !important;
}

.leaflet-popup.bs-down-zone-popup .leaflet-popup-content-wrapper {
    padding: 0;
    overflow: hidden;
    border-radius: 4px;
    box-shadow: 0 3px 14px rgba(0, 0, 0, 0.4);
}

.leaflet-popup.bs-down-zone-popup .leaflet-popup-content {
    margin: 0;
    padding: 0;
    min-width: 300px;
    max-width: 400px;
}

.leaflet-popup.bs-down-zone-popup .leaflet-popup-close-button {
    color: white;
    font-size: 22px;
    font-weight: normal;
    top: 10px;
    right: 10px;
    width: 24px;
    height: 24px;
    padding: 0;
    text-align: center;
    line-height: 24px;
}

.leaflet-popup.bs-down-zone-popup .leaflet-popup-close-button:hover {
    color: #f0f0f0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
}

/* BS Down Zone попап header */
.bs-down-zone-popup .popup-header {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
    color: white !important;
    padding: 12px 15px;
    border-radius: 8px 8px 0 0;
    border-bottom: 2px solid #b02a37;
}

.bs-down-zone-popup .popup-header h4 {
    margin: 0 !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    color: white !important;
    display: flex;
    align-items: center;
    gap: 8px;
}

.bs-down-zone-popup .popup-header i {
    color: #fff3cd !important;
    font-size: 18px;
}

/* BS Down Zone попап body */
.bs-down-zone-popup .popup-body {
    padding: 15px;
    background: #ffffff !important;
}

/* Location info */
.bs-down-zone-popup .location-info {
    margin: 5px 0;
    padding: 8px 0;
}

.bs-down-zone-popup .location-info strong {
    color: #333 !important;
    font-size: 14px !important;
    display: flex;
    align-items: center;
    gap: 6px;
}

.bs-down-zone-popup .location-info i {
    color: #dc3545 !important;
}

/* Region info */
.bs-down-zone-popup .region-info {
    color: #666 !important;
    font-size: 13px !important;
    margin: 5px 0;
}

/* Reason box стили */
.bs-down-zone-popup .reason-box {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
    padding: 12px;
    border-radius: 6px;
    margin: 12px 0;
    border: 1px solid #dee2e6;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.bs-down-zone-popup .reason-box p {
    margin: 0 !important;
    color: #333 !important;
    font-size: 13px !important;
}

.bs-down-zone-popup .reason-box .reason-label {
    font-weight: 600 !important;
    color: #495057 !important;
    margin-bottom: 5px !important;
    display: flex;
    align-items: center;
    gap: 6px;
}

.bs-down-zone-popup .reason-box .reason-text {
    color: #555 !important;
    line-height: 1.4;
    padding: 8px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #e3e6ea;
}

/* No reason box */
.bs-down-zone-popup .no-reason-box {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7) !important;
    padding: 12px;
    border-radius: 6px;
    margin: 12px 0;
    border: 1px solid #ffeaa7;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.bs-down-zone-popup .no-reason-box p {
    margin: 0 !important;
    color: #856404 !important;
    font-size: 13px !important;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
}

.bs-down-zone-popup .no-reason-box i {
    color: #ffc107 !important;
}

/* Stations list */
.bs-down-zone-popup .stations-info {
    margin: 12px 0;
}

.bs-down-zone-popup .stations-info .stations-label {
    font-weight: 600 !important;
    color: #333 !important;
    font-size: 13px !important;
    margin-bottom: 8px !important;
    display: flex;
    align-items: center;
    gap: 6px;
}

.bs-down-zone-popup .stations-info i {
    color: #6c757d !important;
}

.bs-down-zone-popup .stations-list {
    max-height: 150px;
    overflow-y: auto;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
    padding: 10px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.bs-down-zone-popup .stations-list::-webkit-scrollbar {
    width: 4px;
}

.bs-down-zone-popup .stations-list::-webkit-scrollbar-track {
    background: #f1f3f4;
    border-radius: 2px;
}

.bs-down-zone-popup .stations-list::-webkit-scrollbar-thumb {
    background: #c1c8cd;
    border-radius: 2px;
}

.bs-down-zone-popup .stations-list::-webkit-scrollbar-thumb:hover {
    background: #a8b3ba;
}

.bs-down-zone-popup .stations-list .station-item {
    padding: 4px 0;
    font-size: 12px !important;
    color: #555 !important;
    line-height: 1.3;
}

.bs-down-zone-popup .stations-list .station-item:not(:first-child) {
    border-top: 1px solid #e0e6ed;
    padding-top: 6px;
} 