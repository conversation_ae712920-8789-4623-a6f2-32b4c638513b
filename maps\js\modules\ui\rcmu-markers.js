// РЦМУ маркерлар координатор модули
// Бу модул rcmu-ui.js, rcmu-data.js, rcmu-display.js компонентларини координация қилади

(function() {
    'use strict';

    // РЦМУ маркерлар системасини инициализация қилиш
    window.initRcmuMarkers = function() {
        // Бўлинган компонентларни инициализация қилиш
        initializeRcmuComponents();
        
        // Фильтрлар ўзгарганда РЦМУ маркерларини янгилаш
        setupFilterListeners();
        
        // Глобал функцияларни экспорт қилиш
        exportGlobalFunctions();
    };

    // Бўлинган компонентларни инициализация қилиш
    function initializeRcmuComponents() {
        // UI компонентини инициализация қилиш
        if (window.initRcmuUI) {
            window.initRcmuUI();
        }

        // Display компонентини инициализация қилиш
        if (window.initRcmuDisplay) {
            window.initRcmuDisplay();
        }

        // Data компонентини инициализация қилиш (автоматик)
        // loadRcmuData функцияси window.RcmuUI.isVisible() га боғлиқ
    }

    // Фильтрлар ўзгарганда РЦМУ маркерларини янгилаш
    function setupFilterListeners() {
        // Регион фильтри ўзгарганда
        const regionSelect = document.getElementById('region');
        if (regionSelect) {
            regionSelect.addEventListener('change', handleFilterChange);
        }

        // Ҳудуд фильтри ўзгарганда
        const areaSelect = document.getElementById('area');
        if (areaSelect) {
            areaSelect.addEventListener('change', handleFilterChange);
        }

        // Статус фильтри ўзгарганда
        const statusSelect = document.getElementById('status');
        if (statusSelect) {
            statusSelect.addEventListener('change', handleFilterChange);
        }
    }

    // Фильтр ўзгарганда РЦМУ маркерларини янгилаш
    function handleFilterChange() {
        // Агар РЦМУ кўринадиган бўлса, маркерларни янгилаш
        if (window.RcmuUI && window.RcmuUI.isVisible()) {
            refreshRcmuMarkers();
        }
    }

    // РЦМУ маркерларини янгилаш
    function refreshRcmuMarkers() {
        // Мавжуд маълумотлар билан маркерларни қайта кўрсатиш
        const rcmuData = window.getRcmuData ? window.getRcmuData() : [];
        if (rcmuData.length > 0 && window.showRcmuMarkers) {
            window.showRcmuMarkers(rcmuData);
        }
    }

    // Глобал функцияларни экспорт қилиш (backward compatibility учун)
    function exportGlobalFunctions() {
        // РЦМУ маркерларини фильтрлар учун янгилаш
        window.refreshRcmuMarkersForFilters = refreshRcmuMarkers;
        
        // РЦМУ маркерларини яшириш (display компонентига йўналтириш)
        window.hideRcmuMarkersLegacy = function() {
            if (window.hideRcmuMarkers) {
                window.hideRcmuMarkers();
            }
        };
        
        // РЦМУ кўринадими текшириш
        window.checkRcmuVisible = function() {
            return window.RcmuUI ? window.RcmuUI.isVisible() : false;
        };
    }

    // Авария режими ўзгарганда
    window.addEventListener('alarmModeChanged', function(event) {
        if (window.RcmuUI) {
            if (event.detail.enabled) {
                window.RcmuUI.disable();
            } else {
                window.RcmuUI.enable();
            }
        }
    });

    // Time Machine режими ўзгарганда
    const timeMachineCheckbox = document.getElementById('time-machine-enabled') ||
        document.getElementById('time-machine-checkbox');
    if (timeMachineCheckbox) {
        timeMachineCheckbox.addEventListener('change', function(event) {
            if (window.RcmuUI) {
                if (event.target.checked) {
                    window.RcmuUI.disable();
                } else {
                    window.RcmuUI.enable();
                }
            }
        });
    }

    // Off Stations Panel ўзгарганда
    window.addEventListener('offStationsPanelChanged', function(event) {
        // Хозирча бўш - кейинчалик функционал қўшилиши мумкин
    });

})();
