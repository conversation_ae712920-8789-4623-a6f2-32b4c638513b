/* Основные стили */
body {
    margin: 0;
    padding: 0;
    overflow: hidden;
}

.container-fluid {
    padding: 0;
    margin: 0;
    position: relative;
}

/* Контейнер карты */
#mapid {
    height: calc(100vh - 60px) !important;
    width: calc(100% - 300px);
    margin: 0;
    padding: 0;
    position: fixed;
    top: 60px !important;
    left: 300px;
    right: 0;
    bottom: 0;
    z-index: 95;
    /* Карта юкланишида марказлаштириш учун */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Навбар */
.navbar {
    margin-bottom: 0 !important;
    z-index: 2000;
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    height: 60px !important;
}

/* Leaflet контролы */
.leaflet-control-container .leaflet-top {
    top: 20px;
}

.leaflet-control-container .leaflet-right {
    right: 10px;
}

.leaflet-control-zoom {
    margin-right: 10px;
}

.leaflet-control-attribution {
    display: none !important;
}

/* Копирайт */
.custom-copyright {
    position: absolute;
    bottom: 5px;
    right: 10px;
    background-color: rgba(255, 255, 255, 0.7);
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    color: #333;
    z-index: 1000;
    font-weight: 500;
}

/* Утилиты отступов */
.sidebar-content>div:nth-child(2) {
    margin-top: 3px;
}

.regions-table+div {
    margin-top: 3px;
}

/* Tooltip для названий БС */
.bs-name-tooltip {
    background: transparent;
    border: none;
    box-shadow: none;
    color: #000000;
    font-weight: bold;
    font-size: 10px;
    padding: 0;
    text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.7);
    white-space: nowrap;
    border-radius: 0;
    transition: font-size 0.2s;
}

.zoom-level-low .bs-name-tooltip {
    font-size: 8px;
}

.zoom-level-medium .bs-name-tooltip {
    font-size: 10px;
}

.zoom-level-high .bs-name-tooltip {
    font-size: 12px;
}

.leaflet-tooltip.bs-name-tooltip {
    background: transparent;
    border: none;
    box-shadow: none;
}

.leaflet-tooltip.bs-name-tooltip:before {
    display: none;
}

/* Карта тайллари орасидаги бўшлиқларни йўқотиш */
.leaflet-container {
    background: #ddd;
    font-size: 0;
    line-height: 0;
    width: 100% !important;
    height: 100% !important;
}

.leaflet-tile-container {
    pointer-events: none;
}

.leaflet-tile {
    pointer-events: auto;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

/* Safari учун махсус */
@media not all and (min-resolution:.001dpcm) {
    @supports (-webkit-appearance:none) {
        .leaflet-tile {
            image-rendering: -webkit-optimize-contrast;
        }
    }
}

/* Tile юкланишидаги муаммоларни ҳал қилиш */
.leaflet-tile-loaded {
    visibility: inherit;
}

.leaflet-zoom-animated .leaflet-tile {
    transition: none;
} 