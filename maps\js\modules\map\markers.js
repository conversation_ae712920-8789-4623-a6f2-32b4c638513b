// Модуль работы с маркерами

// Массив для хранения маркеров
window.pointLayers = [];

// Определение размера круга в зависимости от уровня зума
function getCircleRadius(zoom) {
    if (zoom < 6) return 2;
    else if (zoom < 10) return 4;
    else return 10;
}

// Создание маркера БС
window.createBSMarker = function (point, statusValue, checkDuration = true, isOtherAlarmsMode = false) {
    // Проверка координат
    if (!point.lat || !point.lon || isNaN(parseFloat(point.lat)) || isNaN(parseFloat(point.lon))) {
        return null;
    }

    const pointName = point.bsName || point.name;

    // Определение цвета и группы продолжительности
    let pointColor = 'green';
    let durationGroup = 0;

    // Проверка статуса
    const isOffline = point.status === true || point.status === 'true' || point.status === 1 || point.status === '1';
    if (isOffline) { // БС в аварии
        if (statusValue === 'online') {
            return null; // Не показываем аварийные БС при фильтре "онлайн"
        }

        // Other alarms режимида
        if (isOtherAlarmsMode && point.durationCategory) {
            // Давомийлик категорияси бўйича ранг белгилаш
            // alarm-manager.js дан келган форматни қабул қилиш
            durationGroup = parseInt(point.durationCategory) || 1;
            pointColor = getDowntimeColor(durationGroup);
        } else {
            // Обычный режим - Определение группы продолжительности
            durationGroup = window.getDowntimeDuration(point);
            
            // Машина времени режимида ҳам downtime duration га қараб ранг белгилаш
            if (window.isTimeMachineEnabled || point.historical) {
                // Downtime duration га қараб ранг олиш
                pointColor = getDowntimeColor(durationGroup);
                

            } else {
                // Реал вақт режимида
                pointColor = getDowntimeColor(durationGroup);
            }
        }
    } else { // БС работает
        if (statusValue === 'offline') {
            return null; // Не показываем работающие БС при фильтре "оффлайн"
        }
        pointColor = 'green';
    }

    // Проверка фильтра продолжительности
    if (checkDuration) {
        const currentDurationFilter = document.getElementById('duration-filter').value;
        if (currentDurationFilter !== 'all') {
            if (isOffline) {
                if (parseInt(currentDurationFilter) !== durationGroup) {
                    return null;
                }
            } else {
                return null; // Не показываем работающие БС при фильтре продолжительности
            }
        }
    }

    // Координаты
    let lat = parseFloat(point.lat);
    let lon = parseFloat(point.lon);

    // Определение необходимости постоянной подписи
    const permanentTooltip = statusValue === 'offline' && isOffline;

    // Создание маркера
    const circleMarker = L.circleMarker([lat, lon], {
        radius: 10, // Default to a larger size, zoom handler will adjust it
        fillColor: pointColor,
        color: "#000",
        weight: 1,
        opacity: 1,
        fillOpacity: 0.8,
        pointName: pointName,
        durationGroup: durationGroup,
        pointData: point
    });

    // Добавление hover tooltip для аварийных БС
    if (isOffline) {
        circleMarker.on('mouseover', function (e) {
            // Подготовка содержимого popup
            let popupContent = window.createPopupContent(point);

            // Создание временного popup
            let popup = L.popup({
                offset: [0, -10],
                closeButton: false,
                className: 'bs-hover-popup'
            })
                .setLatLng(e.latlng)
                .setContent(popupContent)
                .openOn(window.mymap);

            // Сохранение popup в маркере
            this._hoverPopup = popup;
        });

        // Закрытие popup при mouseout
        circleMarker.on('mouseout', function (e) {
            if (this._hoverPopup) {
                window.mymap.closePopup(this._hoverPopup);
                this._hoverPopup = null;
            }
        });

        // Маркерга босганда картани марказлаштириш
        circleMarker.on('click', function (e) {
            // Автоматик марказлаштириш ёқилган бўлса
            if (window.autoCenterEnabled !== false) {
                // Агар Ўзбекистон чегарасидан чиқиб кетган бўлса, аввал Ўзбекистонга қайтариш
                const currentBounds = window.mymap.getBounds();
                if (window.uzbekistanBounds && !window.uzbekistanBounds.contains(currentBounds.getCenter())) {
                    // Аввал Ўзбекистон марказига қайтариш
                    window.mymap.flyTo([41.3, 69.3], 5, {
                        duration: 1
                    });
                    // Кейин маркер жойига ўтиш
                    setTimeout(() => {
                        window.mymap.flyTo(e.latlng, Math.max(window.mymap.getZoom(), 12), {
                            duration: 1.5
                        });
                    }, 1200);
                } else {
                    // Оддий марказлаштириш
                    window.mymap.flyTo(e.latlng, Math.max(window.mymap.getZoom(), 12), {
                        duration: 1
                    });
                }
            }
        });

        // Постоянный tooltip с именем БС
        if (permanentTooltip) {
            circleMarker.bindTooltip(pointName, {
                permanent: true,
                direction: 'bottom',
                offset: [0, 3],
                className: 'bs-name-tooltip'
            });
        }
    } else {
        // Online БС лар учун ҳам hover popup қўшиш
        circleMarker.on('mouseover', function (e) {
            // Подготовка содержимого popup
            let popupContent = window.createPopupContent(point);

            // Создание временного popup
            let popup = L.popup({
                offset: [0, -10],
                closeButton: false,
                className: 'bs-hover-popup'
            })
                .setLatLng(e.latlng)
                .setContent(popupContent)
                .openOn(window.mymap);

            // Сохранение popup в маркере
            this._hoverPopup = popup;
        });

        // Закрытие popup при mouseout
        circleMarker.on('mouseout', function (e) {
            if (this._hoverPopup) {
                window.mymap.closePopup(this._hoverPopup);
                this._hoverPopup = null;
            }
        });
        
        // Online БС га босганда ҳам картани марказлаштириш
        circleMarker.on('click', function (e) {
            // Автоматик марказлаштириш ёқилган бўлса
            if (window.autoCenterEnabled !== false) {
                // Агар Ўзбекистон чегарасидан чиқиб кетган бўлса, аввал Ўзбекистонга қайтариш
                const currentBounds = window.mymap.getBounds();
                if (window.uzbekistanBounds && !window.uzbekistanBounds.contains(currentBounds.getCenter())) {
                    // Аввал Ўзбекистон марказига қайтариш
                    window.mymap.flyTo([41.3, 69.3], 5, {
                        duration: 1
                    });
                    // Кейин маркер жойига ўтиш
                    setTimeout(() => {
                        window.mymap.flyTo(e.latlng, Math.max(window.mymap.getZoom(), 12), {
                            duration: 1.5
                        });
                    }, 1200);
                } else {
                    // Оддий марказлаштириш
                    window.mymap.flyTo(e.latlng, Math.max(window.mymap.getZoom(), 12), {
                        duration: 1
                    });
                }
            }
        });
        
        // Online БС лар учун tooltip қўшмаймиз, фақат popup ишлатамиз
        // Бу popup билан конфликт бўлмаслиги учун
    }

    return circleMarker;
}

// Обновление размеров маркеров при изменении зума
window.updateCircleSizes = function () {
    const currentZoom = window.mymap.getZoom();
    const newRadius = getCircleRadius(currentZoom);

    // Определение класса для масштаба текста
    let zoomClass = '';
    if (currentZoom < 8) {
        zoomClass = 'zoom-level-low';
    } else if (currentZoom < 12) {
        zoomClass = 'zoom-level-medium';
    } else {
        zoomClass = 'zoom-level-high';
    }

    // Обновление классов
    document.body.classList.remove('zoom-level-low', 'zoom-level-medium', 'zoom-level-high');
    document.body.classList.add(zoomClass);

    // Обновление радиуса всех маркеров в кластере
    if (window.bsMarkerCluster) {
        window.bsMarkerCluster.eachLayer(function (layer) {
            if (layer instanceof L.CircleMarker) {
                layer.setRadius(newRadius);

                // Обновление offset для постоянных подсказок
                if (layer.getTooltip() && layer.getTooltip().options.permanent) {
                    let offsetValue = 3;
                    if (currentZoom < 8) {
                        offsetValue = 2;
                    } else if (currentZoom >= 12) {
                        offsetValue = 4;
                    }

                    const tooltipContent = layer.getTooltip().getContent();

                    layer.unbindTooltip();
                    layer.bindTooltip(tooltipContent, {
                        permanent: true,
                        direction: 'bottom',
                        offset: [0, offsetValue],
                        className: 'bs-name-tooltip'
                    });
                }
            }
        });
    }
    
    // Fallback - эски маркерлар учун (кластер ташқарисидаги)
    window.mymap.eachLayer(function (layer) {
        if (layer instanceof L.CircleMarker && layer._parentCluster === undefined) {
            layer.setRadius(newRadius);

            // Обновление offset для постоянных подсказок
            if (layer.getTooltip() && layer.getTooltip().options.permanent) {
                let offsetValue = 3;
                if (currentZoom < 8) {
                    offsetValue = 2;
                } else if (currentZoom >= 12) {
                    offsetValue = 4;
                }

                const tooltipContent = layer.getTooltip().getContent();

                layer.unbindTooltip();
                layer.bindTooltip(tooltipContent, {
                    permanent: true,
                    direction: 'bottom',
                    offset: [0, offsetValue],
                    className: 'bs-name-tooltip'
                });
            }
        }
    });

    // Синхронизация с Яндекс картой
    if (window.isYandexMode && window.yandexMap) {
        window.syncYandexMapWithLeaflet();
    }
}

// Обновление текста статуса БС при смене языка
function updateBSStatusText() {
    // Кластердаги маркерларни янгилаш
    if (window.bsMarkerCluster) {
        window.bsMarkerCluster.eachLayer(function (layer) {
            if (layer instanceof L.CircleMarker && layer.getTooltip()) {
                const pointName = layer.options.pointName;
                const isOffline = layer.options.fillColor !== 'green';

                // Обновление только для непостоянных tooltip (фақат offline БС лар учун)
                if (!layer.getTooltip().options.permanent && isOffline) {
                    const statusText = window.translations[window.currentLanguage]['bs_inactive'];

                    layer.unbindTooltip();
                    layer.bindTooltip("<b>" + pointName + "</b><br>" + statusText, {
                        permanent: false,
                        direction: 'top',
                        offset: [0, -10]
                    });
                }
            }
        });
    }
    
    // Fallback - кластер ташқарисидаги маркерлар учун
    window.mymap.eachLayer(function (layer) {
        if (layer instanceof L.CircleMarker && layer.getTooltip() && layer._parentCluster === undefined) {
            const pointName = layer.options.pointName;
            const isOffline = layer.options.fillColor !== 'green';

            // Обновление только для непостоянных tooltip (фақат offline БС лар учун)
            if (!layer.getTooltip().options.permanent && isOffline) {
                const statusText = window.translations[window.currentLanguage]['bs_inactive'];

                layer.unbindTooltip();
                layer.bindTooltip("<b>" + pointName + "</b><br>" + statusText, {
                    permanent: false,
                    direction: 'top',
                    offset: [0, -10]
                });
            }
        }
    });
}

// Определение группы продолжительности простоя
window.getDowntimeDuration = function(point) {
    // Машина времени режимида алоҳида функцияни ишлатиш
    if (window.isTimeMachineEnabled && window.getDowntimeDurationForTimeMachine) {
        return window.getDowntimeDurationForTimeMachine(point);
    }
    
    if (!point.calcTime) return 0;
    
    const timeStr = point.calcTime;
    let totalMinutes = 0;
    
    // Парсинг строки времени "X hours Y minutes" или "X дней Y часов"
    const dayMatch = timeStr.match(/(\d+)\s*(д|d|дней|days)/);
    const hourMatch = timeStr.match(/(\d+)\s*(ч|h|час|hour)/);
    const minMatch = timeStr.match(/(\d+)\s*(м|m|мин|min)/);
    
    if (dayMatch) {
        totalMinutes += parseInt(dayMatch[1]) * 24 * 60;
    }
    if (hourMatch) {
        totalMinutes += parseInt(hourMatch[1]) * 60;
    }
    if (minMatch) {
        totalMinutes += parseInt(minMatch[1]);
    }
    
    // Группировка по часам
    const hours = totalMinutes / 60;
    
    if (hours <= 1) return 1;
    else if (hours <= 2) return 2;
    else if (hours <= 3) return 3;
    else if (hours <= 4) return 4;
    else if (hours <= 24) return 6;  // 4 соат - 1 кун
    else if (hours <= 168) return 7; // 1 кун - 7 кун (168 соат = 7 кун)
    else return 8; // 7 кундан кўп
}

// Получение цвета по группе продолжительности
function getDowntimeColor(durationGroup) {
    const group = parseInt(durationGroup) || 0;
    // Агар 0 бўлса (авария йўқ), яшил ранг қайтариш
    if (group === 0) return 'green';
    
    // Тубдан фарқли рангларни қўллаш (яшил ва қорадан ташқари)
    const colors = {
        1: '#0066FF',  // До 1 часа - ёрқин кўк
        2: '#FFD700',  // До 2 часов - тилло сариқ
        3: '#9900FF',  // До 3 часов - бинафша
        4: '#FF0000',  // До 4 часов - қизил
        5: '#FF6600',  // Более 4 часов - оранж
        6: '#00CCCC',  // 4 часа - 1 день - турк кўки
        7: '#FF1493',  // 1 день - 7 дней - қип-қизил пинк
        8: '#000000'   // Более 7 дней - қора
    };
    
    return colors[group] || '#ff0000';
} 