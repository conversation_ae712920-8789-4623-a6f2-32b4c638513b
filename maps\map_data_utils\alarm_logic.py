"""
Авария маълумотларини ишлаш логикаси
"""


def get_technology_from_alarm(alarm_name):
    """Авария номидан технология турини аниқлаш"""
    if not alarm_name:
        return None

    alarm_name_lower = alarm_name.lower()

    # 2G учун OML Fault
    if 'oml fault' in alarm_name_lower or 'oml_fault' in alarm_name_lower:
        return '2G'
    # 3G учун NodeB Unavailable
    elif 'nodeb unavailable' in alarm_name_lower:
        return '3G'
    # 4G учун S1ap Link Down ёки S1 Interface Fault
    elif 's1ap link down' in alarm_name_lower or 's1 interface fault' in alarm_name_lower:
        return '4G'

    return None 