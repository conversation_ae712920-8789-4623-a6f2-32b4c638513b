"""
API эндпоинтлар модули
"""
from datetime import datetime
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from django.utils.dateparse import parse_datetime
from bsview.models import RegionUzb, AreaUzb, Current_Alarms, Log_Alarms
from ..map_data_utils import get_formatted_bs_data, getNumberBS, get_cabinet_type, get_technology_from_alarm


@api_view(['GET'])
def get_map_data(request):
    """API для получения данных карты"""
    try:
        # Получаем параметр времени если есть
        datetime_param = request.query_params.get('datetime')
        
        # Для отладки
        if datetime_param:
            print(f"[DEBUG] get_map_data: datetime_param = {datetime_param}")
        
        # Агар datetime берилган бўлса, тарихий маълумотларни олиш
        if datetime_param:
            # Используем функцию get_formatted_bs_data с параметром datetime
            # skip_alarms=False қилиб авария маълумотларини олиш
            bs_stations = get_formatted_bs_data(datetime_param=datetime_param, skip_alarms=False)
        else:
            # Жорий маълумотларни олиш
            bs_stations = get_formatted_bs_data(skip_alarms=True)
            
            # БС лар ва аларм луғати
            bs_by_number = {}
            alarm_details = {}
            
            # БС маълумотларини номер бўйича луғатга жойлаш
            for bs in bs_stations:
                bsnum = getNumberBS(bs['name'])
                if bsnum:
                    bs_by_number[bsnum] = bs['id']
            
            # Барча аларм маълумотларини йиғиш
            for alarm in Current_Alarms.objects.all():
                bs_number = getNumberBS(alarm.bsname)
                if not bs_number or bs_number not in bs_by_number:
                    continue

                bs_id = bs_by_number[bs_number]

                # Технология турини аниқлаш
                if alarm.bscrnc.startswith("BSC"):
                    typeG = "2G"
                    alarm_name = "OML Fault"
                elif alarm.bscrnc.startswith("RNC"):
                    typeG = "3G"
                    alarm_name = "NodeB Unavailable"
                elif alarm.bscrnc.startswith("LTE"):
                    typeG = "4G"
                    alarm_name = "S1ap Link Down"
                else:
                    continue

                # Илк марта кўрилган БС алармини қўшиш
                if bs_id not in alarm_details:
                    alarm_details[bs_id] = {
                        'status': True,
                        'calcTime': str(alarm.calctime) if hasattr(alarm, 'calctime') else None,
                        'typeG': typeG,
                        'cabinetType': get_cabinet_type(alarm.bsname),
                        'alarms_by_tech': {}
                    }

                # Технология бўйича авария маълумотларини қўшиш
                alarm_details[bs_id]['alarms_by_tech'][typeG] = {
                    'alarmname': alarm_name,
                    'appeartime': alarm.appeartime.isoformat() if alarm.appeartime else None,
                    'cleartime': None,  # Жорий авария ҳали тугамаган
                    'duration': str(alarm.calctime) if hasattr(alarm, 'calctime') else None
                }

                # Мавжуд БС га технология турини қўшиш
                if typeG not in alarm_details[bs_id]['typeG']:
                    alarm_details[bs_id]['typeG'] += "/" + typeG
            
            # Аваряли БС ларга статус қўшиш
            for bs in bs_stations:
                if bs['id'] in alarm_details:
                    bs.update(alarm_details[bs['id']])
        
        # Для отладки - считаем БС с авариями
        stations_with_alarms = [s for s in bs_stations if s.get('status', False) is True]
        print(f"[DEBUG] get_map_data: Total stations = {len(bs_stations)}, With alarms = {len(stations_with_alarms)}")
        
        # Получаем список регионов
        regions = list(RegionUzb.objects.all().values('id', 'name'))
        
        # Формируем ответ
        response_data = {
            'points': bs_stations,
            'regions': regions
        }
        
        return Response(response_data)
    except Exception as e:
        print(f"[ERROR] get_map_data: {str(e)}")
        print(f"[ERROR] Exception type: {type(e).__name__}")
        import traceback
        print(f"[ERROR] Traceback:\n{traceback.format_exc()}")
        return Response({"error": str(e), "type": type(e).__name__}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_all_regions(request):
    """Barcha viloyatlarni olish"""
    regions = RegionUzb.objects.all().values('id', 'name')
    return Response(list(regions))


@api_view(['GET'])
def get_all_areas(request):
    """Barcha tumanlarni olish"""
    areas = AreaUzb.objects.all().values('id', 'name', 'region_id')
    return Response(list(areas))


@api_view(['GET'])
def get_areas_by_region(request, region_id):
    """Viloyat bo'yicha tumanlarni olish"""
    areas = AreaUzb.objects.filter(region_id=region_id).values('id', 'name', 'region_id')
    return Response(list(areas))


@api_view(['GET'])
def get_historical_alarms(request):
    """Танланган вақт учун тарихий аварияларни олиш"""
    try:
        # Вақт параметрини олиш
        datetime_str = request.query_params.get('datetime')
        if not datetime_str:
            return Response({"error": "datetime parameter is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Вақтни парс қилиш
        try:
            # Аввал ISO форматни синаб кўрамиз
            target_datetime = parse_datetime(datetime_str)
            if target_datetime and target_datetime.tzinfo:
                # Агар timezone маълумоти бор бўлса, уни олиб ташлаймиз
                target_datetime = target_datetime.replace(tzinfo=None)
            elif not target_datetime:
                # Агар ISO формат ишламаса, бошқа форматларни синаб кўрамиз
                target_datetime = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return Response({"error": "Invalid datetime format. Use ISO format or YYYY-MM-DD HH:MM:SS"},
                          status=status.HTTP_400_BAD_REQUEST)

        # Танланган вақт авария давомийлиги ичида бўлган аварияларни топиш
        historical_alarms = Log_Alarms.objects.filter(
            appeartime__lte=target_datetime,  # Авария танланган вақтдан олдин ёки ўша вақтда бошланган
            cleartime__gte=target_datetime    # Авария танланган вақтда ёки кейин тугаган
        ).values('bsname', 'bsnumber', 'alarmname', 'appeartime', 'cleartime')

        # Натижаларни форматлаш
        result = []
        for alarm in historical_alarms:
            technology = get_technology_from_alarm(alarm['alarmname'])
            if technology:  # Фақат 2G/3G/4G аварияларини қайтариш
                # Авария давомийлигини ҳисоблаш
                appear_time = alarm['appeartime']

                # Танланган вақтгача бўлган давомийликни ҳисоблаш
                duration = target_datetime - appear_time
                duration_hours = duration.total_seconds() / 3600

                # Фақат мусбат давомийликни қабул қилиш
                if duration_hours > 0:
                    result.append({
                        'bsname': alarm['bsname'],
                        'bsnumber': alarm['bsnumber'],
                        'alarmname': alarm['alarmname'],
                        'technology': technology,
                        'appeartime': alarm['appeartime'],
                        'cleartime': alarm['cleartime'],
                        'duration_hours': round(duration_hours, 2)
                    })

        return Response(result)

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 