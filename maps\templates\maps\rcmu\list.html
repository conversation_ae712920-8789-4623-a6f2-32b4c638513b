{% extends 'base.html' %}
{% load static %}

{% block title %}РЦМУ - Управление данными{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="/maps/css/rcmu_list.css?v=1.2">
{% endblock %}

{% block content %}
<div class="rcmu-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="header-text">
                    <h1 class="mb-0">
                        <i class="fas fa-satellite-dish me-2"></i>
                        РЦМУ - Управление данными
                    </h1>
                    <p class="mb-0 mt-2">Загрузка и управление данными о базовых станциях без трафика</p>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <div class="stats-card">
                    <h3 class="mb-0">{{ total_count }}</h3>
                    <small>Всего записей</small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Файл юклаш учун тугма -->
    <div class="d-flex justify-content-end mb-3">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
            <i class="fas fa-upload me-2"></i>Загрузить Excel файл
        </button>
    </div>

    <!-- Таблица данных -->
    <div class="table-responsive">
        <table class="table table-hover" id="rcmuDataTable">
            <thead class="table-dark">
                <tr>
                    <th>Site</th>
                    <th>Название БС</th>
                    <th>Регион</th>
                    <th>Филиал</th>
                    <th>Дата (LEGO)</th>
                    <th>Ответственный</th>
                    <th>Координаты</th>
                    <th>Комментарий качественника</th>
                    <th>Действия</th>
                </tr>
                <tr class="filter-row">
                    <th><input type="text" class="form-control form-control-sm" placeholder="Фильтр..."></th>
                    <th><input type="text" class="form-control form-control-sm" placeholder="Фильтр..."></th>
                    <th>
                        <select class="form-select form-select-sm">
                            <option value="">Все</option>
                            {% for region in regions %}
                            <option value="{{ region }}">{{ region }}</option>
                            {% endfor %}
                        </select>
                    </th>
                    <th>
                        <select class="form-select form-select-sm">
                            <option value="">Все</option>
                            {% for branch in branches %}
                            <option value="{{ branch }}">{{ branch }}</option>
                            {% endfor %}
                        </select>
                    </th>
                    <th><input type="text" class="form-control form-control-sm" placeholder="Фильтр..."></th>
                    <th>
                        <select class="form-select form-select-sm">
                            <option value="">Все</option>
                            {% for responsible in responsibles %}
                            <option value="{{ responsible }}">{{ responsible }}</option>
                            {% endfor %}
                        </select>
                    </th>
                    <th>
                        <select class="form-select form-select-sm">
                            <option value="">Все</option>
                            <option value="Есть">Есть</option>
                            <option value="Нет">Нет</option>
                        </select>
                    </th>
                    <th>
                        <select class="form-select form-select-sm">
                            <option value="">Все</option>
                            <option value="Есть">Есть</option>
                            <option value="Нет">Нет</option>
                        </select>
                    </th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                {% for item in page_obj %}
                <tr>
                    <td><strong>{{ item.site }}</strong></td>
                    <td>{{ item.bs_name|default:"—" }}</td>
                    <td>{{ item.region|default:"—" }}</td>
                    <td>{{ item.branch|default:"—" }}</td>
                    <td>{{ item.date_lego|date:"d.m.Y"|default:"—" }}</td>
                    <td>{{ item.responsible|default:"—" }}</td>
                    <td>
                        {% if item.lat and item.lon %}
                        <span class="badge bg-success">Есть</span>
                        {% else %}
                        <span class="badge bg-warning">Нет</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if item.quality_comment %}
                        <span class="badge bg-info">Есть</span>
                        {% else %}
                        <span class="badge bg-secondary">Нет</span>
                        {% endif %}
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary btn-action" onclick="viewDetails({{ item.id }})" title="Просмотр">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-warning btn-action" onclick="editComment({{ item.id }})" title="Редактировать комментарий">
                            <i class="fas fa-edit"></i>
                        </button>
                        {% if user.privilege_id == 1 or user.privilege_id == 6 %}
                        <button class="btn btn-sm btn-outline-danger btn-action" onclick="deleteRecord({{ item.id }})" title="Удалить">
                            <i class="fas fa-trash"></i>
                        </button>
                        {% endif %}
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="9" class="text-center py-4">
                        <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">Данные не найдены</p>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Пагинация -->
    {% if page_obj.has_other_pages %}
    <nav aria-label="Пагинация">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link"
                    href="?page=1">Первая</a>
            </li>
            <li class="page-item">
                <a class="page-link"
                    href="?page={{ page_obj.previous_page_number }}">Предыдущая</a>
            </li>
            {% endif %}

            <li class="page-item active">
                <span class="page-link">{{ page_obj.number }} из {{ page_obj.paginator.num_pages }}</span>
            </li>

            {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link"
                    href="?page={{ page_obj.next_page_number }}">Следующая</a>
            </li>
            <li class="page-item">
                <a class="page-link"
                    href="?page={{ page_obj.paginator.num_pages }}">Последняя</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>

<!-- Модальные окна -->
<!-- Файл юклаш модал ойнаси -->
<div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadModalLabel">Загрузка Excel файла</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Область загрузки файла -->
                <div class="upload-area" id="uploadArea">
                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                    <h4>Загрузить Excel файл</h4>
                    <p class="text-muted">Перетащите файл сюда или нажмите для выбора</p>
                    <form id="uploadForm" enctype="multipart/form-data">
                        {% csrf_token %}
                        <input type="file" id="fileInput" name="excel_file" accept=".xlsx,.xls" style="display: none;">
                        <button type="button" class="btn btn-secondary" onclick="document.getElementById('fileInput').click();">
                            <i class="fas fa-file-excel me-2"></i>Выбрать файл
                        </button>
                    </form>
                    <div id="uploadProgress" class="mt-3" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Модальное окно просмотра деталей -->
<div class="modal fade" id="detailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Детали записи РЦМУ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detailModalBody">
                <!-- Содержимое загружается через AJAX -->
            </div>
        </div>
    </div>
</div>

<!-- Модальное окно редактирования комментария -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Редактировать комментарий качественника</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <div class="mb-3">
                        <label for="qualityComment" class="form-label">Комментарий качественника</label>
                        <textarea class="form-control" id="qualityComment" rows="4"
                            placeholder="Введите комментарий качественника..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                <button type="button" class="btn btn-primary" onclick="saveComment()">Сохранить</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="/maps/js/rcmu_list.js"></script>
{% endblock %}