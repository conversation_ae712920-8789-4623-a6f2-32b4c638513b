// Alarm Markers Module - Alarm Manager модулининг маркерлар бошқариш функциялари

window.AlarmMarkers = (function() {
    'use strict';
    
    // Маркерларни янгилаш
    function updateAlarmMarkers(data) {
        if (!window.updateMapMarkers) return;
        
        // Авария маълумотларини map data форматига ўгириш
        const formattedStations = data.stations || [];
        
        // Ҳар бир БС учун давомийлик категориясини аниқлаш
        formattedStations.forEach(station => {
            if (station.other_alarms && station.other_alarms.length > 0) {
                // Энг узоқ давом этган аварияни топиш
                const maxDuration = Math.max(...station.other_alarms.map(a => a.duration_hours || 0));
                
                // Давомийлик категорияси - normal режим билан бир хил
                if (maxDuration <= 1) {
                    station.durationCategory = '1';
                } else if (maxDuration <= 2) {
                    station.durationCategory = '2';
                } else if (maxDuration <= 3) {
                    station.durationCategory = '3';
                } else if (maxDuration <= 4) {
                    station.durationCategory = '4';
                } else if (maxDuration <= 24) {
                    station.durationCategory = '6';
                } else if (maxDuration <= 168) {
                    station.durationCategory = '7';
                } else {
                    station.durationCategory = '8';
                }
                
                // calcTime ни форматлаш
                station.calcTime = station.other_alarms[0].duration_text;
            }
        });
        
        // Маркерларни янгилаш
        window.updateMapMarkers(formattedStations, true); // true = other alarms mode
    }
    
    // Public API
    return {
        updateAlarmMarkers
    };
})(); 