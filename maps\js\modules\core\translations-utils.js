// Таржима утилиталари модули - Translation utilities

// Транслитерация кириллицы для неизвестных названий
window.transliterate = function(text) {
    const translitMap = {
        'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo',
        'ж': 'zh', 'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm',
        'н': 'n', 'о': 'o', 'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u',
        'ф': 'f', 'х': 'kh', 'ц': 'ts', 'ч': 'ch', 'ш': 'sh', 'щ': 'sch', 'ъ': '',
        'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya',
        'А': 'A', 'Б': 'B', 'В': 'V', 'Г': 'G', 'Д': 'D', 'Е': 'E', 'Ё': 'Yo',
        'Ж': 'Zh', 'З': 'Z', 'И': 'I', 'Й': 'Y', 'К': 'K', 'Л': 'L', 'М': 'M',
        'Н': 'N', 'О': 'O', 'П': 'P', 'Р': 'R', 'С': 'S', 'Т': 'T', 'У': 'U',
        'Ф': 'F', 'Х': 'Kh', 'Ц': 'Ts', 'Ч': 'Ch', 'Ш': 'Sh', 'Щ': 'Sch', 'Ъ': '',
        'Ы': 'Y', 'Ь': '', 'Э': 'E', 'Ю': 'Yu', 'Я': 'Ya'
    };
    
    return text.split('').map(char => translitMap[char] || char).join('');
}

// Расширенная функция перевода районов с транслитерацией
window.translateAreaNameAdvanced = function(russianName) {
    // Сначала попробовать точный перевод
    const exactTranslation = window.translateAreaName(russianName);
    
    // Если есть точный перевод, вернуть его
    if (exactTranslation !== russianName) {
        return exactTranslation;
    }
    
    // Если точного перевода нет, сначала заменить основные русские слова
    let result = russianName;
    
    // Специальная обработка для "город" - переставить слова
    result = result.replace(/город\s+([^\s]+)/gi, '$1 city');
    result = result.replace(/г\.\s*([^\s]+)/gi, '$1 city');
    
    // Заменить основные русские слова на английские ПЕРЕД транслитерацией
    result = result.replace(/район/gi, 'district');
    result = result.replace(/область/gi, 'region');
    
    // Затем применить транслитерацию к оставшемуся тексту
    result = window.transliterate(result);
   
    return result;
}

// Функция получения текущего языка
window.getCurrentLanguage = function() {
    return window.currentLanguage || 'ru';
}

// Функция перевода ключей интерфейса
window.translate = function(key) {
    const lang = window.getCurrentLanguage();
    if (window.translations && window.translations[lang] && window.translations[lang][key]) {
        return window.translations[lang][key];
    }
    return key; // Если перевод не найден, вернуть оригинальный ключ
}

// Функция форматирования времени для разных языков
window.formatTimeString = function(hours, minutes) {
    const lang = window.getCurrentLanguage();
    
    if (lang === 'en') {
        if (hours > 0) {
            return `${hours}h ${minutes}min`;
        } else {
            return `${minutes}min`;
        }
    } else {
        if (hours > 0) {
            return `${hours}ч ${minutes}мин`;
        } else {
            return `${minutes}мин`;
        }
    }
}

// Функция форматирования даты для разных языков
window.formatDateString = function(dateObj) {
    const lang = window.getCurrentLanguage();
    
    if (lang === 'en') {
        return dateObj.toLocaleDateString('en-US');
    } else {
        return dateObj.toLocaleDateString('ru-RU');
    }
}

// Функция переключения языка
window.switchLanguage = function(newLang) {
    if (['ru', 'en'].includes(newLang)) {
        window.currentLanguage = newLang;
        
        // Обновить все элементы интерфейса с переводами
        updateInterfaceLanguage();
        
        // Обновить карту если нужно
        if (typeof window.rebuildClusters === 'function') {
            window.rebuildClusters();
        }
    }
}

// Функция обновления языка интерфейса
function updateInterfaceLanguage() {
    // Обновить все элементы с data-translate атрибутом
    const elementsToTranslate = document.querySelectorAll('[data-translate]');
    
    elementsToTranslate.forEach(element => {
        const key = element.getAttribute('data-translate');
        const translatedText = window.translate(key);
        
        if (element.tagName === 'INPUT' && element.type === 'text') {
            element.placeholder = translatedText;
        } else {
            element.textContent = translatedText;
        }
    });
} 