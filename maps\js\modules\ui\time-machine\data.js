// Машина времени - тарихий маълумотлар модули

// Загрузка исторических данных
async function loadHistoricalData(dateTime) {
    if (!dateTime) return;

    try {
        // Показываем индикатор загрузки
        if (window.showLoader) {
            window.showLoader();
        }

        // Форматируем дату в локальный формат как в монолите
        const localDateTimeString = dateTime.getFullYear() + '-' +
            String(dateTime.getMonth() + 1).padStart(2, '0') + '-' +
            String(dateTime.getDate()).padStart(2, '0') + ' ' +
            String(dateTime.getHours()).padStart(2, '0') + ':' +
            String(dateTime.getMinutes()).padStart(2, '0') + ':' +
            String(dateTime.getSeconds()).padStart(2, '0');



        const response = await fetch('/map/api/historical-alarms/?datetime=' + encodeURIComponent(localDateTimeString));
        const historicalAlarms = await response.json();

        if (response.ok) {
            // Тарихий маълумотларни жорий БС маълумотлари билан бирлаштириш
            const currentBsData = window.originalBsData || window.globalStations;
            const historicalData = combineHistoricalData(currentBsData, historicalAlarms);

            // Обновляем глобальные данные
            window.globalStations = historicalData;
            window.points = historicalData; // Для совместимости

            // Применяем текущие фильтры (вилоят, туман, статус, duration)
            if (window.applyCurrentFilters) {
                window.applyCurrentFilters();
            }

            // Обновляем статистику - просто используем обычную функцию!
            if (window.updateRegionsStats) {
                window.updateRegionsStats();
            } else {
                console.error('❌ updateRegionsStats функциясини топа олмадим!');
            }
            
            // Районлар статистикасини янгилаш (агар регион танланган бўлса)
            const currentRegionId = document.getElementById('region').value;
            if (currentRegionId && window.updateAreasStats) {
                window.updateAreasStats(currentRegionId);
            }
        } else {
            console.error('Тарихий маълумотларни юклашда хато:', historicalAlarms.error);
            alert('Тарихий маълумотларни юклашда хато: ' + (historicalAlarms.error || 'Номаълум хато'));
        }
    } catch (error) {
        console.error('Тарихий маълумотларни юклашда хато:', error);
        alert('Тарихий маълумотларни юклашда хато: ' + error.message);
    } finally {
        // Скрываем индикатор загрузки
        setTimeout(() => {
            if (window.hideLoader) {
                window.hideLoader();
            } else {
                // Если функция не найдена, скрываем loader напрямую
                const loader = document.getElementById('loader');
                if (loader) {
                    loader.style.opacity = '0';
                    loader.style.visibility = 'hidden';
                    loader.style.display = 'none';
                }
            }
        }, 100); // Небольшая задержка для гарантии
    }
}

// Функцияни window объектига assign қилиш
window.loadHistoricalData = loadHistoricalData;

// Функция для получения номера БС из имени
function getNumberBS(bsname) {
    if (!bsname) return null;

    // Если имя содержит подчеркивание, берем последнюю часть
    if (bsname.includes('_')) {
        return bsname.split('_').pop();
    }

    // Иначе ищем числа в конце строки
    const match = bsname.match(/\d+$/);
    return match ? match[0] : null;
}

// Объединение исторических и текущих данных
function combineHistoricalData(currentBsData, historicalAlarms) {

    // Барча БС ларни қайтариш стратегияси
    const result = [];

    // БС по имени и номеру для исторических аварий
    const alarmsByBsName = {};
    const alarmsByBsNumber = {};

    historicalAlarms.forEach(alarm => {
        const bsName = alarm.bsname;
        const bsNumber = alarm.bsnumber;

        if (!alarmsByBsName[bsName]) {
            alarmsByBsName[bsName] = [];
        }
        alarmsByBsName[bsName].push(alarm);

        if (!alarmsByBsNumber[bsNumber]) {
            alarmsByBsNumber[bsNumber] = [];
        }
        alarmsByBsNumber[bsNumber].push(alarm);
    });

    // Барча БС ларни кўриб чиқиш
    currentBsData.forEach(bs => {
        const bsName = bs.name || bs.bsName;
        const bsNumber = getNumberBS(bsName);

        // БС маълумотларини нусхалаш
        const bsData = {
            ...bs,
            status: false, // Бошланғич ҳолатда барча БС лар online
            calcTime: null,
            cabinetType: null,
            typeG: null,
            alarms_by_tech: {},
            historical: true
        };

        // Аварияларни текшириш
        let hasAlarms = false;
        const alarms = [];

        // Ном бўйича қидириш
        if (alarmsByBsName[bsName]) {
            alarms.push(...alarmsByBsName[bsName]);
            hasAlarms = true;
        }

        // Рақам бўйича қидириш (агар ном бўйича топилмаган бўлса)
        if (!hasAlarms && bsNumber && alarmsByBsNumber[bsNumber]) {
            alarms.push(...alarmsByBsNumber[bsNumber]);
            hasAlarms = true;
        }

        // Агар авариялар топилган бўлса
        if (hasAlarms && alarms.length > 0) {
            bsData.status = true;
            const technologies = [];

            // Ҳар бир технология учун авария маълумотларини йиғиш
            alarms.forEach(alarm => {
                const tech = alarm.technology;
                if (tech) {
                    technologies.push(tech);

                    // Технология бўйича авария маълумотларини сақлаш
                    bsData.alarms_by_tech[tech] = {
                        alarmname: alarm.alarmname,
                        appeartime: alarm.appeartime,
                        cleartime: alarm.cleartime,
                        duration_hours: alarm.duration_hours
                    };
                }
            });

            // Умумий маълумотлар
            bsData.typeG = technologies.length > 0 ? technologies.join('/') : null;
            bsData.cabinetType = get_cabinet_type(bsName);

            // Энг узоқ давом этган аварияни топиш
            if (alarms.length > 0) {
                const maxDuration = Math.max(...alarms.map(a => a.duration_hours || 0));
                const hours = Math.floor(maxDuration);
                const minutes = Math.floor((maxDuration - hours) * 60);
                bsData.calcTime = hours + ' hours ' + minutes + ' minutes';
                

            }
        }

        result.push(bsData);
    });

    return result;
}

// Функция для получения типа кабинета
function get_cabinet_type(bsname) {
    if (!bsname) return null;
    const lower = bsname.toLowerCase();
    if (lower.includes('lte')) return 'LTE';
    if (lower.includes('bts')) return 'BTS';
    if (lower.includes('node')) return 'NodeB';
    return null;
}

// Обновление статистики для машины времени
function updateRegionsStatsForTimeMachine() {
    try {
        
        // Регионлар статистикасини ҳисоблаш
        const regionStats = {};
        const totalStats = { total: 0, active: 0, inactive: 0 };

        // Барча БС ларни кўриб чиқиш
        if (!window.globalStations || window.globalStations.length === 0) {
            return;
        }
        
        window.globalStations.forEach(station => {
            const regionId = station.region_id;
            
            // Регион статистикасини инициализация қилиш
            if (!regionStats[regionId]) {
                regionStats[regionId] = { total: 0, active: 0, inactive: 0 };
            }

            // Регион ва умумий статистикани ҳисоблаш
            regionStats[regionId].total++;
            totalStats.total++;

            const isOffline = station.status === true || station.status === 'true' || station.status === 1 || station.status === '1';
            
            if (isOffline) {
                regionStats[regionId].inactive++;
                totalStats.inactive++;
            } else {
                regionStats[regionId].active++;
                totalStats.active++;
            }
        });
        


        // Текущий выбранный регион
        const currentRegionId = document.getElementById('region').value;

        // Жадвалдаги регион қаторларини янгилаш
        const regionRows = document.querySelectorAll('.region-row');
        regionRows.forEach(row => {
            const regionId = row.getAttribute('data-region-id');
            
            // Пропускаем строку "All regions" 
            if (regionId === '') {
                return;
            }
            
            const stats = regionStats[regionId] || { total: 0, active: 0, inactive: 0 };

            // Обновление ячеек региона
            const totalCell = row.querySelector('.region-total');
            const activeCell = row.querySelector('.region-active');
            const inactiveCell = row.querySelector('.region-inactive');
            const percentCell = row.querySelector('.region-percent');
            
            if (totalCell) totalCell.textContent = stats.total;
            if (activeCell) activeCell.textContent = stats.active;
            if (inactiveCell) inactiveCell.textContent = stats.inactive;
            
            // Расчет процента неактивных БС
            const inactivePercent = stats.total > 0 ? ((stats.inactive / stats.total) * 100).toFixed(1) : 0;
            if (percentCell) percentCell.textContent = inactivePercent + '%';

            // Выделение активного региона
            if (regionId == currentRegionId) {
                row.classList.add('active');
            } else {
                row.classList.remove('active');
            }
        });

        // Обновление строки "All regions"
        const allRegionsRow = document.querySelector('.region-row[data-region-id=""]');
        
        if (allRegionsRow) {
            const allTotalCell = allRegionsRow.querySelector('.all-regions-total');
            const allActiveCell = allRegionsRow.querySelector('.all-regions-active');
            const allInactiveCell = allRegionsRow.querySelector('.all-regions-inactive');
            const allPercentCell = allRegionsRow.querySelector('.all-regions-percent');
            
            if (allTotalCell) {
                allTotalCell.textContent = totalStats.total;
            }
            if (allActiveCell) {
                allActiveCell.textContent = totalStats.active;
            }
            if (allInactiveCell) {
                allInactiveCell.textContent = totalStats.inactive;
            }
            
            // Расчет процента для всех регионов
            const allInactivePercent = totalStats.total > 0 ? ((totalStats.inactive / totalStats.total) * 100).toFixed(1) : 0;
            if (allPercentCell) {
                allPercentCell.textContent = allInactivePercent + '%';
            }

            // Обновление названия при смене языка
            const nameCell = allRegionsRow.querySelector('.region-name');
            if (nameCell) {
                nameCell.textContent = window.currentLanguage === 'ru' ? 'Все регионы' : 'All regions';
            }

            // Выделение если не выбран конкретный регион
            if (!currentRegionId) {
                allRegionsRow.classList.add('active');
            } else {
                allRegionsRow.classList.remove('active');
            }
        }

        // Умумий статистикани янгилаш
        document.getElementById('total-bs-count').textContent = totalStats.total;
        document.getElementById('active-bs-count').textContent = totalStats.active;
        document.getElementById('inactive-bs-count').textContent = totalStats.inactive;

        // Downtime duration статистикасини ҳисоблаш ва янгилаш
        if (window.calculateDurationStatsForTimeMachine) {
            window.calculateDurationStatsForTimeMachine(window.globalStations);
        } else {
            // Агар махсус функция йўқ бўлса, умумий функцияни ишлатиш
            window.calculateDurationStats(window.globalStations);
        }
        
        // Районлар статистикасини янгилаш (агар регион танланган бўлса)
        const selectedRegionId = document.getElementById('region').value;
        if (selectedRegionId && window.updateAreasStats) {
            window.updateAreasStats(selectedRegionId);
        }
    } catch (error) {
        console.error('Error in updateRegionsStatsForTimeMachine:', error);
    }
}

// Функцияни window объектига assign қилиш
window.updateRegionsStatsForTimeMachine = updateRegionsStatsForTimeMachine;

// Машина времени учун downtime duration статистикасини ҳисоблаш
function calculateDurationStatsForTimeMachine(stations) {
    // Танланган вилоят ва туманни олиш
    const regionId = document.getElementById('region').value;
    const areaId = document.getElementById('area').value;
    
    // Фильтрлаш
    let filteredStations = [...stations];
    if (regionId) {
        filteredStations = filteredStations.filter(station => station.region_id == regionId);
    }
    if (areaId) {
        filteredStations = filteredStations.filter(station => station.area_id == areaId);
    }
    
    // Сброс статистики
    const durationStats = { '1': 0, '2': 0, '3': 0, '4': 0, '6': 0, '7': 0, '8': 0 };

    // Фильтрация только аварийных БС
    const offlineStations = filteredStations.filter(station => {
        return station.status === true || station.status === 'true' || station.status === 1 || station.status === '1';
    });

    // Группировка по продолжительности
    offlineStations.forEach(station => {
        const durationGroup = window.getDowntimeDurationForTimeMachine(station);
        if (durationGroup > 0) {
            durationStats[durationGroup]++;
        }
    });

    // Обновление отображения
    [1, 2, 3, 4, 6, 7, 8].forEach(i => {
        const countElement = document.getElementById('downtime-count-' + i);
        if (countElement) {
            countElement.textContent = durationStats[i] || 0;
        }
    });

    // Общее количество аварийных БС
    const totalOffline = Object.values(durationStats).reduce((acc, val) => acc + val, 0);
    const countAllElement = document.getElementById('downtime-count-all');
    if (countAllElement) {
        countAllElement.textContent = totalOffline;
    }

    return durationStats;
}

// Функцияни window объектига assign қилиш
window.calculateDurationStatsForTimeMachine = calculateDurationStatsForTimeMachine;

// Машина времени учун downtime duration ни аниқлаш
function getDowntimeDurationForTimeMachine(station) {
    if (!station.status || !station.alarms_by_tech) return 0;

    // Энг узоқ давом этган аварияни топиш
    let maxDurationHours = 0;

    Object.values(station.alarms_by_tech).forEach(alarm => {
        let durationHours = 0;
        
        // duration_hours майдони мавжуд бўлса
        if (alarm.duration_hours !== undefined && alarm.duration_hours !== null) {
            durationHours = parseFloat(alarm.duration_hours);
        }
        // duration майдони мавжуд бўлса
        else if (alarm.duration !== undefined && alarm.duration !== null) {
            if (typeof alarm.duration === 'object' && alarm.duration.total_seconds) {
                durationHours = alarm.duration.total_seconds() / 3600;
            } else if (typeof alarm.duration === 'string') {
                // Агар string форматда бўлса парс қилиш
                const match = alarm.duration.match(/(\d+)\s*hours?\s*(\d+)?\s*minutes?/i);
                if (match) {
                    durationHours = parseInt(match[1]) + (parseInt(match[2] || 0) / 60);
                }
            } else if (typeof alarm.duration === 'number') {
                durationHours = alarm.duration;
            }
        }
        // Агар duration йўқ бўлса, appeartime дан ҳисоблаш
        else if (alarm.appeartime && window.selectedDateTime) {
            const appearTime = new Date(alarm.appeartime);
            const duration = window.selectedDateTime - appearTime;
            durationHours = duration / (1000 * 60 * 60);
        }

        maxDurationHours = Math.max(maxDurationHours, durationHours);
    });

    // Группага ажратиш
    if (maxDurationHours <= 1) return 1;
    else if (maxDurationHours <= 2) return 2;
    else if (maxDurationHours <= 3) return 3;
    else if (maxDurationHours <= 4) return 4;
    else if (maxDurationHours <= 24) return 6;  // 4 соат - 1 кун
    else if (maxDurationHours <= 168) return 7; // 1 кун - 7 кун (168 соат = 7 кун)
    else return 8; // 7 кундан кўп
}

// Функцияни window объектига assign қилиш
window.getDowntimeDurationForTimeMachine = getDowntimeDurationForTimeMachine;

