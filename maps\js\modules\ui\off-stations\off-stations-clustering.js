// Off Stations кластерлаш модули - БС ларни қўшнилик бўйича группалаш
(function() {
    'use strict';
    
    // Конфигурация ва утилиталарни олиш
    const { CONFIG } = window.OffStationsConfig;
    const { calculateDistance, calculateGroupCenter, getGroupArea } = window.OffStationsUtils;

    // БС ларни қўшнилик бўйича группалаш
    function clusterOffStations(stations) {
        if (stations.length < CONFIG.MIN_STATIONS_TO_SHOW) return [];

        // Туман чегараларини ҳисобга олмасдан, фақат географик яқинлик бўйича кластерлаш
        const components = simpleClusterByProximity(stations);

        // Группаларни шакллантириш
        const groups = components
            .filter(component => component.length >= CONFIG.MIN_STATIONS_TO_SHOW)
            .map(component => ({
                stations: component,
                center: calculateGroupCenter(component),
                area: getGroupArea(component)
            }));

        return groups;
    }

    // БС ларни район бўйича группалаш
    function groupByArea(stations) {
        const areaGroups = new Map();
        
        stations.forEach(station => {
            const areaKey = `${station.area_id || 'unknown'}_${station.area_name || 'unknown'}`;
            
            if (!areaGroups.has(areaKey)) {
                areaGroups.set(areaKey, []);
            }
            
            areaGroups.get(areaKey).push(station);
        });
        
        return areaGroups;
    }

    // Содда кластерлаш - фақат яқинлик бўйича
    function simpleClusterByProximity(stations) {
        const visited = new Set();
        const clusters = [];
        
        for (let i = 0; i < stations.length; i++) {
            if (visited.has(i)) continue;
            
            const cluster = [];
            const toVisit = [i];
            
            while (toVisit.length > 0) {
                const currentIndex = toVisit.pop();
                if (visited.has(currentIndex)) continue;
                
                visited.add(currentIndex);
                cluster.push(stations[currentIndex]);
                
                // Жорий БС га яқин бошқа OFF БС ларни излаш
                for (let j = 0; j < stations.length; j++) {
                    if (!visited.has(j)) {
                        const distance = calculateDistance(
                            stations[currentIndex].lat, stations[currentIndex].lon,
                            stations[j].lat, stations[j].lon
                        );
                        
                        // 5 км ичидаги OFF БС лар автоматик бир кластерда
                        if (distance <= 5000) {
                            // ON БС текшириш (содда усул)
                            if (isDirectNeighbor(stations[currentIndex], stations[j])) {
                                toVisit.push(j);
                            }
                        }
                    }
                }
            }
            
            if (cluster.length > 0) {
                clusters.push(cluster);
            }
        }
        
        return clusters;
    }

    // Икки OFF БС тўғридан-тўғри қўшними (содда текшириш)
    function isDirectNeighbor(station1, station2) {
        const allStations = window.globalStations || [];
        const distance = calculateDistance(station1.lat, station1.lon, station2.lat, station2.lon);
        
        // Жуда яқин БС лар (1км дан кам) ҳар доим қўшни
        if (distance < 1000) {
            return true;
        }
        
        // Орада ON БС борми содда текшириш
        let onBsCount = 0;
        
        for (let station of allStations) {
            if (station.status !== true && station.id !== station1.id && station.id !== station2.id) {
                const dist1 = calculateDistance(station1.lat, station1.lon, station.lat, station.lon);
                const dist2 = calculateDistance(station2.lat, station2.lon, station.lat, station.lon);
                
                // Агар ON БС икки OFF БС орасида бўлса
                if (dist1 < distance && dist2 < distance) {
                    // Учбурчак тенгсизлиги
                    if (dist1 + dist2 < distance * 1.3) {
                        onBsCount++;
                        if (onBsCount >= 2) { // 2 та ёки кўп ON БС бўлса блоклайди
                            return false;
                        }
                    }
                }
            }
        }
        
        return true;
    }

    // Боғланган компонентларни топиш (DFS ёрдамида)
    function findConnectedComponents(stations, adjacencyList) {
        const visited = new Set();
        const components = [];

        stations.forEach((station, index) => {
            if (!visited.has(index)) {
                const component = [];
                dfs(index, adjacencyList, visited, component, stations);
                if (component.length > 0) {
                    components.push(component);
                }
            }
        });

        return components;
    }

    // Чуқурлик бўйича излаш (DFS)
    function dfs(index, adjacencyList, visited, component, stations) {
        visited.add(index);
        component.push(stations[index]);

        // Қўшниларни кўриб чиқиш
        const neighbors = adjacencyList.get(index) || [];
        neighbors.forEach(neighborIndex => {
            if (!visited.has(neighborIndex)) {
                dfs(neighborIndex, adjacencyList, visited, component, stations);
            }
        });
    }
    
    // Глобал объектга экспорт қилиш
    window.OffStationsClustering = {
        clusterOffStations: clusterOffStations
    };
})(); 