// РЦМУ Data модули - маълумот юклаш функциялари

(function() {
    'use strict';

    // Глобал ўзгарувчилар
    let rcmuData = [];

    // Конфигурация
    const CONFIG = {
        API_ENDPOINT: '/map/rcmu/api/data/',
        UPDATE_INTERVAL: 30000, // 30 секунд
    };

    // РЦМУ маълумотларини юклаш
    window.loadRcmuData = function() {
        if (!window.RcmuUI || !window.RcmuUI.isVisible()) return;

        fetch(CONFIG.API_ENDPOINT)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    console.error('РЦМУ маълумотлари юкланмади:', data.error);
                    return;
                }

                rcmuData = data.rcmu_data || [];
                
                // Маркерларни кўрсатиш
                if (window.showRcmuMarkers) {
                    window.showRcmuMarkers(rcmuData);
                }
            })
            .catch(error => {
                console.error('РЦМУ API хатоси:', error);
            });
    };

    // Маълумотларни олиш
    window.getRcmuData = function() {
        return rcmuData;
    };

    // Маълумотларни янгилаш
    window.refreshRcmuData = function() {
        window.loadRcmuData();
    };

})(); 