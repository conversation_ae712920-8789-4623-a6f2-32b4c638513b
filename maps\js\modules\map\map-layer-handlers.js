// Карта қатламлари ва манбалари бошқаруви

// Обновление слоя карты
function updateMapLayer() {
    // Удаление текущего слоя
    if (window.mymap.hasLayer(window.currentMapLayer)) {
        window.mymap.removeLayer(window.currentMapLayer);
    }
    if (window.mymap.hasLayer(window.currentSatelliteLayer)) {
        window.mymap.removeLayer(window.currentSatelliteLayer);
    }

    // Добавление нужного слоя
    if (window.isMapMode) {
        window.currentMapLayer.addTo(window.mymap);
    } else {
        window.currentSatelliteLayer.addTo(window.mymap);
    }

    // Синхронизация с Яндекс картой
    if (window.isYandexMode && window.yandexMap) {
        window.syncYandexMapWithLeaflet();
    }

    // Перерисовка маркеров для Яндекс карты
    if (window.isYandexMode) {
        window.applyCurrentFilters();
    }
}

// Обработчики изменения источника карты

// OSM источник
function handleOSMSourceClick() {
    document.getElementById('osm-source-button').classList.add('active');
    document.getElementById('yandex-source-button').classList.remove('active');
    document.getElementById('google-source-button').classList.remove('active');

    window.isYandexMode = false;

    if (window.yandexMapContainer) {
        window.yandexMapContainer.style.display = 'none';
    }

    window.currentMapLayer = window.osmMapLayer;
    window.currentSatelliteLayer = window.osmSatelliteLayer;

    // Удаление других слоев
    if (window.mymap.hasLayer(window.googleMapLayer)) {
        window.mymap.removeLayer(window.googleMapLayer);
    }
    if (window.mymap.hasLayer(window.googleSatelliteLayer)) {
        window.mymap.removeLayer(window.googleSatelliteLayer);
    }

    updateMapLayer();

    // Перерисовка маркеров
    redrawSearchMarkers();
    window.applyCurrentFilters();
}

// Яндекс источник
function handleYandexSourceClick() {
    document.getElementById('yandex-source-button').classList.add('active');
    document.getElementById('osm-source-button').classList.remove('active');
    document.getElementById('google-source-button').classList.remove('active');

    window.isYandexMode = true;

    // Проверка загрузки Яндекс API
    if (!window.isYandexAPILoaded) {
        initYandexMap();
    } else {
        window.currentMapLayer = window.yandexMapLayer;
        window.currentSatelliteLayer = window.yandexSatelliteLayer;
    }

    // Установка слоев Яндекс
    if (window.isMapMode) {
        if (window.yandexMap) {
            window.yandexMap.setType('yandex#map');
        }
        if (window.yandexMapLayer) {
            window.yandexMapLayer.addTo(window.mymap);
        }
    } else {
        if (window.yandexMap) {
            window.yandexMap.setType('yandex#satellite');
        }
        if (window.yandexSatelliteLayer) {
            window.yandexSatelliteLayer.addTo(window.mymap);
        }
    }

    // Удаление других слоев
    if (window.mymap.hasLayer(window.osmMapLayer)) {
        window.mymap.removeLayer(window.osmMapLayer);
    }
    if (window.mymap.hasLayer(window.osmSatelliteLayer)) {
        window.mymap.removeLayer(window.osmSatelliteLayer);
    }
    if (window.mymap.hasLayer(window.googleMapLayer)) {
        window.mymap.removeLayer(window.googleMapLayer);
    }
    if (window.mymap.hasLayer(window.googleSatelliteLayer)) {
        window.mymap.removeLayer(window.googleSatelliteLayer);
    }

    // Показ контейнера Яндекс
    if (window.yandexMapContainer) {
        window.yandexMapContainer.style.display = 'block';
    }

    // Синхронизация
    window.syncYandexMapWithLeaflet();

    // Перерисовка маркеров
    redrawSearchMarkers();
    window.applyCurrentFilters();
}

// Google источник
function handleGoogleSourceClick() {
    document.getElementById('google-source-button').classList.add('active');
    document.getElementById('osm-source-button').classList.remove('active');
    document.getElementById('yandex-source-button').classList.remove('active');

    window.isYandexMode = false;

    if (window.yandexMapContainer) {
        window.yandexMapContainer.style.display = 'none';
    }

    window.currentMapLayer = window.googleMapLayer;
    window.currentSatelliteLayer = window.googleSatelliteLayer;

    // Удаление OSM слоев
    if (window.mymap.hasLayer(window.osmMapLayer)) {
        window.mymap.removeLayer(window.osmMapLayer);
    }
    if (window.mymap.hasLayer(window.osmSatelliteLayer)) {
        window.mymap.removeLayer(window.osmSatelliteLayer);
    }

    updateMapLayer();

    // Перерисовка маркеров
    redrawSearchMarkers();
    window.applyCurrentFilters();
}

// Перерисовка поисковых маркеров при смене источника
function redrawSearchMarkers() {
    if (window.searchMarker) {
        const latlng = window.searchMarker.getLatLng();
        // Фақат displayName ни сақлаймиз, "Search Result:" қўшмаймиз
        const displayName = window.searchMarker.displayName || '';

        window.mymap.removeLayer(window.searchMarker);
        window.displaySearchResult(latlng.lat, latlng.lng, displayName);
    }
}

// Export функций для использования в других модулях
window.updateMapLayer = updateMapLayer;
window.handleOSMSourceClick = handleOSMSourceClick;
window.handleYandexSourceClick = handleYandexSourceClick;
window.handleGoogleSourceClick = handleGoogleSourceClick; 