/* Вақт машинаси навигация элементлари */

/* Контролы времени в ряд */
.time-controls-row {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.time-control {
    flex: 1;
}

/* Навигационные кнопки времени */
.time-navigation {
    display: flex;
    gap: 3px;
    margin-top: 8px;
    justify-content: center;
}

.time-nav-btn {
    padding: 4px 8px;
    border: 1px solid #ddd;
    background: #f8f9fa;
    color: #333;
    cursor: pointer;
    font-size: 11px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.time-nav-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.time-nav-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    background: #dee2e6;
}

.time-nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.time-now-btn {
    background: #4CAF50;
    color: white;
    border-color: #4CAF50;
    font-weight: 500;
}

.time-now-btn:hover {
    background: #45a049;
    border-color: #45a049;
}

.time-now-btn:active {
    background: #388e3c;
    border-color: #388e3c;
}

/* Индикатор активной машины времени */
.time-machine-active-indicator {
    position: fixed;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #ff9800;
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    z-index: 2500;
    box-shadow: 0 4px 12px rgba(255, 152, 0, 0.4);
    display: none;
    animation: pulse 2s infinite;
    pointer-events: none;
}

@keyframes pulse {
    0% {
        box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
    }
    50% {
        box-shadow: 0 2px 15px rgba(255, 152, 0, 0.5);
    }
    100% {
        box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
    }
}

.time-machine-active-indicator.show {
    display: block;
}

/* Time Machine тугмаси */
.time-machine-toggle-btn {
    position: absolute;
    top: 10px;
    left: 60px;  /* Яна 30px ўнгга сурдик */
    z-index: 1100;
}

.time-machine-btn {
    width: 36px;
    height: 36px;
    border: 2px solid rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    background-color: white;
    color: #333;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    position: relative;
}

.time-machine-btn:hover {
    background-color: #f0f0f0;
    transform: scale(1.05);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.time-machine-btn.active {
    background-color: #ff9800;
    color: white;
    border-color: #ff9800;
    animation: timePulse 2s ease-in-out infinite;
}

@keyframes timePulse {
    0%, 100% {
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
    50% {
        box-shadow: 0 2px 10px rgba(255, 152, 0, 0.4);
    }
}

/* Custom tooltip */
.time-machine-btn .tooltip-text {
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.time-machine-btn:hover .tooltip-text {
    opacity: 1;
}

.time-machine-btn svg {
    width: 20px;
    height: 20px;
}

/* Floating контейнер */
.time-machine-floating-container {
    position: absolute;
    top: 10px;
    left: 105px;  /* Тугма позициясига мос - яна 30px ўнгга */
    width: 480px;  /* Кенгроқ қилдик */
    background-color: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.12);
    z-index: 1200;
    display: none;
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
    transition: all 0.3s ease;
    padding: 8px;
    border: 1px solid rgba(0, 0, 0, 0.08);
}

.time-machine-floating-container.show {
    display: block;
    opacity: 1;
    transform: translateY(0) scale(1);
}

.time-machine-floating-container:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
} 