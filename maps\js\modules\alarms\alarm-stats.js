// Alarm Stats Module - Alarm Manager модулининг статистика бошқариш функциялари

window.AlarmStats = (function() {
    'use strict';
    
    // Статистикани янгилаш
    async function updateAlarmStatistics(selectedAlarmTypes, isManual = true, isFromFilter = false, filteredData) {
        // Loader ни фақат manual режимда ва фильтрдан келмаганда кўрсатиш
        if (isManual && !isFromFilter && window.showLoader) {
            window.showLoader();
        }
        
        try {
            // Фақат битта статистика сўрови юборамиз
            // Backend томонидан ҳам глобал, ҳам фильтрланган статистикани оламиз
            
            const params = new URLSearchParams();
            selectedAlarmTypes.forEach(type => params.append('alarm_types[]', type));
            
            const regionId = document.getElementById('region').value;
            const areaId = document.getElementById('area').value;
            if (regionId) params.append('region_id', regionId);
            if (areaId) params.append('area_id', areaId);
            
            if (window.timeMachineDate) {
                params.append('datetime', window.timeMachineDate.toISOString());
            }
            
            // Статистикани олиш
            const response = await fetch(`/map/alarms/api/alarm-statistics/?${params}`);
            const stats = await response.json();
            
            // UI ни янгилаш - фильтрланган маълумотларни ҳам узатамиз
            updateAlarmStatsUI(stats, filteredData);
            
        } catch (error) {
            console.error('[AlarmStats] Error loading statistics:', error);
        } finally {
            // Loader ни фақат manual режимда ва фильтрдан келмаганда яшириш
            if (isManual && !isFromFilter && window.hideLoader) {
                window.hideLoader();
            }
        }
    }
    
    // Статистика UI ни янгилаш
    function updateAlarmStatsUI(stats, filteredData) {
        // Статистика маълумотлари
        const durationStats = stats;
        
        // Sidebar статистикасини янгилаш (фильтрланган)
        const offlineCount = document.querySelector('.stat-value.offline-count');
        if (offlineCount) {
            // Агар duration filter танланган бўлса, фильтрланган маълумотлардан санаймиз
            if (filteredData && filteredData.stations) {
                offlineCount.textContent = filteredData.stations.length || 0;
            } else {
                offlineCount.textContent = durationStats.total_alarms || 0;
            }
        }
        
        // Duration stats ни янгилаш
        updateDurationStats(durationStats.duration_stats || {});
        
        // Регион статистикасини янгилаш
        updateRegionStats(durationStats.region_stats || {});
        
        // Район статистикасини янгилаш
        updateAreaStats(durationStats.area_stats || {});
    }
    
    // Duration statistics янгилаш
    function updateDurationStats(durationStats) {
        // Duration categories
        const categories = {
            '1': 'До 1 ч',
            '2': '1-2 ч',
            '3': '2-3 ч',
            '4': '3-4 ч',
            '6': '4ч-1д',
            '7': '1д-7д',
            '8': '7д+'
        };
        
        Object.keys(categories).forEach(categoryId => {
            const element = document.querySelector(`.downtime-item[data-duration="${categoryId}"] .count`);
            if (element) {
                element.textContent = durationStats[categoryId] || 0;
            }
        });
    }
    
    // Регион статистикасини янгилаш
    function updateRegionStats(regionStats) {
        // Регион статистикасини янгилаш логикаси
        if (regionStats && typeof window.updateRegionsTable === 'function') {
            window.updateRegionsTable(regionStats);
        }
        
        console.log('[AlarmStats] Region stats updated:', regionStats);
    }
    
    // Район статистикасини янгилаш
    function updateAreaStats(areaStats) {
        // Район статистикасини янгилаш логикаси
        const currentRegionId = document.getElementById('region').value;
        if (areaStats && currentRegionId && typeof window.updateAreasTable === 'function') {
            // Backend нестед формат юборади: region_id -> area_id -> stats
            const regionAreaStats = areaStats[currentRegionId] || {};
            window.updateAreasTable(regionAreaStats, currentRegionId);
        }
        
        console.log('[AlarmStats] Area stats updated:', areaStats);
    }
    
    // Локал duration статистикасини ҳисоблаш
    function calculateLocalDurationStats(stations) {
        const localStats = {
            '1': 0,
            '2': 0,
            '3': 0,
            '4': 0,
            '6': 0,
            '7': 0,
            '8': 0
        };
        
        if (!stations || stations.length === 0) return localStats;
        
        stations.forEach(station => {
            if (station.other_alarms && station.other_alarms.length > 0) {
                const maxDuration = Math.max(...station.other_alarms.map(a => a.duration_hours || 0));
                
                if (maxDuration <= 1) {
                    localStats['1']++;
                } else if (maxDuration <= 2) {
                    localStats['2']++;
                } else if (maxDuration <= 3) {
                    localStats['3']++;
                } else if (maxDuration <= 4) {
                    localStats['4']++;
                } else if (maxDuration <= 24) {
                    localStats['6']++;
                } else if (maxDuration <= 168) {
                    localStats['7']++;
                } else {
                    localStats['8']++;
                }
            }
        });
        
        return localStats;
    }
    
    // Normal duration legend ни қайта тиклаш
    function restoreNormalDurationLegend() {
        // Duration legend ни normal ҳолатига қайтариш
        const durationItems = document.querySelectorAll('.downtime-item');
        durationItems.forEach(item => {
            item.classList.remove('active');
        });
    }
    
    // Public API
    return {
        updateAlarmStatistics,
        updateAlarmStatsUI,
        calculateLocalDurationStats,
        restoreNormalDurationLegend
    };
})(); 