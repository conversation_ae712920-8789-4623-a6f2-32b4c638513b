// Кластер асосий модули - Core clustering logic

// Глобал кластер переменналари
window.bsMarkerCluster = null;
window.branchClusters = new Map(); // Ҳар бир филиал учун алоҳида кластер
window.regionClusters = new Map(); // Ҳар бир вилоят учун алоҳида кластер
window.areaClusters = new Map(); // Ҳар бир туман учун алоҳида кластер
window.currentClusterMode = null; // 'branches', 'regions', 'areas', 'none'

// Зум даражасига қараб кластер режимини аниқлаш
function getClusterModeByZoom(zoom) {
    if (zoom < 8) {
        return 'branches';  // Филиаллар бўйича (zoom 5-7)
    } else if (zoom >= 8 && zoom < 10) {
        return 'regions';   // Вилоятлар бўйича (zoom 8-9)
    } else if (zoom >= 10 && zoom < 12) {
        return 'areas';     // Туманлар бўйича (zoom 10-11)
    } else {
        return 'none';      // Кластерсиз (zoom 12+)
    }
}

// Кластер initialize қилиш - ЛОГИК кластерлаш
window.initializeClusterSystem = function() {
    // Ҳар бир филиал учун алоҳида кластер группа яратиш
    window.branchClusters = window.branchClusters || new Map();
    window.regionClusters = window.regionClusters || new Map();
    window.areaClusters = window.areaClusters || new Map();
    
    window.branchClusters.clear();
    window.regionClusters.clear();
    window.areaClusters.clear();
    
    // Зум ўзгарганда кластерни янгилаш
    window.mymap.on('zoomend', function() {
        updateClustersByZoom();
    });
    
    // Бошланғич кластер режимини ўрнатиш (фақат режимни белгилаш, кластер эмас)
    const currentZoom = window.mymap.getZoom();
    window.currentClusterMode = getClusterModeByZoom(currentZoom);
}

// Зум асосида кластерларни янгилаш
function updateClustersByZoom() {
    const currentZoom = window.mymap.getZoom();
    const newMode = getClusterModeByZoom(currentZoom);
    
    if (newMode !== window.currentClusterMode) {
        window.currentClusterMode = newMode;
        window.rebuildClusters();
    }
}

// Кластерларни қайта қуриш - ЛОГИК группалаш
window.rebuildClusters = function() {
    // Олдинги кластерларни тозалаш
    clearAllClusters();
    
    // Жорий маркерларни олиш
    const allMarkers = window.pointLayers || [];
    
    if (allMarkers.length === 0) {
        return;
    }
    
    if (window.currentClusterMode === 'branches') {
        window.buildBranchClusters(allMarkers);
    } else if (window.currentClusterMode === 'regions') {
        window.buildRegionClusters(allMarkers);
    } else if (window.currentClusterMode === 'areas') {
        window.buildAreaClusters(allMarkers);
    } else {
        // Кластерсиз режим - барча маркерларни тўғридан-тўғри кўрсатиш
        allMarkers.forEach(marker => {
            if (!window.mymap.hasLayer(marker)) {
                window.mymap.addLayer(marker);
            }
        });
    }
}

// Барча кластерларни тозалаш
function clearAllClusters() {
    // Филиал кластерларини тозалаш
    window.branchClusters.forEach(cluster => {
        if (window.mymap.hasLayer(cluster)) {
            window.mymap.removeLayer(cluster);
        }
    });
    window.branchClusters.clear();
    
    // Вилоят кластерларини тозалаш
    window.regionClusters.forEach(cluster => {
        if (window.mymap.hasLayer(cluster)) {
            window.mymap.removeLayer(cluster);
        }
    });
    window.regionClusters.clear();
    
    // Туман кластерларини тозалаш
    window.areaClusters.forEach(cluster => {
        if (window.mymap.hasLayer(cluster)) {
            window.mymap.removeLayer(cluster);
        }
    });
    window.areaClusters.clear();
    
    // Ялғиз маркерларни ҳам тозалаш
    window.pointLayers.forEach(marker => {
        if (window.mymap.hasLayer(marker)) {
            window.mymap.removeLayer(marker);
        }
    });
}

// Маркерларни кластерга қўшиш - ЯНГИ ЛОГИК
window.addMarkersToCluster = function(markers) {
    if (!markers || markers.length === 0) return;
    
    // pointLayers ни янгилаш
    window.pointLayers.length = 0;
    window.pointLayers.push(...markers);
    
    // Кластерларни қайта қуриш
    window.rebuildClusters();
}

// Кластерларни тозалаш - ЯНГИ ЛОГИК
window.clearClusters = function() {
    clearAllClusters();
}

// Кластер статистикасини олиш - ЯНГИ ЛОГИК
window.getClusterStats = function() {
    if (!window.pointLayers || window.pointLayers.length === 0) {
        return { total: 0, offline: 0 };
    }
    
    let total = window.pointLayers.length;
    let offline = 0;
    
    window.pointLayers.forEach(function(marker) {
        const pointData = marker.options.pointData;
        if (pointData.status === true || pointData.status === 'true' || 
            pointData.status === 1 || pointData.status === '1') {
            offline++;
        }
    });
    
    return { total, offline };
}

// Регион ёки туман бўйича кластерлаш (кейинги версия учун)
window.groupMarkersByLocation = function(markers, groupBy = 'region') {
    const groups = new Map();
    
    markers.forEach(marker => {
        const pointData = marker.options.pointData;
        let key;
        
        if (groupBy === 'region') {
            key = pointData.region_id || 'unknown';
        } else if (groupBy === 'area') {
            key = pointData.area_id || 'unknown';
        }
        
        if (!groups.has(key)) {
            groups.set(key, []);
        }
        groups.get(key).push(marker);
    });
    
    return groups;
}

// Маркерларни янгилаш функцияси - ЛОГИК кластер билан
window.updateMapMarkers = function(stations, isOtherAlarmsMode = false) {
    
    // Эски кластер ва маркерларни тозалаш
    clearAllClusters();
    window.pointLayers.length = 0;
    
    // Фильтрларни олиш
    const statusValue = document.getElementById('status').value;
    const bsSearchText = document.getElementById('bs-search').value;
    
    // БС номи бўйича фильтрлаш
    let filteredStations = stations;
    if (bsSearchText) {
        filteredStations = window.filterByBsName(stations, bsSearchText);
    }
    
    // Маркерларни яратиш
    let createdMarkers = 0;
    filteredStations.forEach(function(station) {
        const circleMarker = window.createBSMarker(station, statusValue, false, isOtherAlarmsMode);
        if (circleMarker) {
            window.pointLayers.push(circleMarker);
            createdMarkers++;
        }
    });
    
    // Кластерларни қайта қуриш
    window.rebuildClusters();
    
    // Статистикани янгилаш
    const totalDisplayed = filteredStations.length;
    const inactiveDisplayed = filteredStations.filter(point => {
        return point.status === true || point.status === 'true' || point.status === 1 || point.status === '1';
    }).length;
    const activeDisplayed = totalDisplayed - inactiveDisplayed;
    
    document.getElementById('total-bs-count').textContent = totalDisplayed;
    document.getElementById('active-bs-count').textContent = activeDisplayed;
    document.getElementById('inactive-bs-count').textContent = inactiveDisplayed;
}; 