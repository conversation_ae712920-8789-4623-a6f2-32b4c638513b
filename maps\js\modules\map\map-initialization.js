// Карта инициализация модули

// Инициализация карты
function initializeMap() {
    // Создание карты Leaflet
    window.mymap = L.map('mapid', {
        preferCanvas: true,
        renderer: L.canvas({ padding: 0.5 }),
        center: [41.3, 69.3], // Марказни бирданига белгилаш
        zoom: 5  // Филиалларни кўриш учун zoom 5
    });

    // Карта тўлиқ юкланганидан кейин қўшимча тўғрилаш
    window.mymap.whenReady(function () {
        // Карта размерини янгилаш
        window.mymap.invalidateSize();
        // Марказни яна бир марта белгилаш
        setTimeout(function () {
            window.mymap.setView([41.3, 69.3], 5);
        }, 100);
    });

    // Границы Узбекистана
    const uzbekistanBounds = [
        [37.1, 55.9], // Юго-западная точка
        [45.6, 73.1]  // Северо-восточная точка
    ];
    // maxBounds ўрнатмаймиз, фақат minZoom ва maxZoom чегараларини белгилаймиз
    // window.mymap.setMaxBounds(uzbekistanBounds);

    // Минимал ва максимал зум даражаларини белгилаш
    window.mymap.setMinZoom(5);
    window.mymap.setMaxZoom(18);

    // Ўзбекистон чегараларини сақлаш (кейинчалик фойдаланиш учун)
    window.uzbekistanBounds = L.latLngBounds(uzbekistanBounds);

    // Обработчик изменения масштаба
    window.mymap.on('zoomend', window.updateCircleSizes);

    // Синхронизация с Яндекс картой
    window.mymap.on('moveend', function () {
        if (window.isYandexMode && window.yandexMap) {
            window.syncYandexMapWithLeaflet();
        }
    });

    // Карта ҳаракатланганда чекириш
    window.mymap.on('moveend', function () {
        checkIfOutsideUzbekistan();
    });
}

// Карта Ўзбекистон чегарасидан чиққанлигини текшириш
function checkIfOutsideUzbekistan() {
    const returnBtn = document.getElementById('return-to-uzbekistan');
    if (!returnBtn || !window.uzbekistanBounds) return;

    const currentBounds = window.mymap.getBounds();
    const currentCenter = window.mymap.getCenter();

    // Агар марказ Ўзбекистон чегарасидан ташқарида бўлса
    if (!window.uzbekistanBounds.contains(currentCenter)) {
        returnBtn.style.display = 'block';
    } else {
        returnBtn.style.display = 'none';
    }
}

// Export функции для использования в других модулях
window.initializeMap = initializeMap;
window.checkIfOutsideUzbekistan = checkIfOutsideUzbekistan; 