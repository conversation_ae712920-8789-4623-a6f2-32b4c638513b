/* Вақт машинаси - асосий контейнер стиллари */

.time-machine-container {
    margin-bottom: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    padding: 8px;
    background-color: #f9f9f9;
    transition: all 0.3s ease;
}

/* Активное состояние машины времени */
.time-machine-container.active {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.2);
}

.time-machine-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.time-machine-header label {
    font-size: 13px;
    font-weight: 500;
    margin-right: 10px;
    color: #333;
}

.time-machine-toggle {
    display: none;
}

.time-machine-controls {
    display: none;
}

.time-machine-controls .filter-group {
    margin-bottom: 6px;
}

.time-machine-controls .filter-group label {
    font-size: 10px;
}

.time-machine-controls input,
.time-machine-controls select {
    width: 100%;
    padding: 4px 6px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 11px;
    height: 28px;
}

/* Отображение текущего времени */
.current-time-display {
    margin-top: 8px;
    padding: 6px 10px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    text-align: center;
    font-size: 12px;
    font-weight: 500;
    color: #495057;
    transition: all 0.3s ease;
}

.time-machine-container.active .current-time-display {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.time-machine-body {
    padding: 0;
}

/* Time machine checkbox блокланганда */
.time-machine-toggle input[type="checkbox"]:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.time-machine-toggle input[type="checkbox"]:disabled + label {
    opacity: 0.5;
    cursor: not-allowed;
    color: #999;
}

/* Блокланган контейнер */
.time-machine-container.disabled {
    opacity: 0.7;
    pointer-events: none;
} 