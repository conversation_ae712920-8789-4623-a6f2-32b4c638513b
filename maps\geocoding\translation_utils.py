"""
Вилоят ва туман номларини таржима қилиш утилиталари
"""


def translate_region_name(russian_name):
    """Вилоят номларини инглизчага таржима қилиш"""
    region_translations = {
        'Ташкент город': 'Tashkent City',
        'город Ташкент': 'Tashkent city',
        'Андижанская область': 'Andijan Region',
        'Ферганская область': 'Fergana Region',
        'Наманганская область': 'Namangan Region',
        'Бухарская область': 'Bukhara Region',
        'Джиззахская область': 'Jizzakh Region',
        'Кашкадарьинская область': 'Kashkadarya Region',
        'Наваийская область': 'Navoi Region',
        'Ташкентская область': 'Tashkent Region',
        'Сырдарьинская область': 'Sirdarya Region',
        'Сурхандарьинская область': 'Surkhandarya Region',
        'Хорезмская область': 'Khorezm Region',
        'Рес.Каракалпакстан': 'Republic of Karakalpakstan',
        'Самаркандская область': 'Samarkand Region'
    }
    return region_translations.get(russian_name, russian_name)


def translate_area_name(russian_name):
    """Туман номларини инглизчага таржима қилиш"""
    # Асосий шаҳарлар
    city_translations = {
        'город Ташкент': 'Tashkent city',
        'город Нукус': 'Nukus city',
        'город Самарканд': 'Samarkand city',
        'город Бухара': 'Bukhara city',
        'город Андижан': 'Andijan city',
        'город Фергана': 'Fergana city',
        'город Наманган': 'Namangan city',
        'город Карши': 'Qarshi city',
        'город Термез': 'Termez city',
        'город Ургенч': 'Urgench city',
        'город Навои': 'Navoi city',
        'город Джизак': 'Jizzakh city',
        'город Коканд': 'Kokand city',
        'город Маргилан': 'Margilan city',
        'город Гулистан': 'Gulistan city',
    }
    
    # Агар шаҳар бўлса
    if russian_name in city_translations:
        return city_translations[russian_name]
    
    # Агар район бўлса, оддий транслитерация
    if 'район' in russian_name:
        # Район сўзини district га алмаштириш
        return russian_name.replace('район', 'district').replace('ский', '')
    
    return russian_name 