"""
RCMU API функциялар модули
"""
from django.http import JsonResponse
from ..models import RcmuData


def rcmu_api_data(request):
    """
    API для получения данных РЦМУ для карты
    """
    # Проверка прав доступа (картада ишлаши учун вақтинча ўчирилди)
    # if not check_rcmu_permission(request.user):
    #     return JsonResponse({'error': 'У вас нет прав доступа к этой функции.'}, status=403)
    
    # Получаем только записи с координатами
    rcmu_data = RcmuData.objects.filter(lat__isnull=False, lon__isnull=False).exclude(lat='', lon='')
    
    data = []
    for item in rcmu_data:
        data.append({
            'id': item.id,
            'site': item.site,
            'bs_name': item.bs_name,
            'lat': item.lat.replace(',', '.') if item.lat else None,
            'lon': item.lon.replace(',', '.') if item.lon else None,
            'region': item.region,
            'region_id': item.region_id,
            'branch': item.branch,
            'cause_ru': item.cause_ru,
            'cause_eng': item.cause_eng,
            'responsible': item.responsible,
            'quality_comment': item.quality_comment,
            'date_lego': item.date_lego.strftime('%Y-%m-%d') if item.date_lego else None,
        })
    
    return JsonResponse({'rcmu_data': data}) 