from datetime import datetime
from django.db.models import Q
from django.utils import timezone
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from bsview.models import BsBeeline, Log_Alarms_full, RegionUzb, AreaUzb

@api_view(['GET'])
def get_other_alarms_statistics(request):
    """Танланган авария турлари бўйича статистика"""
    try:
        alarm_types = request.query_params.getlist('alarm_types[]')
        region_id = request.query_params.get('region_id')
        area_id = request.query_params.get('area_id')
        datetime_str = request.query_params.get('datetime')
        
        if not alarm_types:
            return Response({
                'error': 'alarm_types parameter is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Вақтни аниқлаш
        if datetime_str:
            try:
                target_datetime = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
                if target_datetime.tzinfo:
                    target_datetime = target_datetime.replace(tzinfo=None)
            except:
                target_datetime = timezone.now()
        else:
            target_datetime = timezone.now()
        
        # Актив аварияларни олиш
        query = Q(alarmname__in=alarm_types) & Q(appeartime__lte=target_datetime) & (
            Q(cleartime__isnull=True) | Q(cleartime__gt=target_datetime)
        )
        
        active_alarms = Log_Alarms_full.objects.filter(query)
        
        # Давомийлик бўйича статистика
        duration_stats = {
            '1': 0,  # До 1 часа
            '2': 0,  # До 2 часов
            '3': 0,  # До 3 часов
            '4': 0,  # До 4 часов
            '5': 0   # Более 4 часов
        }
        
        # Авария турлари бўйича статистика
        alarm_type_stats = {}
        
        # Регион бўйича статистика
        region_stats = {}
        area_stats = {}  # Район статистикаси (янги)
        bs_with_alarms_global = set()  # Глобал авария бўлган БС лар (фильтрсиз)
        bs_with_alarms_filtered = set()  # Фильтрланган авария бўлган БС лар
        
        # Барча регионларни олиш
        all_regions = RegionUzb.objects.all()
        for region in all_regions:
            if region.name not in ['Рес. Узбекистан', 'Респ. Узбекистан']:
                region_stats[str(region.id)] = {
                    'region_name': region.name,
                    'total_bs': 0,
                    'bs_with_alarms': 0,
                    'percentage': 0
                }
                area_stats[str(region.id)] = {}  # Ҳар бир регион учун районлар контейнери
        
        # Барча районларни олиш ва инициализация қилиш
        all_areas = AreaUzb.objects.all()
        for area in all_areas:
            if area.region_id and str(area.region_id) in area_stats:
                area_stats[str(area.region_id)][str(area.id)] = {
                    'area_name': area.name,
                    'total_bs': 0,
                    'bs_with_alarms': 0,
                    'percentage': 0
                }
        
        # Барча БС ларни регионлар ва районлар бўйича санаш (фильтрсиз)
        all_bs = BsBeeline.objects.all()
        for bs in all_bs:
            if bs.region_id and str(bs.region_id) in region_stats:
                region_stats[str(bs.region_id)]['total_bs'] += 1
                
                # Район статистикаси
                if bs.area_id and str(bs.region_id) in area_stats and str(bs.area_id) in area_stats[str(bs.region_id)]:
                    area_stats[str(bs.region_id)][str(bs.area_id)]['total_bs'] += 1
        
        # Барча аварияларни глобал ҳисоблаш (фильтрсиз)
        for alarm in active_alarms:
            try:
                bs = BsBeeline.objects.get(bsnum=alarm.bsnumber)
                
                # Глобал рўйхатга қўшиш
                bs_with_alarms_global.add(bs.id)
                
                # Фильтрлар
                if region_id and str(bs.region_id) != str(region_id):
                    continue
                if area_id and str(bs.area_id) != str(area_id):
                    continue
                
                # Фильтрланган рўйхатга қўшиш
                bs_with_alarms_filtered.add(bs.id)
                    
            except BsBeeline.DoesNotExist:
                continue
        
        # Регион статистикасини ҳисоблаш (ГЛОБАЛ)
        for bs_id in bs_with_alarms_global:
            try:
                bs = BsBeeline.objects.get(id=bs_id)
                if bs.region_id and str(bs.region_id) in region_stats:
                    region_stats[str(bs.region_id)]['bs_with_alarms'] += 1
                
                # Район статистикаси
                if bs.region_id and bs.area_id:
                    if str(bs.region_id) in area_stats and str(bs.area_id) in area_stats[str(bs.region_id)]:
                        area_stats[str(bs.region_id)][str(bs.area_id)]['bs_with_alarms'] += 1
                        
            except BsBeeline.DoesNotExist:
                continue
        
        # Фоизларни ҳисоблаш
        for region_id, stats in region_stats.items():
            if stats['total_bs'] > 0:
                stats['percentage'] = round((stats['bs_with_alarms'] / stats['total_bs']) * 100, 1)
        
        # Район фоизларини ҳисоблаш
        for region_id, areas in area_stats.items():
            for area_id, stats in areas.items():
                if stats['total_bs'] > 0:
                    stats['percentage'] = round((stats['bs_with_alarms'] / stats['total_bs']) * 100, 1)
        
        # Умумий статистика - фильтрланган авария бўлган БС лар сони
        total_bs_with_alarms = len(bs_with_alarms_filtered)
        
        # Duration статистикасини БС лар бўйича ҳисоблаш
        # Ҳар бир БС учун энг узоқ давом этган аварияни олиш
        duration_stats_by_bs = {
            '1': 0,  # До 1 часа
            '2': 0,  # До 2 часов
            '3': 0,  # До 3 часов
            '4': 0,  # До 4 часов
            '5': 0   # Более 4 часов
        }
        
        # Ҳар бир БС учун максимал давомийликни топиш (БАРЧА БС лар учун)
        bs_max_durations_global = {}
        for alarm in active_alarms:
            try:
                bs = BsBeeline.objects.get(bsnum=alarm.bsnumber)
                
                # Давомийликни ҳисоблаш
                if alarm.appeartime:
                    duration = target_datetime - alarm.appeartime
                    hours = duration.total_seconds() / 3600
                    
                    # БС учун максимал давомийликни сақлаш
                    if bs.id not in bs_max_durations_global or hours > bs_max_durations_global[bs.id]:
                        bs_max_durations_global[bs.id] = hours
                        
            except BsBeeline.DoesNotExist:
                continue
        
        # БС лар бўйича duration статистикасини ҳисоблаш (БАРЧА БС лар учун)
        for bs_id in bs_with_alarms_global:
            if bs_id in bs_max_durations_global:
                hours = bs_max_durations_global[bs_id]
                if hours <= 1:
                    duration_stats_by_bs['1'] += 1
                elif hours <= 2:
                    duration_stats_by_bs['2'] += 1
                elif hours <= 3:
                    duration_stats_by_bs['3'] += 1
                elif hours <= 4:
                    duration_stats_by_bs['4'] += 1
                else:
                    duration_stats_by_bs['5'] += 1
        
        # Агар фильтр танланган бўлса, фильтрланган duration статистикасини ҳисоблаш
        duration_stats_filtered = {
            '1': 0,  # До 1 часа
            '2': 0,  # До 2 часов
            '3': 0,  # До 3 часов
            '4': 0,  # До 4 часов
            '5': 0   # Более 4 часов
        }
        
        if region_id or area_id:
            # Фильтрланган БС лар учун duration статистикаси
            for bs_id in bs_with_alarms_filtered:
                if bs_id in bs_max_durations_global:
                    hours = bs_max_durations_global[bs_id]
                    if hours <= 1:
                        duration_stats_filtered['1'] += 1
                    elif hours <= 2:
                        duration_stats_filtered['2'] += 1
                    elif hours <= 3:
                        duration_stats_filtered['3'] += 1
                    elif hours <= 4:
                        duration_stats_filtered['4'] += 1
                    else:
                        duration_stats_filtered['5'] += 1
        else:
            # Фильтр йўқ бўлса, глобал статистикани қўллаш
            duration_stats_filtered = duration_stats_by_bs
        
        return Response({
            'total_alarms': total_bs_with_alarms,
            'duration_stats': duration_stats_filtered,
            'alarm_type_stats': alarm_type_stats,
            'region_stats': region_stats,
            'area_stats': area_stats,
            'datetime': datetime_str if datetime_str else 'current'
        })
        
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 