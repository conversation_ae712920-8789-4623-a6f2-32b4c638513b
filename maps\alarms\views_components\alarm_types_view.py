from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from bsview.models import Log_Alarms_full

@api_view(['GET'])
def get_alarm_types(request):
    """Барча мавжуд авария турларини олиш"""
    try:
        # Log_Alarms_full жадвалидан уникал авария номларини олиш
        alarm_types = Log_Alarms_full.objects.values('alarmname').distinct().order_by('alarmname')
        
        # Фақат бўш бўлмаган қийматларни олиш
        alarm_list = [
            alarm['alarmname'] 
            for alarm in alarm_types 
            if alarm['alarmname'] and alarm['alarmname'].strip()
        ]
        
        return Response({
            'alarm_types': alarm_list,
            'count': len(alarm_list)
        })
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 