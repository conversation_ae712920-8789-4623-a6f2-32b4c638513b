// РЦМУ UI модули - UI ва toggle бошқариш функциялари

(function() {
    'use strict';

    // Глобал ўзгарувчилар
    let isRcmuVisible = false;

    // РЦМУ UI системасини инициализация қилиш
    window.initRcmuUI = function() {
        createToggleButton();
        attachEventListeners();
        restoreRcmuState();
    };

    // Toggle кнопка учун event listener
    function createToggleButton() {
        const toggleBtn = document.querySelector('.rcmu-no-traff-toggle-btn');
        if (toggleBtn) {
            // Тилга қараб title ўрнатиш
            const lang = window.currentLanguage || 'ru';
            toggleBtn.title = lang === 'en' ? 'RCMU - BS without traffic' : 'РЦМУ - БС без трафика';

            toggleBtn.addEventListener('click', toggleRcmuVisibility);
        }
    }

    // Event listener лар қўшиш
    function attachEventListeners() {
        // Авария режими ўзгарганда
        window.addEventListener('alarmModeChanged', handleAlarmModeChange);

        // Time Machine checkbox ўзгарганда
        const timeMachineCheckbox = document.getElementById('time-machine-enabled') ||
            document.getElementById('time-machine-checkbox');
        if (timeMachineCheckbox) {
            timeMachineCheckbox.addEventListener('change', handleTimeMachineChange);
        }

        // Off Stations Panel ўзгарганда
        window.addEventListener('offStationsPanelChanged', handleOffStationsPanelChange);
    }

    // РЦМУ маркерларни кўрсатиш/яшириш (асосий toggle)
    function toggleRcmuVisibility() {
        const toggleBtn = document.querySelector('.rcmu-no-traff-toggle-btn');

        // Агар кнопка disabled бўлса, ҳеч нарса қилмаймиз
        if (toggleBtn && toggleBtn.disabled) {
            return;
        }

        isRcmuVisible = !isRcmuVisible;

        if (isRcmuVisible) {
            toggleBtn.classList.add('active');
            if (window.loadRcmuData) {
                window.loadRcmuData();
            }
        } else {
            toggleBtn.classList.remove('active');
            if (window.hideRcmuMarkers) {
                window.hideRcmuMarkers();
            }
        }

        // Ҳолатни сақлаш
        localStorage.setItem('rcmuMarkersVisible', isRcmuVisible);
    }

    // РЦМУ ҳолатини тиклаш
    function restoreRcmuState() {
        const savedVisible = localStorage.getItem('rcmuMarkersVisible');
        const toggleBtn = document.querySelector('.rcmu-no-traff-toggle-btn');

        // Кнопка title ни тилга қараб ўрнатиш
        if (toggleBtn) {
            const lang = window.currentLanguage || 'ru';
            toggleBtn.title = lang === 'en' ? 'RCMU - BS without traffic' : 'РЦМУ - БС без трафика';
        }

        // Кўринувчанлик ҳолати
        if (savedVisible === 'true') {
            isRcmuVisible = true;
            if (toggleBtn) {
                toggleBtn.classList.add('active');
            }
            if (window.loadRcmuData) {
                window.loadRcmuData();
            }
        } else {
            isRcmuVisible = false;
        }
    }

    // Авария режими ўзгарганда
    function handleAlarmModeChange(event) {
        const toggleBtn = document.querySelector('.rcmu-no-traff-toggle-btn');
        if (!toggleBtn) return;

        if (event.detail.enabled) {
            // Авария режими ёқилганда кнопкани блоклаш
            disableToggleButton();
        } else {
            // Авария режими ўчирилганда кнопкани фаоллаштириш
            enableToggleButton();
        }
    }

    // Time Machine ўзгарганда
    function handleTimeMachineChange(event) {
        const toggleBtn = document.querySelector('.rcmu-no-traff-toggle-btn');
        if (!toggleBtn) return;

        if (event.target.checked) {
            // Time Machine ёқилганда кнопкани блоклаш
            disableToggleButton();
        } else {
            // Time Machine ўчирилганда кнопкани фаоллаштириш
            enableToggleButton();
        }
    }

    // Off Stations Panel ўзгарганда
    function handleOffStationsPanelChange(event) {
        // Хозирча бўш - кейинчалик функционал қўшилиши мумкин
    }

    // Кнопкани блоклаш
    function disableToggleButton() {
        const toggleBtn = document.querySelector('.rcmu-no-traff-toggle-btn');
        if (toggleBtn) {
            toggleBtn.disabled = true;
            toggleBtn.style.opacity = '0.5';
            toggleBtn.style.cursor = 'not-allowed';
        }
    }

    // Кнопкани фаоллаштириш
    function enableToggleButton() {
        const toggleBtn = document.querySelector('.rcmu-no-traff-toggle-btn');
        if (toggleBtn) {
            toggleBtn.disabled = false;
            toggleBtn.style.opacity = '1';
            toggleBtn.style.cursor = 'pointer';
        }
    }

    // Ҳолатни текшириш
    function getRcmuVisibleState() {
        return isRcmuVisible;
    }

    // Глобал API
    window.RcmuUI = {
        isVisible: getRcmuVisibleState,
        toggle: toggleRcmuVisibility,
        enable: enableToggleButton,
        disable: disableToggleButton
    };

})(); 