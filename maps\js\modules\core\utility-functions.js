// Ёрдамчи функциялар модули

// Общие вспомогательные функции для карты

// Функция для безопасного получения элемента DOM
function safeGetElement(id) {
    const element = document.getElementById(id);
    if (!element) {
        console.warn(`Element with id "${id}" not found`);
    }
    return element;
}

// Функция для безопасного добавления event listener
function safeAddEventListener(elementId, event, callback) {
    const element = safeGetElement(elementId);
    if (element) {
        element.addEventListener(event, callback);
    }
}

// Функция для дебаунса
function debounce(func, delay) {
    let timeoutId;
    return function (...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}

// Функция для проверки валидности координат
function isValidCoordinate(lat, lng) {
    return (
        typeof lat === 'number' && 
        typeof lng === 'number' &&
        lat >= -90 && lat <= 90 &&
        lng >= -180 && lng <= 180
    );
}

// Функция для форматирования времени
function formatTime(date) {
    if (!date) return '';
    
    const d = new Date(date);
    if (isNaN(d.getTime())) return '';
    
    return d.toLocaleString('ru-RU', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Функция для безопасного парсинга JSON
function safeParseJSON(jsonString, defaultValue = null) {
    try {
        return JSON.parse(jsonString);
    } catch (error) {
        console.warn('Error parsing JSON:', error);
        return defaultValue;
    }
}

// Функция для получения CSS переменной
function getCSSVariable(varName) {
    return getComputedStyle(document.documentElement)
        .getPropertyValue(varName)
        .trim();
}

// Функция для установки CSS переменной
function setCSSVariable(varName, value) {
    document.documentElement.style.setProperty(varName, value);
}

// Функция для глубокого клонирования объекта
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj);
    if (Array.isArray(obj)) return obj.map(deepClone);
    
    const cloned = {};
    for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
            cloned[key] = deepClone(obj[key]);
        }
    }
    return cloned;
}

// Функция для создания уникального ID
function generateUniqueId(prefix = 'id') {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Export функций для использования в других модулях
window.safeGetElement = safeGetElement;
window.safeAddEventListener = safeAddEventListener;
window.debounce = debounce;
window.isValidCoordinate = isValidCoordinate;
window.formatTime = formatTime;
window.safeParseJSON = safeParseJSON;
window.getCSSVariable = getCSSVariable;
window.setCSSVariable = setCSSVariable;
window.deepClone = deepClone;
window.generateUniqueId = generateUniqueId; 