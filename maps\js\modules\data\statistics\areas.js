// Районлар (ареалар) статистикаси модули

// Глобал ўзгарувчилар
window.currentRegionAreas = [];
window.areasStats = {};

// Районлар статистикасини тайёрлаш
window.prepareAreasStats = function(regionId) {
    const areasContainer = document.getElementById('areas-stats-container');
    const areasDivider = document.getElementById('areas-stats-divider');
    const areasTableBody = document.getElementById('areas-table-body');
    
    if (!areasContainer || !areasTableBody) return;
    
    // Агар регион танланмаган бўлса, яширамиз
    if (!regionId) {
        areasContainer.style.display = 'none';
        areasDivider.style.display = 'none';
        return;
    }
    
    // Жадвални тозалаш
    areasTableBody.innerHTML = '';
    
    // Районларни юклаш
    window.loadAreasForRegion(regionId).then(areas => {
        window.currentRegionAreas = areas;
        
        // Контейнерни кўрсатиш
        areasContainer.style.display = 'block';
        areasDivider.style.display = 'block';
        
        // "Барча районлар" қаторини қўшиш
        createAllAreasRow(areasTableBody, regionId);
        
        // Ҳар бир район учун қатор яратиш
        areas.forEach(area => {
            const row = createAreaRow(area, regionId);
            areasTableBody.appendChild(row);
        });
        
        // Статистикани янгилаш
        window.updateAreasStats(regionId);
        
    }).catch(error => {
        console.error('❌ Районларни юклашда хато:', error);
        areasContainer.style.display = 'none';
        areasDivider.style.display = 'none';
    });
}

// Районлар статистикасини янгилаш
window.updateAreasStats = function(regionId) {
    if (!regionId) return;
    
    const areaRows = document.querySelectorAll('.area-row');
    const currentAreaId = document.getElementById('area').value;
    
    // Умумий статистика
    let totalAllAreas = 0;
    let activeAllAreas = 0;
    let inactiveAllAreas = 0;
    
    areaRows.forEach(row => {
        const areaId = row.dataset.areaId;
        
        // "Барча районлар" ни охирида янгилаймиз
        if (areaId === '') {
            return;
        }
        
        // Фақат шу регионнинг БС ларини оламиз
        const areaStations = window.globalStations.filter(station => 
            station.region_id == regionId && station.area_id == areaId
        );
        
        const totalCount = areaStations.length;
        const inactiveCount = areaStations.filter(station => {
            const isOffline = station.status === true || station.status === 'true' || 
                            station.status === 1 || station.status === '1';
            return isOffline;
        }).length;
        const activeCount = totalCount - inactiveCount;
        
        // Қийматларни янгилаш
        row.querySelector('.area-total').textContent = totalCount;
        row.querySelector('.area-active').textContent = activeCount;
        row.querySelector('.area-inactive').textContent = inactiveCount;
        
        // Фоизни ҳисоблаш
        const inactivePercent = totalCount > 0 ? ((inactiveCount / totalCount) * 100).toFixed(1) : 0;
        row.querySelector('.area-percent').textContent = inactivePercent + '%';
        
        // Умумий статистикага қўшиш
        totalAllAreas += totalCount;
        activeAllAreas += activeCount;
        inactiveAllAreas += inactiveCount;
        
        // Район номини тил ўзгарганда янгилаш
        const areaData = window.currentRegionAreas.find(a => a.id == areaId);
        if (areaData) {
            row.querySelector('.area-name').textContent = window.currentLanguage === 'ru' ?
                areaData.name : window.translateAreaName(areaData.name);
        }
        
        // Актив районни белгилаш
        if (areaId == currentAreaId) {
            row.classList.add('active');
        } else {
            row.classList.remove('active');
        }
    });
    
    // "Барча районлар" қаторини янгилаш
    updateAllAreasRow(totalAllAreas, activeAllAreas, inactiveAllAreas, currentAreaId);
}

// "Барча районлар" қаторини яратиш
function createAllAreasRow(areasTableBody, regionId) {
    const allAreasRow = document.createElement('tr');
    allAreasRow.className = 'area-row active';
    allAreasRow.dataset.areaId = '';
    allAreasRow.dataset.regionId = regionId;
    
    // Ном
    const nameCell = document.createElement('td');
    nameCell.className = 'area-name';
    nameCell.style.fontWeight = 'bold';
    nameCell.textContent = window.currentLanguage === 'ru' ? 'Все районы' : 'All areas';
    allAreasRow.appendChild(nameCell);
    
    // Барчаси
    const totalCell = document.createElement('td');
    totalCell.className = 'area-total all-areas-total';
    totalCell.textContent = '0';
    totalCell.dataset.statType = 'all';
    allAreasRow.appendChild(totalCell);
    
    // Актив
    const activeCell = document.createElement('td');
    activeCell.className = 'area-active all-areas-active';
    activeCell.textContent = '0';
    activeCell.dataset.statType = 'online';
    allAreasRow.appendChild(activeCell);
    
    // Ноактив
    const inactiveCell = document.createElement('td');
    inactiveCell.className = 'area-inactive all-areas-inactive';
    inactiveCell.textContent = '0';
    inactiveCell.dataset.statType = 'offline';
    allAreasRow.appendChild(inactiveCell);
    
    // Фоиз
    const percentCell = document.createElement('td');
    percentCell.className = 'area-percent all-areas-percent';
    percentCell.textContent = '0%';
    allAreasRow.appendChild(percentCell);
    
    // Клик ҳандлери
    allAreasRow.addEventListener('click', function(e) {
        handleAllAreasClick(e, this, regionId);
    });
    
    areasTableBody.appendChild(allAreasRow);
}

// Район қаторини яратиш
function createAreaRow(area, regionId) {
    const row = document.createElement('tr');
    row.className = 'area-row';
    row.dataset.areaId = area.id;
    row.dataset.regionId = regionId;
    
    // Район номи
    const nameCell = document.createElement('td');
    nameCell.className = 'area-name';
    nameCell.textContent = window.currentLanguage === 'ru' ?
        area.name : window.translateAreaName(area.name);
    row.appendChild(nameCell);
    
    // Барчаси
    const totalCell = document.createElement('td');
    totalCell.className = 'area-total';
    totalCell.textContent = '0';
    totalCell.dataset.statType = 'all';
    row.appendChild(totalCell);
    
    // Актив
    const activeCell = document.createElement('td');
    activeCell.className = 'area-active';
    activeCell.textContent = '0';
    activeCell.dataset.statType = 'online';
    row.appendChild(activeCell);
    
    // Ноактив
    const inactiveCell = document.createElement('td');
    inactiveCell.className = 'area-inactive';
    inactiveCell.textContent = '0';
    inactiveCell.dataset.statType = 'offline';
    row.appendChild(inactiveCell);
    
    // Фоиз
    const percentCell = document.createElement('td');
    percentCell.className = 'area-percent';
    percentCell.textContent = '0%';
    row.appendChild(percentCell);
    
    // Клик ҳандлери
    row.addEventListener('click', function(e) {
        handleAreaClick(e, this, area, regionId);
    });
    
    return row;
}

// "Барча районлар" клик ҳандлери
function handleAllAreasClick(e, rowElement, regionId) {
    const statType = e.target.dataset.statType || 'all';
    
    // Актив классларни янгилаш
    document.querySelectorAll('.area-row').forEach(r => r.classList.remove('active'));
    rowElement.classList.add('active');
    
    // Район селектини тозалаш
    document.getElementById('area').value = '';
    
    // Статус фильтрини ўрнатиш
    document.getElementById('status').value = statType;
    document.getElementById('duration-filter').value = '';
    
    // Картани марказлаштириш (регион бўйича)
    if (window.autoCenterEnabled !== false) {
        const regionStations = window.globalStations.filter(station => station.region_id == regionId);
        if (regionStations.length > 0) {
            const bounds = L.latLngBounds();
            regionStations.forEach(station => {
                const lat = parseFloat(station.lat);
                const lon = parseFloat(station.lon);
                if (!isNaN(lat) && !isNaN(lon)) {
                    bounds.extend([lat, lon]);
                }
            });
            
            if (bounds.isValid()) {
                window.mymap.fitBounds(bounds, {
                    padding: [50, 50],
                    maxZoom: 10
                });
            }
        }
    }
    
    // Фильтрларни қўллаш
    if (window.AlarmManager && typeof window.AlarmManager.isEnabled === 'function' && window.AlarmManager.isEnabled()) {
        window.AlarmManager.refresh(true, true);
    } else {
        window.applyCurrentFilters();
        
        if (window.calculateDurationStats && window.globalStations) {
            window.calculateDurationStats(window.globalStations);
        }
        
        window.updateAreasStats(regionId);
    }
}

// Район клик ҳандлери
function handleAreaClick(e, rowElement, area, regionId) {
    const statType = e.target.dataset.statType || 'all';
    e.stopPropagation();
    
    // Актив классларни янгилаш
    document.querySelectorAll('.area-row').forEach(r => r.classList.remove('active'));
    rowElement.classList.add('active');
    
    // Район селектини янгилаш
    const areaSelect = document.getElementById('area');
    areaSelect.value = area.id;
    
    // Статус фильтрини ўрнатиш
    document.getElementById('status').value = statType;
    document.getElementById('duration-filter').value = '';
    
    // Картани марказлаштириш
    centerMapOnArea(area, regionId);
    
    // Фильтрларни қўллаш
    if (window.AlarmManager && typeof window.AlarmManager.isEnabled === 'function' && window.AlarmManager.isEnabled()) {
        window.AlarmManager.refresh(true, true);
    } else {
        window.applyCurrentFilters();
        window.updateAreasStats(regionId);
    }
}

// Картани районга марказлаштириш
function centerMapOnArea(area, regionId) {
    const areaStations = window.globalStations.filter(station => 
        station.region_id == regionId && station.area_id == area.id
    );
    
    if (window.autoCenterEnabled !== false) {
        if (areaStations.length > 0) {
            const bounds = L.latLngBounds();
            areaStations.forEach(station => {
                const lat = parseFloat(station.lat);
                const lon = parseFloat(station.lon);
                if (!isNaN(lat) && !isNaN(lon)) {
                    bounds.extend([lat, lon]);
                }
            });
            
            if (bounds.isValid()) {
                window.mymap.fitBounds(bounds, {
                    padding: [50, 50],
                    maxZoom: 12
                });
            }
        }
    }
}

// "Барча районлар" қаторини янгилаш
function updateAllAreasRow(totalAllAreas, activeAllAreas, inactiveAllAreas, currentAreaId) {
    const allAreasRow = document.querySelector('.area-row[data-area-id=""]');
    if (allAreasRow) {
        allAreasRow.querySelector('.all-areas-total').textContent = totalAllAreas;
        allAreasRow.querySelector('.all-areas-active').textContent = activeAllAreas;
        allAreasRow.querySelector('.all-areas-inactive').textContent = inactiveAllAreas;
        
        // Фоизни ҳисоблаш
        const allInactivePercent = totalAllAreas > 0 ? 
            ((inactiveAllAreas / totalAllAreas) * 100).toFixed(1) : 0;
        allAreasRow.querySelector('.all-areas-percent').textContent = allInactivePercent + '%';
        
        // Тил ўзгарганда номни янгилаш
        const nameCell = allAreasRow.querySelector('.area-name');
        nameCell.textContent = window.currentLanguage === 'ru' ? 'Все районы' : 'All areas';
        
        // Актив белгиси
        if (!currentAreaId) {
            allAreasRow.classList.add('active');
        } else {
            allAreasRow.classList.remove('active');
        }
    }
}

// Авария режимида районлар статистикасини янгилаш
window.updateAreasTable = function(areaStats, regionId) {
    if (!areaStats || !regionId) return;
    
    const areaRows = document.querySelectorAll('.area-row');
    
    // Умумий статистика
    let totalAll = 0;
    let totalOnline = 0;
    let totalOffline = 0;
    
    areaRows.forEach(row => {
        const areaId = row.dataset.areaId;
        const rowRegionId = row.dataset.regionId;
        
        // Фақат танланган регион районларини янгилаймиз
        if (rowRegionId != regionId) return;
        
        if (areaId === '') {
            return; // "Барча районлар" ни охирида янгилаймиз
        }
        
        const stats = areaStats[areaId];
        if (stats) {
            // Backend дан келган форматга мосланиш
            const totalBs = stats.total_bs || 0;
            const bsWithAlarms = stats.bs_with_alarms || 0;
            const activeBs = totalBs - bsWithAlarms;
            
            row.querySelector('.area-total').textContent = totalBs;
            row.querySelector('.area-active').textContent = activeBs;
            row.querySelector('.area-inactive').textContent = bsWithAlarms;
            row.querySelector('.area-percent').textContent = (stats.percentage || 0) + '%';
            
            totalAll += totalBs;
            totalOnline += activeBs;
            totalOffline += bsWithAlarms;
        } else {
            // Статистика йўқ бўлса, 0 кўрсатамиз
            row.querySelector('.area-total').textContent = '0';
            row.querySelector('.area-active').textContent = '0';
            row.querySelector('.area-inactive').textContent = '0';
            row.querySelector('.area-percent').textContent = '0%';
        }
    });
    
    // "Барча районлар" ни янгилаш
    const allAreasRow = document.querySelector('.area-row[data-area-id=""][data-region-id="' + regionId + '"]');
    if (allAreasRow) {
        allAreasRow.querySelector('.all-areas-total').textContent = totalAll;
        allAreasRow.querySelector('.all-areas-active').textContent = totalOnline;
        allAreasRow.querySelector('.all-areas-inactive').textContent = totalOffline;
        
        const allPercent = totalAll > 0 ? ((totalOffline / totalAll) * 100).toFixed(1) : 0;
        allAreasRow.querySelector('.all-areas-percent').textContent = allPercent + '%';
    }
} 