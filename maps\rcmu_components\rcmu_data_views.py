"""
RCMU маълумотларни юклаш ва ишлаш модули
"""
import pandas as pd
import json
from django.shortcuts import get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.db import transaction
from bsview.models import BsBeeline
from ..models import RcmuData
from .rcmu_permissions import check_rcmu_permission


@csrf_exempt
@login_required
@require_http_methods(["POST"])
def rcmu_upload(request):
    """
    Загрузка Excel файла с данными РЦМУ
    """
    # Проверка прав доступа
    if not check_rcmu_permission(request.user):
        return JsonResponse({'error': 'У вас нет прав доступа к этой функции.'}, status=403)
    
    if 'excel_file' not in request.FILES:
        return JsonResponse({'error': 'Файл не выбран.'}, status=400)
    
    excel_file = request.FILES['excel_file']
    
    # Проверка расширения файла
    if not excel_file.name.endswith(('.xlsx', '.xls')):
        return JsonResponse({'error': 'Поддерживаются только файлы Excel (.xlsx, .xls).'}, status=400)
    
    try:
        # Чтение Excel файла
        df = pd.read_excel(excel_file)
        
        # Проверка наличия обязательных колонок
        required_columns = ['Site']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            return JsonResponse({
                'error': f'В файле отсутствуют обязательные колонки: {", ".join(missing_columns)}'
            }, status=400)
        
        # Получение координат из bsview_bsbeeline
        bs_coordinates = {}
        for bs in BsBeeline.objects.all():
            bs_coordinates[bs.bsnum] = {
                'lat': bs.lat,
                'lon': bs.lon,
                'bsname': bs.bsname
            }
        
        # Обработка данных и сохранение в БД
        with transaction.atomic():
            # Очистка существующих данных
            RcmuData.objects.all().delete()
            
            # Сохранение новых данных
            created_count = 0
            for index, row in df.iterrows():
                site = str(row.get('Site', '')).strip()
                if not site or site == 'nan':
                    continue
                
                # Получение координат
                coords = bs_coordinates.get(site, {})
                
                # Создание записи
                rcmu_data = RcmuData(
                    branch=str(row.get('Branch', ''))[:100] if pd.notna(row.get('Branch')) else None,
                    region=str(row.get('Region', ''))[:100] if pd.notna(row.get('Region')) else None,
                    region_id=int(row.get('Region ID', 0)) if pd.notna(row.get('Region ID')) and row.get('Region ID') != '' else None,
                    date_lego=pd.to_datetime(row.get('Date (LEGO)')).date() if pd.notna(row.get('Date (LEGO)')) else None,
                    date_performance=pd.to_datetime(row.get('Date (perfomance)')).date() if pd.notna(row.get('Date (perfomance)')) else None,
                    site=site,
                    freq=str(row.get('Freq', ''))[:50] if pd.notna(row.get('Freq')) else None,
                    bs_name=str(row.get('BS name', ''))[:100] if pd.notna(row.get('BS name')) else coords.get('bsname'),
                    operating_permit=str(row.get('Operating permit', ''))[:100] if pd.notna(row.get('Operating permit')) else None,
                    comments=str(row.get('Comments', '')) if pd.notna(row.get('Comments')) else None,
                    cause_ru=str(row.get('Cause (ru)', '')) if pd.notna(row.get('Cause (ru)')) else None,
                    cause_eng=str(row.get('Cause (eng)', '')) if pd.notna(row.get('Cause (eng)')) else None,
                    responsible=str(row.get('Responsible', ''))[:100] if pd.notna(row.get('Responsible')) else None,
                    estimated_air_date=str(row.get('Estimated air date', ''))[:100] if pd.notna(row.get('Estimated air date')) else None,
                    total_down_time_days=str(row.get('Total down time (days)', ''))[:50] if pd.notna(row.get('Total down time (days)')) else None,
                    lat=coords.get('lat'),
                    lon=coords.get('lon'),
                    created_by=request.user
                )
                rcmu_data.save()
                created_count += 1
        
        return JsonResponse({
            'success': True,
            'message': f'Успешно загружено {created_count} записей.',
            'created_count': created_count
        })
        
    except Exception as e:
        return JsonResponse({'error': f'Ошибка при обработке файла: {str(e)}'}, status=500)


@login_required
@require_http_methods(["POST"])
def rcmu_update(request, pk):
    """
    Обновление записи РЦМУ (в основном комментарий качественника)
    """
    # Проверка прав доступа
    if not check_rcmu_permission(request.user):
        return JsonResponse({'error': 'У вас нет прав доступа к этой функции.'}, status=403)
    
    rcmu_data = get_object_or_404(RcmuData, pk=pk)
    
    try:
        data = json.loads(request.body)
        
        # Обновляем только комментарий качественника
        rcmu_data.quality_comment = data.get('quality_comment', '')
        rcmu_data.save()
        
        return JsonResponse({
            'success': True,
            'message': 'Комментарий успешно обновлен.'
        })
        
    except Exception as e:
        return JsonResponse({'error': f'Ошибка при обновлении: {str(e)}'}, status=500)


@login_required
@require_http_methods(["DELETE"])
def rcmu_delete(request, pk):
    """
    Удаление записи РЦМУ
    """
    # Проверка прав доступа (только администраторы могут удалять)
    if not (hasattr(request.user, 'privilege_id') and request.user.privilege_id == 1):
        return JsonResponse({'error': 'У вас нет прав для удаления записей.'}, status=403)
    
    rcmu_data = get_object_or_404(RcmuData, pk=pk)
    
    try:
        rcmu_data.delete()
        return JsonResponse({
            'success': True,
            'message': 'Запись успешно удалена.'
        })
        
    except Exception as e:
        return JsonResponse({'error': f'Ошибка при удалении: {str(e)}'}, status=500) 