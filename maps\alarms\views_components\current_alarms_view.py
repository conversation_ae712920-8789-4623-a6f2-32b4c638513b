from datetime import datetime
from django.db.models import Q
from django.utils import timezone
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from bsview.models import BsBeeline, Log_Alarms_full
from .utils import format_duration

@api_view(['GET'])
def get_current_other_alarms(request):
    """Танланган авария турлари бўйича жорий аварияларни олиш"""
    try:
        # Query параметрларни олиш
        alarm_types = request.query_params.getlist('alarm_types[]')
        region_id = request.query_params.get('region_id')
        area_id = request.query_params.get('area_id')
        language = request.query_params.get('language', 'ru')  # Тил параметри
        
        if not alarm_types:
            return Response({
                'error': 'alarm_types parameter is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Жорий вақт
        current_time = timezone.now()
        
        # Жорий аварияларни фильтрлаш (cleartime бўш ёки келажакда)
        query = Q(alarmname__in=alarm_types) & (
            Q(cleartime__isnull=True) | Q(cleartime__gt=current_time)
        )
        
        current_alarms = Log_Alarms_full.objects.filter(query)
        
        # БС маълумотларини олиш
        bs_data = {}
        for alarm in current_alarms:
            bs_number = alarm.bsnumber
            
            # БС маълумотларини топиш
            try:
                bs = BsBeeline.objects.get(bsnum=bs_number)
                
                # Регион/туман фильтри
                if region_id and str(bs.region_id) != str(region_id):
                    continue
                if area_id and str(bs.area_id) != str(area_id):
                    continue
                
                # Авария давомийлигини ҳисоблаш (соатларда)
                duration_hours = 0
                if alarm.appeartime:
                    duration = current_time - alarm.appeartime
                    duration_hours = duration.total_seconds() / 3600
                
                # БС маълумотларини йиғиш
                if bs.id not in bs_data:
                    bs_data[bs.id] = {
                        'id': bs.id,
                        'name': bs.bsname,
                        'bsName': bs.bsname,
                        'lat': bs.lat.replace(',', '.') if bs.lat else None,
                        'lon': bs.lon.replace(',', '.') if bs.lon else None,
                        'status': True,  # Авария бор
                        'region_id': bs.region_id,
                        'region_name': bs.region.name if bs.region else None,
                        'area_id': bs.area_id,
                        'area_name': bs.area.name if bs.area else None,
                        'other_alarms': []
                    }
                
                # Авария маълумотларини қўшиш
                bs_data[bs.id]['other_alarms'].append({
                    'alarm_id': alarm.id,
                    'alarm_name': alarm.alarmname,
                    'appear_time': alarm.appeartime.isoformat() if alarm.appeartime else None,
                    'duration_hours': round(duration_hours, 2),
                    'duration_text': format_duration(duration_hours, language)
                })
                
            except BsBeeline.DoesNotExist:
                continue
        
        # Натижани рўйхатга айлантириш
        result = list(bs_data.values())
        
        return Response({
            'stations': result,
            'count': len(result),
            'alarm_types': alarm_types
        })
        
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 