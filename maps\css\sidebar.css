/* Стили Sidebar */
.sidebar {
    width: 300px;
    max-width: 300px;
    height: calc(100vh - 60px) !important;
    background-color: white;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: fixed;
    top: 60px !important;
    left: 0;
    z-index: 1000;
    border-radius: 0;
}

.sidebar-header {
    padding: 12px;
    background-color: #2c3e50;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h2 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
}

.sidebar-content {
    padding: 0 8px 8px;
    overflow-y: auto;
    flex: 1;
    position: relative;
}

/* Стили фильтров */
.filter-group {
    margin-bottom: 6px;
}

.filter-group label {
    display: block;
    margin-bottom: 1px;
    font-weight: 500;
    font-size: 10px;
    color: #555;
}

.filter-group select,
.filter-group input,
#bs-search,
#reset-filters {
    width: 100%;
    padding: 4px 6px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 11px;
    height: 28px;
}

.search-reset-container {
    display: flex;
    gap: 5px;
    margin-bottom: 6px;
    align-items: flex-end;
}

.search-reset-container .search-input-container {
    flex: 1;
}

.search-reset-container .reset-button-container {
    width: auto;
}

#reset-filters {
    white-space: nowrap;
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
    margin-bottom: 0;
}

#reset-filters:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

/* Языковой селектор */
.language-selector {
    display: flex;
}

.lang-btn {
    background-color: transparent;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    margin-left: 5px;
    padding: 2px 6px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s;
}

.lang-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.lang-btn.active {
    background-color: #f39c12;
    border-color: #f39c12;
    color: #2c3e50;
    font-weight: bold;
}

/* Стили лейблов поиска БС */
#bs-search-label,
#area-label {
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 4px;
    display: inline-block;
}

/* Скрытие только селекта региона */
#region {
    display: none !important;
}

/* Скрытие лейбла для региона */
label[for="region"] {
    display: none !important;
}

/* Убираем отступы у скрытого фильтра региона */
.filter-group:has(#region) {
    display: none !important;
}

/* Стили для фильтра района */
.filter-group:has(#area) {
    display: none;
    margin-top: 10px;
}

.filter-group:has(#area).show {
    display: block;
}

/* Стили для селекта района */
#area {
    width: 100%;
}

/* Стили для лейбла района */
label[for="area"] {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    font-size: 13px;
    color: #555;
}

/* Regions stats стиллари (мавжуд) */
.regions-stats {
    margin: 10px 0;
}

.regions-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 11px;
}

.regions-table th,
.regions-table td {
    padding: 4px 8px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.regions-table th {
    font-weight: bold;
    background-color: #f8f9fa;
    color: #495057;
    font-size: 10px;
    text-transform: uppercase;
}

.region-row {
    cursor: pointer;
    transition: background-color 0.2s;
}

.region-row:hover {
    background-color: #f5f5f5;
}

.region-row.active {
    background-color: #e3f2fd;
}

.region-row td:nth-child(2),
.region-row td:nth-child(3),
.region-row td:nth-child(4),
.region-row td:nth-child(5) {
    text-align: center;
    cursor: pointer;
}

/* Areas stats стиллари (янги) */
.areas-stats {
    margin: 10px 0;
}

.areas-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 11px;
}

.areas-table th,
.areas-table td {
    padding: 4px 8px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.areas-table th {
    font-weight: bold;
    background-color: #f8f9fa;
    color: #495057;
    font-size: 10px;
    text-transform: uppercase;
}

.area-row {
    cursor: pointer;
    transition: background-color 0.2s;
}

.area-row:hover {
    background-color: #f5f5f5;
}

.area-row.active {
    background-color: #e3f2fd;
}

.area-row td:nth-child(2),
.area-row td:nth-child(3),
.area-row td:nth-child(4),
.area-row td:nth-child(5) {
    text-align: center;
    cursor: pointer;
}

/* Статистика қийматлари учун рангли стиллар */
.area-active,
.region-active {
    color: #28a745;
    font-weight: 500;
}

.area-inactive,
.region-inactive {
    color: #dc3545;
    font-weight: 500;
}

.area-percent,
.region-percent {
    color: #f44336;
    font-weight: 600;
}

/* Time Machine */ 