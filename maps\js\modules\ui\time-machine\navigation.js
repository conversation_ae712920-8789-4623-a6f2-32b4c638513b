// Машина времени - вақт навигацияси модули

// Применение машины времени
window.applyTimeMachine = function() {
    if (!window.isTimeMachineEnabled) return;

    const date = document.getElementById('time-machine-date').value;
    const hour = document.getElementById('time-machine-hour').value;
    const minute = document.getElementById('time-machine-minute').value;

    if (date && hour !== '' && minute !== '') {
        window.selectedDateTime = new Date(`${date}T${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}:00`);

        // Обновление отображения времени
        updateTimeDisplay();

        // Обновление слайдера
        if (window.updateSliderFromTime) {
            window.updateSliderFromTime();
        }

        // Загрузка исторических данных
        if (window.loadHistoricalData) {
            window.loadHistoricalData(window.selectedDateTime);
        }
    }
}

// Корректировка времени
window.adjustTime = function(minutes) {
    if (!window.isTimeMachineEnabled) return;

    const dateInput = document.getElementById('time-machine-date');
    const hourInput = document.getElementById('time-machine-hour');
    const minuteInput = document.getElementById('time-machine-minute');

    // Получаем текущие значения из полей
    const currentDate = dateInput.value;
    const currentHour = parseInt(hourInput.value);
    const currentMinute = parseInt(minuteInput.value);

    if (!window.selectedDateTime || !currentDate) {
        // Если время не выбрано, создаем новую дату из текущих значений
        window.selectedDateTime = new Date(`${currentDate}T${String(currentHour).padStart(2, '0')}:${String(currentMinute).padStart(2, '0')}:00`);
    }

    // Создаем новую дату и корректируем время
    const newDateTime = new Date(window.selectedDateTime.getTime());
    newDateTime.setMinutes(newDateTime.getMinutes() + minutes);

    // Обновляем поля ввода
    dateInput.value = newDateTime.toISOString().split('T')[0];
    hourInput.value = newDateTime.getHours();
    minuteInput.value = Math.floor(newDateTime.getMinutes() / 5) * 5;

    // Обновляем selectedDateTime
    window.selectedDateTime = newDateTime;

    // Применяем новое время
    window.applyTimeMachine();
}

// Установка текущего времени
window.setCurrentTime = function() {
    if (!window.isTimeMachineEnabled) return;

    const now = new Date();
    const dateInput = document.getElementById('time-machine-date');
    const hourInput = document.getElementById('time-machine-hour');
    const minuteInput = document.getElementById('time-machine-minute');

    // Обновляем поля ввода
    dateInput.value = now.toISOString().split('T')[0];
    hourInput.value = now.getHours();
    minuteInput.value = Math.floor(now.getMinutes() / 5) * 5;

    // Обновляем selectedDateTime
    window.selectedDateTime = new Date(`${dateInput.value}T${String(hourInput.value).padStart(2, '0')}:${String(minuteInput.value).padStart(2, '0')}:00`);

    // Обновляем слайдер
    if (window.updateSliderFromTime) {
        window.updateSliderFromTime();
    }

    window.applyTimeMachine();
}

// Обновление отображения времени
function updateTimeDisplay() {
    if (window.selectedDateTime) {
        const timeString = formatDateTime(window.selectedDateTime);
        document.getElementById('current-selected-time').textContent = timeString;
    }
}

// Форматирование даты и времени (DD/MM/YYYY HH:MM - 24 соатлик формат)
function formatDateTime(date) {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${day}/${month}/${year}, ${hours}:${minutes}`;
} 