<!-- Sidebar компонент -->
<div class="sidebar">
    <div class="sidebar-header">
        <h2 id="stats-header">Статистика</h2>
        <div class="language-selector">
            <button id="lang-ru" class="lang-btn active">RU</button>
            <button id="lang-en" class="lang-btn">EN</button>
        </div>
    </div>
    <div class="sidebar-content">
        <!-- Таблица статистики регионов -->
        <div class="regions-stats">
            <table class="regions-table">
                <thead>
                    <tr>
                        <th id="region-header">Регион</th>
                        <th>All</th>
                        <th>On</th>
                        <th>Off</th>
                        <th>%</th>
                    </tr>
                </thead>
                <tbody id="regions-table-body">
                    <!-- Статистика регионов добавляется динамически -->
                </tbody>
            </table>
        </div>

        <!-- Разделитель -->
        <div style="border-top: 1px solid #ddd; margin: 3px 0;"></div>

        <!-- Районлар статистикаси (янги) -->
        <div class="areas-stats" id="areas-stats-container" style="display: none;">
            <table class="areas-table">
                <thead>
                    <tr>
                        <th id="area-stats-header">Area</th>
                        <th>All</th>
                        <th>On</th>
                        <th>Off</th>
                        <th>%</th>
                    </tr>
                </thead>
                <tbody id="areas-table-body">
                    <!-- Районлар статистикаси динамик қўшилади -->
                </tbody>
            </table>
        </div>

        <!-- Разделитель (районлар жадвали кўрингандагина) -->
        <div id="areas-stats-divider" style="border-top: 1px solid #ddd; margin: 3px 0; display: none;"></div>

        <!-- Остальные фильтры -->
        <div class="filter-group" style="display: none;">
            <label for="area" id="area-label">Район:</label>
            <select id="area" disabled>
                <option value="" id="all-areas">Все районы</option>
                <!-- Районы будут добавлены динамически -->
            </select>
        </div>

        <!-- Поиск и сброс фильтров в одну строку -->
        <div class="search-reset-container">
            <!-- Поиск по имени или номеру БС -->
            <div class="search-input-container">
                <label for="bs-search" id="bs-search-label">Поиск:</label>
                <input type="text" id="bs-search" class="form-control" placeholder="Имя или номер БС">
            </div>

            <!-- Кнопка сброса фильтров -->
            <div class="reset-button-container">
                <button id="reset-filters" class="btn btn-sm">Сброс</button>
            </div>
        </div>

        <!-- Скрытые селекты (для логики) -->
        <div style="display: none;">
            <select id="region">
                <option value="" id="all-regions">Все регионы</option>
                <!-- Регионы будут добавлены динамически -->
            </select>
            <select id="status">
                <option value="all" id="status-all">Все</option>
                <option value="online" id="status-online">Онлайн</option>
                <option value="offline" id="status-offline">Оффлайн</option>
            </select>
            <!-- Фильтр продолжительности для скрытого селекта -->
            <label>Скрытый селект продолжительности:</label>
            <select id="duration-filter">
                <option value="all" id="duration-all">Все</option>
                <option value="1" id="duration-1">До 1 часа</option>
                <option value="2" id="duration-2">До 2 часов</option>
                <option value="3" id="duration-3">До 3 часов</option>
                <option value="4" id="duration-4">До 4 часов</option>
                <option value="5" id="duration-5">Более 4 часов</option>
                <option value="6" id="duration-6">4 часа - 1 день</option>
                <option value="7" id="duration-7">1 день - 7 дней</option>
                <option value="8" id="duration-8">Более 7 дней</option>
            </select>
            <!-- Скрытые элементы статистики -->
            <div id="total-bs-count">0</div>
            <div id="active-bs-count">0</div>
            <div id="inactive-bs-count">0</div>
        </div>
    </div>
</div>