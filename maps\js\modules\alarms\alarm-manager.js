// Alarm Manager Module - Авария бошқариш модулининг асосий координатори
// Янги модуллар: alarm-ui.js, alarm-data.js, alarm-markers.js, alarm-stats.js

window.AlarmManager = (function() {
    'use strict';
    
    // Приватные переменные
    let isOtherAlarmsMode = false;
    let selectedAlarmTypes = [];
    let alarmRefreshInterval = null;
    
    // Инициализация модуля
    function init() {
        // UI компонентларини яратиш
        if (window.AlarmUI) {
            window.AlarmUI.createAlarmUI();
        }
        
        // Event listener лар
        setupEventListeners();
        
        // Авария турларини юклаш
        if (window.AlarmData) {
            window.AlarmData.loadAlarmTypes();
        }
    }
    
    // Event listener лар
    function setupEventListeners() {
        // Alarm mode toggle - HTML'dagi tugmaga event listener qo'shish
        const toggleBtn = document.querySelector('.alarm-toggle-btn');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', toggleAlarmMode);
        }
        
        // Close button
        const closeBtn = document.getElementById('close-alarm-selector');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                if (window.AlarmUI) window.AlarmUI.hideAlarmSelector();
            });
        }
        
        // Apply button
        const applyBtn = document.getElementById('apply-alarm-selection');
        if (applyBtn) {
            applyBtn.addEventListener('click', applyAlarmSelection);
        }
        
        // Cancel button
        const cancelBtn = document.getElementById('cancel-alarm-selection');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                if (window.AlarmUI) window.AlarmUI.hideAlarmSelector();
            });
        }
        
        // Select all checkbox
        const selectAllBtn = document.getElementById('select-all-alarms');
        if (selectAllBtn) {
            selectAllBtn.addEventListener('change', (event) => {
                if (window.AlarmUI) {
                    window.AlarmUI.toggleSelectAll(event.target.checked);
                    window.AlarmUI.updateSelectAllState();
                }
            });
        }
        
        // Duration filter event listener қўшиш
        setupDurationFilterForAlarmMode();
    }
    
    // Alarm mode toggle
    function toggleAlarmMode() {
        // Time Machine фаол бўлса, авария режимини ёқишга рухсат бермаймиз
        if (window.isTimeMachineEnabled) {
            alert(window.translations[window.currentLanguage]['cannot_enable_alarm_with_time_machine']);
            return;
        }
        
        // Agar avaria rejimida bo'lsak, normal rejimga qaytamiz
        if (isOtherAlarmsMode) {
            disableAlarmMode();
            return;
        }
        
        // Agar panel ochiq bo'lsa, uni yopamiz
        const panel = document.getElementById('alarm-selector-panel');
        if (panel && panel.style.display !== 'none') {
            if (window.AlarmUI) window.AlarmUI.hideAlarmSelector();
            return;
        }
        
        // Aks holda alarm selector ni ko'rsatamiz
        if (window.AlarmUI) window.AlarmUI.showAlarmSelector();
    }
    
    // Танловни қўллаш
    function applyAlarmSelection() {
        // Танланган авария турларини йиғиш
        if (window.AlarmUI) {
            selectedAlarmTypes = window.AlarmUI.getSelectedAlarmTypes();
        }
        
        // Агар танланган бўлса
        if (selectedAlarmTypes.length > 0) {
            enableAlarmMode();
        } else {
            alert(window.currentLanguage === 'ru' ? 
                'Пожалуйста, выберите хотя бы один тип аварии' : 
                'Please select at least one alarm type');
            return;
        }
        
        // Панелни яшириш
        if (window.AlarmUI) window.AlarmUI.hideAlarmSelector();
    }
    
    // Alarm mode ни ёқиш
    function enableAlarmMode() {
        isOtherAlarmsMode = true;
        
        // Time Machine checkbox ни блоклаш
        const timeMachineCheckbox = document.getElementById('time-machine-enabled');
        if (timeMachineCheckbox) {
            timeMachineCheckbox.disabled = true;
        }
        
        // Loader ни кўрсатиш
        if (window.showLoader) {
            window.showLoader();
        }
        
        // UI ни янгилаш
        if (window.AlarmUI) {
            window.AlarmUI.updateUIForAlarmMode(true);
        }
        
        // Мавжуд auto-refresh ни тўхтатиш
        if (window.stopAutoRefresh) {
            window.stopAutoRefresh();
        }
        
        // Авария маълумотларини юклаш (manual = true)
        if (window.AlarmData) {
            window.AlarmData.loadAlarmData(selectedAlarmTypes, true);
        }
        
        // Auto-refresh ни бошлаш (ҳар 10 секунд)
        startAlarmRefresh();
        
        // Event ни dispatch қилиш
        window.dispatchEvent(new CustomEvent('alarmModeChanged', {
            detail: { enabled: true, alarmTypes: selectedAlarmTypes }
        }));
    }
    
    // Alarm mode ни ўчириш
    function disableAlarmMode() {
        isOtherAlarmsMode = false;
        selectedAlarmTypes = [];
        
        // Time Machine checkbox блокини олиб ташлаш
        const timeMachineCheckbox = document.getElementById('time-machine-enabled');
        if (timeMachineCheckbox) {
            timeMachineCheckbox.disabled = false;
        }
        
        // UI ни янгилаш
        if (window.AlarmUI) {
            window.AlarmUI.updateUIForAlarmMode(false);
        }
        
        // Alarm refresh ни тўхтатиш
        stopAlarmRefresh();
        
        // Duration filter event listener'ларни олиб ташлаш
        removeDurationFilterForAlarmMode();
        
        // Duration legend ни аввалги ҳолатига қайтариш
        if (window.AlarmStats) {
            window.AlarmStats.restoreNormalDurationLegend();
        }
        
        // Event ни dispatch қилиш
        window.dispatchEvent(new CustomEvent('alarmModeChanged', {
            detail: { enabled: false }
        }));
        
        // Normal mode маълумотларини юклаш
        if (window.loadMapData) {
            window.loadMapData();
        } else if (window.startAutoRefresh) {
            window.startAutoRefresh();
        }
    }
    
    // Auto-refresh ни бошлаш
    function startAlarmRefresh() {
        if (alarmRefreshInterval) {
            clearInterval(alarmRefreshInterval);
        }
        
        alarmRefreshInterval = setInterval(() => {
            if (isOtherAlarmsMode && selectedAlarmTypes.length > 0 && window.AlarmData) {
                window.AlarmData.loadAlarmData(selectedAlarmTypes, false);
            }
        }, 10000); // 10 секунд
    }
    
    // Auto-refresh ни тўхтатиш
    function stopAlarmRefresh() {
        if (alarmRefreshInterval) {
            clearInterval(alarmRefreshInterval);
            alarmRefreshInterval = null;
        }
    }
    
    // Duration filter event listener қўшиш
    function setupDurationFilterForAlarmMode() {
        const durationItems = document.querySelectorAll('.downtime-item');
        durationItems.forEach(item => {
            item.addEventListener('click', handleAlarmDurationFilter);
        });
    }
    
    // Duration filter event listener'ларни олиб ташлаш
    function removeDurationFilterForAlarmMode() {
        const durationItems = document.querySelectorAll('.downtime-item');
        durationItems.forEach(item => {
            item.removeEventListener('click', handleAlarmDurationFilter);
        });
    }
    
    // Alarm режими duration filter обработчиги
    function handleAlarmDurationFilter(event) {
        if (!isOtherAlarmsMode) return;
        
        event.preventDefault();
        
        // Фаол элементни янгилаш
        document.querySelectorAll('.downtime-item').forEach(item => {
            item.classList.remove('active');
        });
        event.currentTarget.classList.add('active');
        
        // Маълумотларни қайта юклаш
        if (selectedAlarmTypes.length > 0 && window.AlarmData) {
            window.AlarmData.loadAlarmData(selectedAlarmTypes, false, true);
        }
    }
    
    // Авария режими фаол эканлигини текшириш
    function isAlarmModeActive() {
        return isOtherAlarmsMode;
    }
    
    // Refresh функциясини қўшиш (backward compatibility учун)
    function refresh(manual = false, forceRefresh = false) {
        if (isOtherAlarmsMode && selectedAlarmTypes.length > 0 && window.AlarmData) {
            window.AlarmData.loadAlarmData(selectedAlarmTypes, manual, forceRefresh);
        }
    }
    
    // Public API
    return {
        init,
        isAlarmModeActive,
        getSelectedAlarmTypes: () => selectedAlarmTypes,
        // Backward compatibility учун
        isEnabled: isAlarmModeActive,  // isEnabled() -> isAlarmModeActive()
        refresh: refresh,              // refresh() методи
        // Хавфсизлик учун
        isInitialized: () => true
    };
})();

// Глобал инициализация
document.addEventListener('DOMContentLoaded', function() {
    window.AlarmManager.init();
}); 