/* Стили таблицы статистики регионов */
.regions-stats {
    margin-top: 0;
    border-top: none;
    padding-top: 0;
}

.regions-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-top: 0;
    font-size: 12px;
}

.regions-table thead {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.regions-table th {
    background-color: #f3f3f3;
    padding: 6px 5px;
    text-align: center;
    font-weight: 600;
    border-bottom: 2px solid #ddd;
    font-size: 11px;
}

.regions-table td {
    padding: 5px 4px;
    text-align: center;
    border-bottom: 1px solid #eee;
    font-size: 11px;
}

.regions-table tbody tr {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.regions-table tbody tr:hover {
    background-color: #f5f5f5;
}

.regions-table .region-name {
    text-align: left;
    font-weight: 500;
    padding-left: 8px;
}

/* Активная строка региона */
.regions-table tr.active {
    background-color: #e3f2fd;
}

.regions-table tr.active:hover {
    background-color: #bbdefb;
}

/* Стили для колонок статистики */
.region-total {
    color: #333;
    font-weight: 600;
}

.region-active {
    color: #4caf50;
    font-weight: 600;
}

.region-inactive {
    color: #f44336;
    font-weight: 600;
}

.region-percent {
    color: #f44336;
    font-weight: 600;
}

/* Стили для активной строки убраны, так как фон задается на уровне строки */

/* Общая статистика */
.total-stat {
    color: #333;
}

.online-stat {
    color: #4caf50;
}

.offline-stat {
    color: #f44336;
}

/* Строка "Все регионы" */
.region-row[data-region-id=""] {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

.region-row[data-region-id=""]:hover {
    background-color: #e9ecef;
}

.region-row[data-region-id=""].active {
    background-color: #d1ecf1;
}

.region-row[data-region-id=""].active:hover {
    background-color: #bee5eb;
}

/* Кликабельные ячейки статистики (All, On, Off устунлари учун) */
.region-total,
.region-active,
.region-inactive {
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
}

.region-total:hover,
.region-active:hover,
.region-inactive:hover {
    background-color: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* Подсветка при наведении на ячейки активного региона */
.region-row.active .region-total:hover,
.region-row.active .region-active:hover,
.region-row.active .region-inactive:hover {
    background-color: #cfe2ff;
}



