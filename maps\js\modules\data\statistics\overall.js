// Умумий статистика модули

// Переменные для статистики
window.durationStats = {
    '1': 0, '2': 0, '3': 0, '4': 0, '5': 0
};

// Обновление общей статистики
window.updateOverallStats = function() {
    // Вақт машинаси режимида умумий статистикани янгиламаймиз
    if (window.isTimeMachineEnabled) {
        return;
    }

    const statusFilter = document.getElementById('status').value;

    let filteredStations = [...window.globalStations];

    // Применяем фильтр статуса
    if (statusFilter === 'online') {
        // Показываем только активные БС
        filteredStations = filteredStations.filter(station => {
            return !(station.status === true || station.status === 'true' || station.status === 1 || station.status === '1');
        });
    } else if (statusFilter === 'offline') {
        // Показываем только неактивные БС
        filteredStations = filteredStations.filter(station => {
            return station.status === true || station.status === 'true' || station.status === 1 || station.status === '1';
        });
    }

    const totalBs = filteredStations.length;
    // Проверяем статус как булево значение или строку
    const inactiveBs = filteredStations.filter(station => {
        return station.status === true || station.status === 'true' || station.status === 1 || station.status === '1';
    }).length;
    const activeBs = totalBs - inactiveBs;

    // Обновление счетчиков в сайдбаре
    updateSidebarCounters(totalBs, activeBs, inactiveBs);

    // Расчет процентов для прогресс-бара
    updateProgressBars(totalBs, activeBs, inactiveBs);

    // Обновляем статистику продолжительности простоя
    if (window.calculateDurationStats && window.globalStations) {
        window.calculateDurationStats(window.globalStations);
    }
}

// Обновление счетчиков в сайдбаре
function updateSidebarCounters(totalBs, activeBs, inactiveBs) {
    const totalElement = document.getElementById('total-bs-count');
    const activeElement = document.getElementById('active-bs-count');
    const inactiveElement = document.getElementById('inactive-bs-count');

    if (totalElement) totalElement.textContent = totalBs;
    if (activeElement) activeElement.textContent = activeBs;
    if (inactiveElement) inactiveElement.textContent = inactiveBs;
}

// Обновление прогресс-баров
function updateProgressBars(totalBs, activeBs, inactiveBs) {
    // Расчет процентов для прогресс-бара
    const activePercent = totalBs > 0 ? (activeBs / totalBs * 100).toFixed(1) : 0;
    const inactivePercent = totalBs > 0 ? (inactiveBs / totalBs * 100).toFixed(1) : 0;

    // Обновление прогресс-баров (если есть)
    const activeProgress = document.querySelector('.progress-active');
    const inactiveProgress = document.querySelector('.progress-inactive');

    if (activeProgress) {
        activeProgress.style.width = activePercent + '%';
    }

    if (inactiveProgress) {
        inactiveProgress.style.width = inactivePercent + '%';
    }
}

// Обновление общей статистики для машины времени
window.updateOverallStatsForTimeMachine = function(originalStations) {
    if (!originalStations || !window.globalStations) return;

    // Общее количество БС из оригинальных данных
    const totalBs = originalStations.length;
    
    // Количество аварийных БС из исторических данных
    const inactiveBs = window.globalStations.filter(station => 
        station.status === true || station.status === 'true' || station.status === 1 || station.status === '1'
    ).length;
    
    const activeBs = totalBs - inactiveBs;

    console.log('📊 Умумий статистика (машина времени):');
    console.log('  - Жами БС: ' + totalBs);
    console.log('  - Фаол БС: ' + activeBs);
    console.log('  - Аварияли БС: ' + inactiveBs);

    // Обновление счетчиков в сайдбаре
    updateSidebarCounters(totalBs, activeBs, inactiveBs);

    // Обновление прогресс-баров
    updateProgressBars(totalBs, activeBs, inactiveBs);
} 