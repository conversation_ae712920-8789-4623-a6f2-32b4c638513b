"""
BS маълумотларини олиш ва форматлаш функциялари
"""
from datetime import datetime, timedelta
from django.db.models import Q
from bsview.models import RegionUzb, AreaUzb, BsBeeline, Current_Alarms, Log_Alarms
from .utils import get_cabinet_type
from .alarm_logic import get_technology_from_alarm


def get_formatted_bs_data(base_stations=None, region_id=None, area_id=None, datetime_param=None, offset=0, limit=None, skip_alarms=False):
    """BS маълумотларини олиш ва форматлаш"""
    # Аргументлар асосида фильтрлаш
    query = BsBeeline.objects.all()
    if region_id: query = query.filter(region_id=region_id)
    if area_id: query = query.filter(area_id=area_id)
    
    # Базадан BS маълумотларини олиш
    if base_stations is None:
        base_stations = query.values('id', 'bsname', 'bsnum', 'lat', 'lon', 'area_id', 'region_id')
        if limit: base_stations = base_stations[int(offset):int(offset)+int(limit)]
    
    # Регион ва туман луғатларини олиш
    regions = {r['id']: r['name'] for r in RegionUzb.objects.all().values('id', 'name')}
    
    # Агар регион берилган бўлса, фақат шу региондаги туманларни олиш
    if region_id:
        areas = {a['id']: a['name'] for a in AreaUzb.objects.filter(region_id=region_id).values('id', 'name')}
    elif area_id:
        area = AreaUzb.objects.filter(id=area_id).first()
        areas = {area_id: area.name if area else ''}
    else:
        areas = {a['id']: a['name'] for a in AreaUzb.objects.all().values('id', 'name')}
    
    # Таърих учун сана олиш
    dt = None
    if datetime_param:
        try:
            # Парсим ISO формат и убираем timezone информацию для MySQL
            dt = datetime.fromisoformat(datetime_param.replace('Z', '+00:00'))
            if dt.tzinfo:
                # Конвертируем в naive datetime (без timezone)
                dt = dt.replace(tzinfo=None)
            print(f"[DEBUG] Parsed datetime: {dt}")
        except Exception as e:
            print(f"[ERROR] Failed to parse datetime '{datetime_param}': {e}")
            raise ValueError(f"Invalid datetime format: {datetime_param}")
    
    # Для отладки
    if dt:
        print(f"[DEBUG] get_formatted_bs_data: Processing historical data for {dt}")
    
    # BS маълумотларини форматлаш
    formatted_stations = []
    for bs in base_stations:
        # Координаталарни тўғирлаш
        if bs.get('lat'): bs['lat'] = bs['lat'].replace(',', '.')
        if bs.get('lon'): bs['lon'] = bs['lon'].replace(',', '.')
        
        # Базавий маълумотлар
        bs_data = {
            'id': bs['id'], 'name': bs['bsname'], 'bsName': bs['bsname'],
            'lat': bs['lat'], 'lon': bs['lon'], 'status': False,
            'calcTime': None, 'cabinetType': None, 'typeG': None,
            'bsnum': bs['bsnum'],  # БС рақамини қўшиш
            'region_id': bs['region_id'], 'region_name': regions.get(bs['region_id']),
            'area_id': bs['area_id'], 'area_name': areas.get(bs['area_id']),
            'alarms_by_tech': {}  # Технология бўйича авария маълумотлари
        }
        
        # Авария маълумотларини аниқлаш (фақат алоҳида сўралмаганда)
        if not skip_alarms:
            if dt:
                # Таърихий аварияларни технология бўйича текшириш
                bs_data = _process_historical_alarms(bs_data, bs, dt)
            else:
                # Жорий аварияларни технология бўйича текшириш
                bs_data = _process_current_alarms(bs_data, bs)

        formatted_stations.append(bs_data)
    
    return formatted_stations


def _process_historical_alarms(bs_data, bs, dt):
    """Таърихий аварияларни ишлаш"""
    bsnum_str = bs['bsnum']
    
    # Аниқ мослик учун фильтрлар рўйхати
    q_filters = _build_bs_filters(bs, bsnum_str)
    
    historical_alarms = Log_Alarms.objects.filter(
        q_filters, appeartime__lte=dt, cleartime__gt=dt
    )

    if historical_alarms.exists():
        bs_data['status'] = True
        technologies = []

        # Ҳар бир технология учун авария маълумотларини йиғиш
        for alarm in historical_alarms:
            tech = get_technology_from_alarm(alarm.alarmname)
            if tech:
                technologies.append(tech)

                # Технология бўйича авария маълумотларини сақлаш
                bs_data['alarms_by_tech'][tech] = {
                    'alarmname': alarm.alarmname,
                    'appeartime': alarm.appeartime.isoformat() if alarm.appeartime else None,
                    'cleartime': alarm.cleartime.isoformat() if alarm.cleartime else None,
                    'duration': (dt - alarm.appeartime).total_seconds() / 3600 if alarm.appeartime else None  # Соатларда
                }

        # Умумий маълумотлар
        bs_data['typeG'] = '/'.join(sorted(set(technologies))) if technologies else None
        bs_data['cabinetType'] = get_cabinet_type(bs['bsname'])

        # Энг узоқ давом этган аварияни топиш
        if bs_data['alarms_by_tech']:
            longest_duration = max(
                (dt - alarm.appeartime if alarm.appeartime else timedelta(0))
                for alarm in historical_alarms
            )
            hours, remainder = divmod(longest_duration.total_seconds(), 3600)
            bs_data['calcTime'] = f"{int(hours)} hours {int(remainder // 60)} minutes"

    return bs_data


def _process_current_alarms(bs_data, bs):
    """Жорий аварияларни ишлаш"""
    bsnum_str = bs['bsnum']
    
    # Аниқ мослик учун фильтрлар рўйхати
    q_filters = _build_bs_filters(bs, bsnum_str)
    
    current_alarms = Current_Alarms.objects.filter(q_filters)

    if current_alarms.exists():
        bs_data['status'] = True
        technologies = []

        # Ҳар бир технология учун авария маълумотларини йиғиш
        for alarm in current_alarms:
            # Технологияни аниқлаш (bscrnc асосида)
            if alarm.bscrnc.startswith("BSC"):
                tech = "2G"
                alarm_name = "OML Fault"
            elif alarm.bscrnc.startswith("RNC"):
                tech = "3G"
                alarm_name = "NodeB Unavailable"
            elif alarm.bscrnc.startswith("LTE"):
                tech = "4G"
                alarm_name = "S1ap Link Down"
            else:
                continue

            technologies.append(tech)

            # Технология бўйича авария маълумотларини сақлаш
            bs_data['alarms_by_tech'][tech] = {
                'alarmname': alarm_name,
                'appeartime': alarm.appeartime.isoformat() if alarm.appeartime else None,
                'cleartime': None,  # Жорий авария ҳали тугамаган
                'duration': str(alarm.calctime) if hasattr(alarm, 'calctime') else None
            }

        # Умумий маълумотлар
        bs_data['typeG'] = '/'.join(sorted(set(technologies))) if technologies else None
        bs_data['cabinetType'] = get_cabinet_type(bs['bsname'])

        # Энг узоқ давом этган аварияни топиш
        if current_alarms:
            first_alarm = current_alarms.first()
            bs_data['calcTime'] = str(first_alarm.calctime) if hasattr(first_alarm, 'calctime') else None

    return bs_data


def _build_bs_filters(bs, bsnum_str):
    """BS учун фильтрлар қуриш"""
    q_filters = Q()
    
    # 1. Аниқ БС номига мослик
    if bs['bsname']:
        q_filters |= Q(bsname__exact=bs['bsname'])
    
    # 2. БС номери билан тугайдиган номлар
    if bsnum_str:
        q_filters |= Q(bsname__endswith=f"_{bsnum_str}")
        q_filters |= Q(bsname__endswith=bsnum_str) & ~Q(bsname__regex=rf'{bsnum_str}\d+$')
        # Фақат БС номери бўлганлар учун
        q_filters |= Q(bsname__exact=bsnum_str)
    
    return q_filters 