// Alarm UI Module - Alarm Manager модулининг UI яратиш функциялари

window.AlarmUI = (function() {
    'use strict';
    
    // UI компонентларини яратиш
    function createAlarmUI() {
        // Map type control элементни топиш (endi tugma yaratmaymiz)
        const mapTypeControl = document.querySelector('.map-type-control');
        if (!mapTypeControl) {
            console.error('[AlarmUI] .map-type-control element not found!');
            return;
        }
        
        // Alarm selector панели
        const alarmSelectorHtml = `
            <div class="alarm-selector-panel" id="alarm-selector-panel" style="display: none;">
                <div class="alarm-selector-header">
                    <h3>${window.currentLanguage === 'ru' ? 'Выберите типы аварий' : 'Select alarm types'}</h3>
                    <button class="close-btn" id="close-alarm-selector">&times;</button>
                </div>
                <div class="alarm-selector-body">
                    <div class="alarm-types-loading">
                        <div class="spinner"></div>
                        <span>${window.currentLanguage === 'ru' ? 'Загрузка...' : 'Loading...'}</span>
                    </div>
                    <div class="alarm-select-all" style="display: none;">
                        <label>
                            <input type="checkbox" id="select-all-alarms">
                            <span>${window.currentLanguage === 'ru' ? 'Выбрать все' : 'Select all'}</span>
                        </label>
                    </div>
                    <div class="alarm-types-list" id="alarm-types-list" style="display: none;"></div>
                </div>
                <div class="alarm-selector-footer">
                    <button class="btn btn-primary" id="apply-alarm-selection">
                        ${window.currentLanguage === 'ru' ? 'Применить' : 'Apply'}
                    </button>
                    <button class="btn btn-secondary" id="cancel-alarm-selection">
                        ${window.currentLanguage === 'ru' ? 'Отмена' : 'Cancel'}
                    </button>
                </div>
            </div>
        `;
        
        // Body га қўшиш
        document.body.insertAdjacentHTML('beforeend', alarmSelectorHtml);
    }
    
    // Alarm selector ни кўрсатиш
    function showAlarmSelector() {
        const panel = document.getElementById('alarm-selector-panel');
        if (panel) {
            panel.style.display = 'block';
            // Рўйхатни янгилаш
            if (window.AlarmData && window.AlarmData.updateAlarmTypesList) {
                window.AlarmData.updateAlarmTypesList();
            }
        }
    }
    
    // Alarm selector ни яшириш
    function hideAlarmSelector() {
        const panel = document.getElementById('alarm-selector-panel');
        if (panel) {
            panel.style.display = 'none';
        }
    }
    
    // Авария турлари рўйхатини янгилаш
    function updateAlarmTypesList(alarmTypesCache, selectedAlarmTypes) {
        const listContainer = document.getElementById('alarm-types-list');
        const loadingDiv = document.querySelector('.alarm-types-loading');
        const selectAllDiv = document.querySelector('.alarm-select-all');
        
        if (!listContainer || !alarmTypesCache) return;
        
        // Loading ни яшириш
        if (loadingDiv) loadingDiv.style.display = 'none';
        if (selectAllDiv) selectAllDiv.style.display = 'block';
        listContainer.style.display = 'block';
        
        // Рўйхатни тозалаш
        listContainer.innerHTML = '';
        
        // Checkbox лар яратиш
        alarmTypesCache.forEach((alarmType, index) => {
            const isChecked = selectedAlarmTypes.includes(alarmType);
            const checkboxHtml = `
                <div class="alarm-type-item">
                    <label>
                        <input type="checkbox" 
                               class="alarm-type-checkbox"
                               value="${alarmType}" 
                               id="alarm-type-${index}"
                               ${isChecked ? 'checked' : ''}>
                        <span>${alarmType}</span>
                    </label>
                </div>
            `;
            listContainer.insertAdjacentHTML('beforeend', checkboxHtml);
        });
        
        // Select all checkbox ҳолатини янгилаш
        updateSelectAllState();
    }
    
    // Select all checkbox ҳолатини янгилаш
    function updateSelectAllState() {
        const selectAllCheckbox = document.getElementById('select-all-alarms');
        const typeCheckboxes = document.querySelectorAll('.alarm-type-checkbox');
        
        if (!selectAllCheckbox) return;
        
        const totalCheckboxes = typeCheckboxes.length;
        const checkedCheckboxes = document.querySelectorAll('.alarm-type-checkbox:checked').length;
        
        if (checkedCheckboxes === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (checkedCheckboxes === totalCheckboxes) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }
    
    // Select all toggle
    function toggleSelectAll(isChecked) {
        const typeCheckboxes = document.querySelectorAll('.alarm-type-checkbox');
        typeCheckboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
        });
    }
    
    // Танловни йиғиш
    function getSelectedAlarmTypes() {
        const selectedTypes = [];
        const checkboxes = document.querySelectorAll('#alarm-types-list input[type="checkbox"]:checked');
        
        checkboxes.forEach(checkbox => {
            selectedTypes.push(checkbox.value);
        });
        
        return selectedTypes;
    }
    
    // UI ни янгилаш (режим фаоллашганда)
    function updateUIForAlarmMode(isEnabled) {
        const toggleBtn = document.querySelector('.alarm-toggle-btn');
        if (toggleBtn) {
            if (isEnabled) {
                toggleBtn.classList.add('active');
            } else {
                toggleBtn.classList.remove('active');
            }
        }
        
        // Body га alarm-mode классини қўшиш/олиб ташлаш
        if (isEnabled) {
            document.body.classList.add('alarm-mode');
        } else {
            document.body.classList.remove('alarm-mode');
        }
    }
    
    // Public API
    return {
        createAlarmUI,
        showAlarmSelector,
        hideAlarmSelector,
        updateAlarmTypesList,
        updateSelectAllState,
        toggleSelectAll,
        getSelectedAlarmTypes,
        updateUIForAlarmMode
    };
})(); 