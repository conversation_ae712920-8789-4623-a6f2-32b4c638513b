// Машина времени - слайдер бошқаруви модули

// Инициализация слайдера времени
window.initializeTimeSlider = function() {
    const slider = document.getElementById('time-slider');
    const leftArrow = document.getElementById('time-slider-left');
    const rightArrow = document.getElementById('time-slider-right');

    if (!slider) {
        console.warn('time-slider элементи топилмади');
        return;
    }

    // Обработчик изменения слайдера
    slider.addEventListener('input', function () {
        if (window.isTimeMachineEnabled) {
            updateTimeFromSlider(this.value);
        }
    });

    // Левая стрелка (назад)
    if (leftArrow) {
        leftArrow.addEventListener('click', function () {
            if (window.isTimeMachineEnabled) {
                if (window.adjustTime) window.adjustTime(-5); // 5 минут назад
                updateSliderFromTime();
            }
        });
    }

    // Правая стрелка (вперед)
    if (rightArrow) {
        rightArrow.addEventListener('click', function () {
            if (window.isTimeMachineEnabled) {
                if (window.adjustTime) window.adjustTime(5); // 5 минут вперед
                updateSliderFromTime();
            }
        });
    }
}

// Обновление времени из слайдера
function updateTimeFromSlider(sliderValue) {
    const totalMinutes = parseInt(sliderValue);
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;

    // Сохраняем текущую дату
    const currentDate = document.getElementById('time-machine-date').value || new Date().toISOString().split('T')[0];

    // Устанавливаем новое время
    document.getElementById('time-machine-date').value = currentDate;
    document.getElementById('time-machine-hour').value = hours;
    document.getElementById('time-machine-minute').value = Math.floor(minutes / 5) * 5; // Округляем до 5 минут

    // Применяем изменения
    if (window.applyTimeMachine) {
        window.applyTimeMachine();
    }
}

// Обновление слайдера из времени
function updateSliderFromTime() {
    if (!window.selectedDateTime) return;

    const hours = window.selectedDateTime.getHours();
    const minutes = window.selectedDateTime.getMinutes();
    const totalMinutes = hours * 60 + minutes;

    const slider = document.getElementById('time-slider');
    if (slider) {
        slider.value = totalMinutes;
    }
}

// Экспорт функции для глобального использования
window.updateSliderFromTime = updateSliderFromTime; 