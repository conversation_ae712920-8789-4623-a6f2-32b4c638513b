/* Off Stations Panel - БС лар тушганда кўринадиган панел
   Кўрсатиш шартлари:
   - Камида 3 та БС OFF статусда бўлиши керак
   - OFF БС лар бир-бирига тўғридан-тўғри қўшни бўлиши керак
   - Қўшни = ўзаро энг яқин OFF БС лар
*/

/* ========== ХАБАРЛАР СИСТЕМАСИ ОЛИБ ТАШЛАНДИ ========== */

/* Причина индикатори (панел учун сақланган) */
.reason-indicator {
    color: #007bff !important;
    font-size: 12px;
    cursor: help;
    opacity: 0.8;
    transition: opacity 0.2s;
}

.reason-indicator:hover {
    opacity: 1;
}

/* Toggle кнопка - авария режими кнопкаси билан бир хил жойда */
.off-stations-toggle {
    position: absolute;
    top: 212px; /* Alarm режими кнопкасидан пастда */
    left: 311px;
    z-index: 1001; /* Панелдан юқорида */
}

.off-stations-toggle-btn {
    width: 31px;
    height: 31px;
    border-radius: 4px;
    background: white;
    border: 2px solid rgba(0, 0, 0, 0.2);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: #666;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
}

.off-stations-toggle-btn:hover {
    background: #ffebee;
    border-color: #dc3545;
    color: #dc3545;
}

.off-stations-toggle-btn.active {
    background: #dc3545;
    border-color: #dc3545;
    color: white;
}

.off-stations-toggle-btn svg,
.off-stations-toggle-btn img {
    width: 18px;
    height: 18px;
}

/* РЦМУ No Traffic Toggle кнопка */
.rcmu-no-traff-toggle {
    position: absolute;
    top: 250px; /* Off Stations кнопкасидан пастда */
    left: 311px;
    z-index: 1001;
}

.rcmu-no-traff-toggle-btn {
    width: 31px;
    height: 31px;
    border-radius: 4px;
    background: white;
    border: 2px solid rgba(0, 0, 0, 0.2);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: #666;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
}

.rcmu-no-traff-toggle-btn:hover {
    background: #e3f2fd;
    border-color: #2196f3;
    color: #2196f3;
}

.rcmu-no-traff-toggle-btn.active {
    background: #2196f3;
    border-color: #2196f3;
    color: white;
}

.rcmu-no-traff-toggle-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f5f5f5;
    color: #999;
}

.rcmu-no-traff-toggle-btn i {
    font-size: 16px;
}

/* РЦМУ маркерлар стиллари */
.rcmu-marker {
    z-index: 1000 !important;
}

.rcmu-popup .leaflet-popup-content-wrapper {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.rcmu-popup .leaflet-popup-content {
    margin: 12px 16px;
    line-height: 1.4;
}

.rcmu-popup .leaflet-popup-tip {
    background: #fff;
}

.off-stations-panel {
    position: fixed;
    right: 10px; /* Ўнг томонда */
    bottom: 30px; /* Copyright тепасида */
    width: 280px; /* Кичикроқ кенглик */
    max-height: 400px; /* Максимал баландлик чегараси */
    background: rgba(255, 255, 255, 0.7); /* Downtime Duration билан бир хил */
    border-radius: 6px; /* Downtime Duration билан бир хил */
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
    z-index: 999; /* Кнопкалардан пастроқ */
    overflow: hidden;
    transition: all 0.3s ease;
    display: none; /* Бошланғич ҳолатда яширилган */
}

.off-stations-panel.visible {
    display: block;
}



/* Панел header */
.panel-header {
    background: rgba(220, 53, 69, 0.85); /* Легенда стилига мослаштирилган */
    color: white;
    padding: 8px 10px; /* Кичикроқ padding */
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    margin: 0;
    font-size: 12px; /* Кичикроқ */
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 5px;
}

.panel-header h3 i {
    font-size: 14px; /* Кичикроқ */
}

.station-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 1px 5px; /* Кичикроқ */
    border-radius: 8px;
    font-size: 11px; /* Кичикроқ */
    font-weight: 500;
}



/* Панел контент */
.panel-content {
    max-height: 340px; /* Header ни ҳисобга олиб */
    overflow-y: auto;
    overflow-x: hidden;
}

.panel-content::-webkit-scrollbar {
    width: 4px;
}

.panel-content::-webkit-scrollbar-track {
    background: transparent;
}

.panel-content::-webkit-scrollbar-thumb {
    background: rgba(136, 136, 136, 0.5);
    border-radius: 2px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
    background: rgba(85, 85, 85, 0.7);
}

/* БС группалари */
.station-group {
    padding: 10px 12px;
    border-bottom: 1px solid #e0e0e0;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: visible; /* tooltip учун */
}



.station-group:hover {
    background: #f5f5f5;
    box-shadow: inset 0 0 0 2px #dc3545;
}

.station-group.pulse {
    animation: pulse 2s ease-in-out;
}

@keyframes pulse {
    0% {
        background: rgba(220, 53, 69, 0.1);
    }
    50% {
        background: rgba(220, 53, 69, 0.3);
    }
    100% {
        background: rgba(220, 53, 69, 0.1);
    }
}

.station-group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.location-name {
    font-weight: 600;
    color: #333;
    font-size: 13px;
}

.station-count-badge {
    background: rgba(220, 53, 69, 0.9);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 500;
}

.station-info {
    font-size: 11px;
    color: #666;
    line-height: 1.3;
}

.station-list {
    margin-top: 6px;
    padding-left: 12px;
}

.station-name {
    font-size: 10px;
    color: #777;
    margin: 2px 0;
    display: flex;
    align-items: center;
    gap: 4px;
}

.station-name i {
    color: #dc3545;
    font-size: 6px;
}

.downtime-info {
    font-size: 10px;
    color: #999;
    margin-top: 3px;
}

/* Бўш ҳолат */
.empty-state {
    padding: 30px 15px;
    text-align: center;
    color: #999;
}

.empty-state i {
    font-size: 36px;
    margin-bottom: 8px;
    opacity: 0.3;
}

.empty-state p {
    margin: 0;
    font-size: 12px;
}

/* Responsive */
@media (max-width: 768px) {
    .off-stations-toggle {
        top: 185px;
        left: 10px;
    }
    
    .off-stations-toggle-btn {
        width: 30px;
        height: 30px;
    }
    
    .off-stations-panel {
        right: 10px;
        bottom: 45px;
        left: auto;
        width: calc(100% - 80px);
        max-width: 260px;
        max-height: 350px;
    }
}

/* Анимация появления */
.station-group.new-item {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(20px); /* Ўнг томондан */
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Индикатор автообновления */
.auto-update-indicator {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 6px;
    height: 6px;
    background: #28a745;
    border-radius: 50%;
    animation: blink 2s infinite;
}

.auto-update-indicator.paused {
    background: #ffc107;
    animation: none;
}

@keyframes blink {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.3;
    }
} 