"""
Қидирув таклифлари ва suggestion функциялари
"""
import requests
from django.db.models import Q
from rest_framework.decorators import api_view
from rest_framework.response import Response
from bsview.models import RegionUzb, AreaUzb, BsBeeline
from .translation_utils import translate_region_name, translate_area_name


@api_view(['GET'])
def search_suggestions(request):
    """Қидирув учун таклифлар API - фақат географик жойлар"""
    try:
        query = request.query_params.get('q', '')
        lang = request.query_params.get('lang', 'ru')  # Тил параметрини олиш
        
        if len(query) < 2:
            return Response({'suggestions': []})
        
        suggestions = []
        
        # 1. Географик жойлардан қидирув
        # OSM Nominatim орқали таклифлар олиш
        url = "https://nominatim.openstreetmap.org/search"
        params = {
            'format': 'json',
            'q': query,
            'countrycodes': 'uz',
            'limit': 5,
            'addressdetails': 1,
            'accept-language': 'en' if lang == 'en' else 'ru'  # Тилни узатиш
        }
        
        headers = {
            'User-Agent': 'Django QNP Map Application/1.0'
        }
        
        try:
            response = requests.get(url, params=params, headers=headers, timeout=3)
            if response.status_code == 200:
                results = response.json()
                for item in results:
                    suggestions.append({
                        'text': item['display_name'],
                        'lat': float(item['lat']),
                        'lon': float(item['lon']),
                        'type': 'location'
                    })
        except:
            pass  # Хатоларни эътиборга олмаймиз
        
        # 2. Вилоят ва туман номларидан қидирув
        if len(suggestions) < 8:
            # Вилоятлардан қидирув
            region_matches = RegionUzb.objects.filter(name__icontains=query)[:3]
            for region in region_matches:
                # Вилоят марказининг координаталарини топиш
                region_bs = BsBeeline.objects.filter(region=region).first()
                if region_bs:
                    # Инглизча бўлса, translateRegionName функциясини ишлатамиз
                    region_name = translate_region_name(region.name) if lang == 'en' else region.name
                    suggestions.append({
                        'text': region_name,
                        'lat': float(region_bs.lat) if region_bs.lat else 41.3,
                        'lon': float(region_bs.lon) if region_bs.lon else 69.3,
                        'type': 'region',
                        'region_id': region.id
                    })
            
            # Туманлардан қидирув
            area_matches = AreaUzb.objects.filter(name__icontains=query).select_related('region')[:3]
            for area in area_matches:
                # Туман марказининг координаталарини топиш
                area_bs = BsBeeline.objects.filter(area=area).first()
                if area_bs:
                    # Инглизча бўлса, translateAreaName функциясини ишлатамиз
                    if lang == 'en':
                        area_name = f"{translate_area_name(area.name)}, {translate_region_name(area.region.name)}"
                    else:
                        area_name = f"{area.name}, {area.region.name}"
                    
                    suggestions.append({
                        'text': area_name,
                        'lat': float(area_bs.lat) if area_bs.lat else 41.3,
                        'lon': float(area_bs.lon) if area_bs.lon else 69.3,
                        'type': 'area',
                        'area_id': area.id
                    })
        
        return Response({'suggestions': suggestions[:10]})  # Максимум 10 та натижа
        
    except Exception as e:
        print(f"Search suggestions error: {str(e)}")
        return Response({'suggestions': []}) 