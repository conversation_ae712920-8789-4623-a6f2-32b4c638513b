// Вилоятлар статистикаси модули

// Подготовка статистики регионов
window.prepareRegionsStats = function () {
    const regionsTableBody = document.getElementById('regions-table-body');
    if (!regionsTableBody) return;

    // Очистка таблицы
    regionsTableBody.innerHTML = '';

    // Добавляем строку "Все регионы"  
    createAllRegionsRow(regionsTableBody);

    // Создание строк для каждого региона
    window.globalRegions.forEach(region => {
        // "Рес. Узбекистан" ни рўйхатдан чиқариб ташлаш
        if (region.name === 'Рес. Узбекистан' || region.name === 'Респ. Узбекистан') {
            return; // Бу регионни қўшмаймиз
        }
        
        const row = createRegionRow(region);
        regionsTableBody.appendChild(row);
    });

    // Обновление статистики
    window.updateRegionsStats();

    // Заполняем скрытый селект регионов
    populateRegionSelect();
}

// Обновление статистики регионов
window.updateRegionsStats = function () {
    const regionRows = document.querySelectorAll('.region-row');
    const currentRegionId = document.getElementById('region').value;

    // Общая статистика для "Все регионы"
    let totalAllRegions = 0;
    let activeAllRegions = 0;
    let inactiveAllRegions = 0;

    regionRows.forEach(row => {
        const regionId = row.dataset.regionId;

        // Обработка строки "Все регионы"
        if (regionId === '') {
            return; // Пропускаем, обновим в конце
        }

        // ВАЖНО: Для статистики всегда используем ВСЕ БС региона, без фильтра статуса
        const regionStations = window.globalStations.filter(station => station.region_id == regionId);

        const totalCount = regionStations.length;
        const inactiveCount = regionStations.filter(station => {
            const isOffline = station.status === true || station.status === 'true' || station.status === 1 || station.status === '1';
            return isOffline;
        }).length;
        const activeCount = totalCount - inactiveCount;

        // Обновление значений
        row.querySelector('.region-total').textContent = totalCount;
        row.querySelector('.region-active').textContent = activeCount;
        row.querySelector('.region-inactive').textContent = inactiveCount;

        // Расчет процента неактивных БС
        const inactivePercent = totalCount > 0 ? ((inactiveCount / totalCount) * 100).toFixed(1) : 0;
        row.querySelector('.region-percent').textContent = inactivePercent + '%';

        // Накапливаем общую статистику
        totalAllRegions += totalCount;
        activeAllRegions += activeCount;
        inactiveAllRegions += inactiveCount;

        // Обновление названия региона при смене языка
        const regionData = window.globalRegions.find(r => r.id == regionId);
        if (regionData) {
            row.querySelector('.region-name').textContent = window.currentLanguage === 'ru' ?
                regionData.name : window.translateRegionName(regionData.name);
        }

        // Выделение активного региона
        if (regionId == currentRegionId) {
            row.classList.add('active');
        } else {
            row.classList.remove('active');
        }
    });

    // Обновление строки "Все регионы"
    updateAllRegionsRow(totalAllRegions, activeAllRegions, inactiveAllRegions, currentRegionId);

    // Обновляем статистику продолжительности простоя
    if (!window.isTimeMachineEnabled && window.calculateDurationStats && window.globalStations) {
        window.calculateDurationStats(window.globalStations);
    }
}

// Обновление строки "Все регионы"
function updateAllRegionsRow(totalAllRegions, activeAllRegions, inactiveAllRegions, currentRegionId) {
    const allRegionsRow = document.querySelector('.region-row[data-region-id=""]');
    if (allRegionsRow) {
        allRegionsRow.querySelector('.all-regions-total').textContent = totalAllRegions;
        allRegionsRow.querySelector('.all-regions-active').textContent = activeAllRegions;
        allRegionsRow.querySelector('.all-regions-inactive').textContent = inactiveAllRegions;

        // Расчет процента для всех регионов
        const allInactivePercent = totalAllRegions > 0 ? ((inactiveAllRegions / totalAllRegions) * 100).toFixed(1) : 0;
        allRegionsRow.querySelector('.all-regions-percent').textContent = allInactivePercent + '%';

        // Обновление названия при смене языка
        const nameCell = allRegionsRow.querySelector('.region-name');
        nameCell.textContent = window.currentLanguage === 'ru' ? 'Все регионы' : 'All regions';

        // Выделение если не выбран конкретный регион
        if (!currentRegionId) {
            allRegionsRow.classList.add('active');
        } else {
            allRegionsRow.classList.remove('active');
        }
    }
}

// Создание строки "Все регионы"
function createAllRegionsRow(regionsTableBody) {
    const allRegionsRow = document.createElement('tr');
    allRegionsRow.className = 'region-row active';
    allRegionsRow.dataset.regionId = '';

    // Название
    const allNameCell = document.createElement('td');
    allNameCell.className = 'region-name';
    allNameCell.style.fontWeight = 'bold';
    allNameCell.textContent = window.currentLanguage === 'ru' ? 'Все регионы' : 'All regions';
    allRegionsRow.appendChild(allNameCell);

    // Всего БС
    const allTotalCell = document.createElement('td');
    allTotalCell.className = 'region-total all-regions-total';
    allTotalCell.textContent = '0';
    allTotalCell.dataset.statType = 'all';
    allRegionsRow.appendChild(allTotalCell);

    // Активные БС
    const allActiveCell = document.createElement('td');
    allActiveCell.className = 'region-active all-regions-active';
    allActiveCell.textContent = '0';
    allActiveCell.dataset.statType = 'online';
    allRegionsRow.appendChild(allActiveCell);

    // Неактивные БС
    const allInactiveCell = document.createElement('td');
    allInactiveCell.className = 'region-inactive all-regions-inactive';
    allInactiveCell.textContent = '0';
    allInactiveCell.dataset.statType = 'offline';
    allRegionsRow.appendChild(allInactiveCell);

    // Процент
    const allPercentCell = document.createElement('td');
    allPercentCell.className = 'region-percent all-regions-percent';
    allPercentCell.textContent = '0%';
    allRegionsRow.appendChild(allPercentCell);

    // Обработчик клика для "Все регионы"
    allRegionsRow.addEventListener('click', function (e) {
        handleAllRegionsClick(e, this);
    });

    regionsTableBody.appendChild(allRegionsRow);
}

// Создание строки региона
function createRegionRow(region) {
    const row = document.createElement('tr');
    row.className = 'region-row';
    row.dataset.regionId = region.id;

    // Название региона
    const nameCell = document.createElement('td');
    nameCell.className = 'region-name';
    nameCell.textContent = window.currentLanguage === 'ru' ?
        region.name : window.translateRegionName(region.name);
    row.appendChild(nameCell);

    // Всего БС
    const totalCell = document.createElement('td');
    totalCell.className = 'region-total';
    totalCell.textContent = '0';
    totalCell.dataset.statType = 'all';
    row.appendChild(totalCell);

    // Активные БС
    const activeCell = document.createElement('td');
    activeCell.className = 'region-active';
    activeCell.textContent = '0';
    activeCell.dataset.statType = 'online';
    row.appendChild(activeCell);

    // Неактивные БС
    const inactiveCell = document.createElement('td');
    inactiveCell.className = 'region-inactive';
    inactiveCell.textContent = '0';
    inactiveCell.dataset.statType = 'offline';
    row.appendChild(inactiveCell);

    // Процент
    const percentCell = document.createElement('td');
    percentCell.className = 'region-percent';
    percentCell.textContent = '0%';
    row.appendChild(percentCell);

    // Обработчик клика
    row.addEventListener('click', function (e) {
        handleRegionClick(e, this, region);
    });

    return row;
}

// Обработчик клика для "Все регионы"
function handleAllRegionsClick(e, rowElement) {
    const statType = e.target.dataset.statType || 'all';
    
    document.querySelectorAll('.region-row').forEach(r => r.classList.remove('active'));
    rowElement.classList.add('active');

    document.getElementById('region').value = '';
    document.getElementById('area').value = '';
    document.getElementById('area').disabled = true;

    const areaFilterGroup = document.querySelector('.filter-group:has(#area)');
    if (areaFilterGroup) {
        areaFilterGroup.classList.remove('show');
    }
    
    // Районлар статистикасини яшириш
    const areasContainer = document.getElementById('areas-stats-container');
    const areasDivider = document.getElementById('areas-stats-divider');
    if (areasContainer) {
        areasContainer.style.display = 'none';
    }
    if (areasDivider) {
        areasDivider.style.display = 'none';
    }

    document.getElementById('status').value = statType;
    document.getElementById('duration-filter').value = '';

    if (window.autoCenterEnabled !== false) {
        window.mymap.setView([41.3, 69.3], 6);
    }

    // Авария режимида эмасми текшириш
    if (window.AlarmManager && typeof window.AlarmManager.isEnabled === 'function' && window.AlarmManager.isEnabled()) {
        // Авария режимида - фақт авария маълумотларини янгилаймиз
        window.AlarmManager.refresh(true, true);
    } else {
        // Нормал режимда - одатдагидек ишлаймиз
        window.applyCurrentFilters();

        if (window.calculateDurationStats && window.globalStations) {
            window.calculateDurationStats(window.globalStations);
        }

        window.updateRegionsStats();
        
        // РЦМУ маркерларини янгилаш
        if (window.refreshRcmuMarkersForFilters) {
            window.refreshRcmuMarkersForFilters();
        }
    }
}

// Обработчик клика по региону
function handleRegionClick(e, rowElement, region) {
    const statType = e.target.dataset.statType || 'all';
    e.stopPropagation();
    
    document.querySelectorAll('.region-row').forEach(r => r.classList.remove('active'));
    rowElement.classList.add('active');

    const regionSelect = document.getElementById('region');
    const oldValue = regionSelect.value;
    regionSelect.value = region.id;

    document.getElementById('area').value = '';
    document.getElementById('area').disabled = false;

    const areaFilterGroup = document.querySelector('.filter-group:has(#area)');
    if (areaFilterGroup) {
        areaFilterGroup.classList.add('show');
    }

    document.getElementById('status').value = statType;
    document.getElementById('duration-filter').value = '';

    centerMapOnRegion(region);

    if (oldValue !== region.id) {
        loadRegionAreas(region);
    }
    
    // Районлар статистикасини кўрсатиш
    if (window.prepareAreasStats) {
        window.prepareAreasStats(region.id);
    }

    // Авария режимида эмасми текшириш
    if (window.AlarmManager && typeof window.AlarmManager.isEnabled === 'function' && window.AlarmManager.isEnabled()) {
        // Авария режимида - фақат авария маълумотларини янгилаймиз
        window.AlarmManager.refresh(true, true);
    } else {
        // Нормал режимда - одатдагидек ишлаймиз
        window.applyCurrentFilters();
        window.updateRegionsStats();
        
        // РЦМУ маркерларини янгилаш
        if (window.refreshRcmuMarkersForFilters) {
            window.refreshRcmuMarkersForFilters();
        }
    }
}

// Центрирование карты на регионе
function centerMapOnRegion(region) {
    const regionStations = window.globalStations.filter(station => station.region_id == region.id);
    
    if (window.autoCenterEnabled !== false) {
        if (regionStations.length > 0) {
            const bounds = L.latLngBounds();
            regionStations.forEach(station => {
                const lat = parseFloat(station.lat);
                const lon = parseFloat(station.lon);
                if (!isNaN(lat) && !isNaN(lon)) {
                    bounds.extend([lat, lon]);
                }
            });
            
            if (bounds.isValid()) {
                window.mymap.fitBounds(bounds, {
                    padding: [50, 50],
                    maxZoom: 10
                });
            }
        } else {
            if (window.regionCenters && window.regionCenters[region.id]) {
                window.mymap.setView(window.regionCenters[region.id], 9);
            }
        }
    }
}

// Загрузка районов региона
function loadRegionAreas(region) {
    window.loadAreasForRegion(region.id).then(areas => {
        const areaSelect = document.getElementById('area');
        if (areaSelect) {
            areaSelect.innerHTML = '<option value="">' + 
                (window.currentLanguage === 'ru' ? 'Все районы' : 'All areas') + 
                '</option>';
            
            areas.forEach(area => {
                const option = document.createElement('option');
                option.value = area.id;
                option.dataset.originalName = area.name;
                option.textContent = window.currentLanguage === 'ru' ? 
                    area.name : window.translateAreaName(area.name);
                areaSelect.appendChild(option);
            });
            
            areaSelect.disabled = false;
        }
    }).catch(error => {
        console.error('❌ Туманларни юклашда хато:', error);
    });
}

// Заполнение скрытого селекта регионов
function populateRegionSelect() {
    const regionSelect = document.getElementById('region');
    if (regionSelect && regionSelect.options.length <= 1) {
        window.globalRegions.forEach(region => {
            if (region.name === 'Рес. Узбекистан' || region.name === 'Респ. Узбекистан') {
                return;
            }
            const option = document.createElement('option');
            option.value = region.id;
            option.textContent = region.name;
            regionSelect.appendChild(option);
        });
    }
}

// Авария режимида регион статистикасини янгилаш
window.updateRegionsTable = function(regionStats) {
    if (!regionStats) return;
    
    const regionRows = document.querySelectorAll('.region-row');
    
    // Умумий статистика
    let totalAll = 0;
    let totalOnline = 0;
    let totalOffline = 0;
    
    regionRows.forEach(row => {
        const regionId = row.dataset.regionId;
        
        if (regionId === '') {
            return; // "Барча регионлар" ни охирида янгилаймиз
        }
        
        const stats = regionStats[regionId];
        if (stats) {
            // Backend дан келган форматга мосланиш
            const totalBs = stats.total_bs || 0;
            const bsWithAlarms = stats.bs_with_alarms || 0;
            const activeBs = totalBs - bsWithAlarms;
            
            row.querySelector('.region-total').textContent = totalBs;
            row.querySelector('.region-active').textContent = activeBs;
            row.querySelector('.region-inactive').textContent = bsWithAlarms;
            row.querySelector('.region-percent').textContent = (stats.percentage || 0) + '%';
            
            totalAll += totalBs;
            totalOnline += activeBs;
            totalOffline += bsWithAlarms;
        } else {
            // Агар статистика йўқ бўлса, 0 кўрсатамиз
            row.querySelector('.region-total').textContent = '0';
            row.querySelector('.region-active').textContent = '0';
            row.querySelector('.region-inactive').textContent = '0';
            row.querySelector('.region-percent').textContent = '0%';
        }
    });
    
    // "Барча регионлар" ни янгилаш
    const allRegionsRow = document.querySelector('.region-row[data-region-id=""]');
    if (allRegionsRow) {
        allRegionsRow.querySelector('.all-regions-total').textContent = totalAll;
        allRegionsRow.querySelector('.all-regions-active').textContent = totalOnline;
        allRegionsRow.querySelector('.all-regions-inactive').textContent = totalOffline;
        
        const allPercent = totalAll > 0 ? ((totalOffline / totalAll) * 100).toFixed(1) : 0;
        allRegionsRow.querySelector('.all-regions-percent').textContent = allPercent + '%';
    }
} 