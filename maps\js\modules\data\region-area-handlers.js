// Вилоят ва туман ишловчилари

// Обработчик изменения региона
function handleRegionChange(e) {
    // Проверяем, что изменение произошло от пользователя (select), а не программно
    if (e && e.isTrusted === false) {
        return; // Игнорируем программные изменения
    }

    const regionId = this.value;
    const statusValue = document.getElementById('status').value;

    // Сброс фильтра района
    document.getElementById('area').value = '';
    document.getElementById('area').disabled = true;

    // Показываем/скрываем фильтр района
    const areaFilterGroup = document.querySelector('.filter-group:has(#area)');
    if (areaFilterGroup) {
        if (regionId) {
            areaFilterGroup.classList.add('show');
        } else {
            areaFilterGroup.classList.remove('show');
        }
    }

    if (regionId) {
        // Фильтрация по региону
        const regionStations = window.globalStations.filter(point => point.region_id == regionId);

        // Центрирование карты
        if (regionStations.length > 0 && window.autoCenterEnabled !== false) {
            const center = window.calculateCenter(regionStations);
            if (center) {
                window.mymap.setView([center.lat, center.lon], 10);
            } else if (window.regionCenters[regionId]) {
                window.mymap.setView(window.regionCenters[regionId], 10);
            }
        }

        // Применение фильтров
        let filteredPoints = [...regionStations];

        // Фильтр по поиску БС
        const bsSearch = document.getElementById('bs-search').value;
        if (bsSearch) {
            filteredPoints = window.filterByBsName(filteredPoints, bsSearch);
        }

        // Применение всех фильтров
        window.applyCurrentFilters();

        // Загрузка районов
        window.loadAreasForRegion(regionId).then(areas => {
            const areaSelect = document.getElementById('area');
            areaSelect.innerHTML = '<option value="">' + window.translations[window.currentLanguage]['all_areas'] + '</option>';

            areas.forEach(area => {
                const option = document.createElement('option');
                option.value = area.id;
                // Сохраняем оригинальное русское название
                option.dataset.originalName = area.name;
                // Показываем текущий перевод
                option.textContent = window.currentLanguage === 'ru' ?
                    area.name : window.translateAreaName(area.name);
                areaSelect.appendChild(option);
            });

            areaSelect.disabled = false;
        });
    } else {
        // Возврат к общему виду - фақат автоматик марказлаштириш ёқилган бўлса
        if (window.autoCenterEnabled !== false) {
            window.mymap.setView([41.3, 69.3], 6);
        }
        window.applyCurrentFilters();
    }

    // Обновление визуального выделения
    window.updateRegionsStats();
}

// Обработчик изменения района
function handleAreaChange() {
    const areaId = this.value;
    const regionId = document.getElementById('region').value;
    const statusValue = document.getElementById('status').value;

    if (areaId) {
        // Фильтрация по району
        const areaStations = window.globalStations.filter(point => point.area_id == areaId);

        // Центрирование карты - фақат автоматик марказлаштириш ёқилган бўлса
        if (areaStations.length > 0 && window.autoCenterEnabled !== false) {
            const center = window.calculateCenter(areaStations);
            if (center) {
                window.mymap.setView([center.lat, center.lon], 12);
            }
        }

        // Применение фильтров
        let filteredPoints = [...areaStations];

        // Фильтр по поиску БС
        const bsSearch = document.getElementById('bs-search').value;
        if (bsSearch) {
            filteredPoints = window.filterByBsName(filteredPoints, bsSearch);
        }

        // Применение всех фильтров
        window.applyCurrentFilters();
    } else if (regionId) {
        // Возврат к виду региона
        const regionStations = window.globalStations.filter(point => point.region_id == regionId);

        // Марказлаштириш - фақат автоматик марказлаштириш ёқилган бўлса
        if (regionStations.length > 0 && window.autoCenterEnabled !== false) {
            const center = window.calculateCenter(regionStations);
            if (center) {
                window.mymap.setView([center.lat, center.lon], 10);
            } else if (window.regionCenters[regionId]) {
                window.mymap.setView(window.regionCenters[regionId], 10);
            }
        }

        // Применение фильтров
        let filteredPoints = [...regionStations];

        const bsSearch = document.getElementById('bs-search').value;
        if (bsSearch) {
            filteredPoints = window.filterByBsName(filteredPoints, bsSearch);
        }

        window.applyCurrentFilters();
    } else {
        window.applyCurrentFilters();
    }

    // Обновление визуального выделения
    window.updateRegionsStats();
}

// Export функций для использования в других модулях
window.handleRegionChange = handleRegionChange;
window.handleAreaChange = handleAreaChange; 