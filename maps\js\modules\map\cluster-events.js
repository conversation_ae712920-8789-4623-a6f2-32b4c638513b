// Кластер ҳодисалари модули - Cluster event handlers

// Кластер маркерига event handler'лар қўшиш
window.addClusterEventHandlers = function(clusterMarker, markers, centerLat, centerLng, type) {
    
    // Click handler функцияси
    function handleClusterClick(e, eventType) {
        
        // Event ни тўхтатиш
        if (e.originalEvent) {
            e.originalEvent.preventDefault();
            e.originalEvent.stopPropagation();
        }
        L.DomEvent.stopPropagation(e);
        
        // Bounds ҳисоблаш
        const latlngs = markers.map(m => m.getLatLng());
        
        if (latlngs.length === 0) {
            return false;
        }
        
        const bounds = L.latLngBounds(latlngs);
        
        // Zoom уровнини аниқлаш (кластер типига қараб)
        let targetZoom = 10;
        if (type === 'branch') {
            targetZoom = 9;  // Филиалдан вилоятга
        } else if (type === 'region') {
            targetZoom = 11; // Вилоятдан туманга
        } else if (type === 'area') {
            targetZoom = 13; // Тумандан маркерларга
        }
        
        // Zoom қилиш - setView билан (аниқ zoom учун)
        setTimeout(() => {
            try {
                window.mymap.setView([centerLat, centerLng], targetZoom);
            } catch (error) {
                // Fallback - fitBounds
                try {
                    window.mymap.fitBounds(bounds, { 
                        padding: [20, 20],
                        maxZoom: targetZoom + 1  // Бир даража юқори
                    });
                } catch (error2) {
                    // Silent fail
                }
            }
        }, 100); // 100ms задержка
        return false; // Event ни тўлиқ тўхтатиш
    }
    
    // Event listener'ларни қўшиш - икки усул
    clusterMarker.on('click', function(e) {
        return handleClusterClick(e, 'click');
    });
    
    clusterMarker.on('mousedown', function(e) {
        // Фақат чап тугма бўлганда
        if (e.originalEvent && e.originalEvent.button === 0) {
            return handleClusterClick(e, 'mousedown');
        }
    });
}

// Кластер hover эффекти қўшиш
window.addClusterHoverEffect = function(clusterMarker) {
    clusterMarker.on('mouseover', function(e) {
        const iconElement = e.target.getElement();
        if (iconElement) {
            iconElement.style.transform = 'scale(1.1)';
            iconElement.style.zIndex = '1000';
        }
    });
    
    clusterMarker.on('mouseout', function(e) {
        const iconElement = e.target.getElement();
        if (iconElement) {
            iconElement.style.transform = 'scale(1)';
            iconElement.style.zIndex = '';
        }
    });
}

// Кластер маркери бўйича статистика кўрсатиш
window.showClusterStatistics = function(clusterMarker, markers, clusterName) {
    const total = markers.length;
    const offline = markers.filter(marker => {
        const pointData = marker.options.pointData;
        return pointData.status === true || pointData.status === 'true' || 
               pointData.status === 1 || pointData.status === '1';
    }).length;
    const online = total - offline;
    
    const statsText = `
        <div class="cluster-stats">
            <h4>${clusterName}</h4>
            <p>Жами БС: ${total}</p>
            <p>Фаол: ${online}</p>
            <p>Ноактив: ${offline}</p>
        </div>
    `;
    
    return statsText;
} 