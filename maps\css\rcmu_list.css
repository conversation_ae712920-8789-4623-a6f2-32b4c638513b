/* РЦМУ List модули CSS стиллари */

.rcmu-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2.5rem 0; /* padding'ни оширамиз */
    margin-bottom: 2.5rem; /* пастки чегарани оширамиз */
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.rcmu-header h1,
.rcmu-header p {
    text-align: left; /* Матнни чапга текислаймиз */
}

.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 2.5rem; /* padding'ни оширамиз */
    text-align: center;
    background-color: #f8f9fa;
    margin-bottom: 3rem; /* жадвалдан узоқлаштирамиз */
    transition: all 0.3s ease;
}

.upload-area h4 {
    font-size: 1.5rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.upload-area p {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

.upload-area:hover {
    border-color: #667eea;
    background-color: #e3f2fd;
}

.upload-area.dragover {
    border-color: #667eea;
    background-color: #e3f2fd;
    transform: scale(1.02);
}

.table-responsive {
    margin-top: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-action {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    margin: 0 0.125rem;
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.filter-section {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1rem;
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
} 