// Кластер қуриш модули - Cluster builders

// Филиаллар асосида кластер яратиш
window.buildBranchClusters = function(markers) {
    // Барча region ID ларни санаш
    const regionIdCounts = new Map();
    
    const branchGroups = new Map();
    
    // Аввал барча region ID ларни ҳисоблаш
    markers.forEach((marker) => {
        const pointData = marker.options.pointData;
        const regionId = pointData.region_id || pointData.regionId || 
                        (pointData.region && typeof pointData.region === 'object' ? pointData.region.id : null);
        
        if (regionId) {
            const count = regionIdCounts.get(regionId) || 0;
            regionIdCounts.set(regionId, count + 1);
        }
    });
    
    // Маркерларни филиаллар бўйича группалаш
    markers.forEach((marker, index) => {
        const pointData = marker.options.pointData;
        
        const regionId = pointData.region_id || pointData.regionId || 
                        (pointData.region && typeof pointData.region === 'object' ? pointData.region.id : null);
        
        const branchKey = window.regionToBranch[regionId];
        
        if (branchKey) {
            if (!branchGroups.has(branchKey)) {
                branchGroups.set(branchKey, []);
            }
            branchGroups.get(branchKey).push(marker);
        }
    });
    
    // Ҳар бир филиал учун кластер яратиш
    branchGroups.forEach((groupMarkers, branchKey) => {
        if (groupMarkers.length > 0) {
            const branchData = window.branches[branchKey];
            if (!branchData) {
                return;
            }
            
            const clusterMarker = window.createLogicalClusterMarker(groupMarkers, branchData, 'branch');
            
            if (clusterMarker) {
                window.branchClusters.set(branchKey, clusterMarker);
                window.mymap.addLayer(clusterMarker);
            }
        }
    });
}

// Вилоятлар асосида кластер яратиш
window.buildRegionClusters = function(markers) {
    const regionGroups = new Map();
    
    // Маркерларни вилоятлар бўйича группалаш
    markers.forEach(marker => {
        const pointData = marker.options.pointData;
        const regionId = pointData.region_id || pointData.regionId || 
                        (pointData.region && typeof pointData.region === 'object' ? pointData.region.id : null);
        
        if (regionId) {
            if (!regionGroups.has(regionId)) {
                regionGroups.set(regionId, []);
            }
            regionGroups.get(regionId).push(marker);
        }
    });
    
    // Ҳар бир вилоят учун кластер яратиш
    regionGroups.forEach((groupMarkers, regionId) => {
        if (groupMarkers.length > 0) {
            const regionName = groupMarkers[0].options.pointData.region_name || 
                              groupMarkers[0].options.pointData.region || 
                              `Region ${regionId}`;
            
            // Тил асосида таржима
            let displayName = regionName;
            if (window.currentLanguage === 'en' && window.translateRegionName) {
                displayName = window.translateRegionName(regionName);
            }
            
            const regionData = {
                name: regionName,  // Аслида рус номи
                displayName: displayName,  // Кўрсатиш учун таржима
                shortName: window.getShortRegionName(displayName)
            };
            
            const clusterMarker = window.createLogicalClusterMarker(groupMarkers, regionData, 'region');
            
            if (clusterMarker) {
                window.regionClusters.set(regionId, clusterMarker);
                window.mymap.addLayer(clusterMarker);
            }
        }
    });
}

// Туманлар асосида кластер яратиш
window.buildAreaClusters = function(markers) {
    const areaGroups = new Map();
    
    // Маркерларни туманлар бўйича группалаш
    markers.forEach(marker => {
        const pointData = marker.options.pointData;
        const areaId = pointData.area_id || pointData.areaId || 
                      (pointData.area && typeof pointData.area === 'object' ? pointData.area.id : null);
        
        if (areaId) {
            if (!areaGroups.has(areaId)) {
                areaGroups.set(areaId, []);
            }
            areaGroups.get(areaId).push(marker);
        }
    });
    
    // Ҳар бир туман учун кластер яратиш
    areaGroups.forEach((groupMarkers, areaId) => {
        if (groupMarkers.length > 0) {
            const areaName = groupMarkers[0].options.pointData.area_name || 
                            groupMarkers[0].options.pointData.area || 
                            `Area ${areaId}`;
            
            // Тил асосида таржима
            let displayName = areaName;
            if (window.currentLanguage === 'en' && window.translateAreaName) {
                displayName = window.translateAreaName(areaName);
            }
            
            const areaData = {
                name: areaName,  // Аслида рус номи
                displayName: displayName,  // Кўрсатиш учун таржима
                shortName: window.getShortAreaName(displayName)
            };
            
            const clusterMarker = window.createLogicalClusterMarker(groupMarkers, areaData, 'area');
            
            if (clusterMarker) {
                window.areaClusters.set(areaId, clusterMarker);
                window.mymap.addLayer(clusterMarker);
            }
        }
    });
}

// Маркерлар учун филиални аниқлаш
window.getBranchForCluster = function(markers) {
    if (!markers || markers.length === 0) return null;
    
    // Биринчи маркернинг вилоят ID сини олиш
    const firstMarker = markers[0];
    const pointData = firstMarker.options.pointData;
    
    // region_id ни турли майдонлардан олишга ҳаракат қилиш
    const regionId = pointData.region_id || pointData.regionId || 
                     (pointData.region && typeof pointData.region === 'object' ? pointData.region.id : null);
    
    if (!regionId) {
        return null;
    }
    
    // Вилоят ID бўйича филиални топиш
    const branchKey = window.regionToBranch[regionId];
    
    return branchKey;
}

// Маркерлар учун вилоятни аниқлаш
window.getRegionForCluster = function(markers) {
    if (!markers || markers.length === 0) return null;
    
    const firstMarker = markers[0];
    const pointData = firstMarker.options.pointData;
    return pointData.region || pointData.region_name || pointData.regionName || null;
}

// Маркерлар учун туманни аниқлаш
window.getAreaForCluster = function(markers) {
    if (!markers || markers.length === 0) return null;
    
    const firstMarker = markers[0];
    const pointData = firstMarker.options.pointData;
    return pointData.area || pointData.area_name || pointData.areaName || null;
} 