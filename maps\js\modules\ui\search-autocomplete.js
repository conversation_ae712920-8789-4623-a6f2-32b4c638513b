// Модуль автокомплита для поиска

// Таймер для автокомплита
let searchTimeout = null;

// Функция автокомплита
async function handleSearchInput(value) {
    if (value.length < 2) {
        window.SearchUI.hideSearchResults();
        return;
    }

    try {
        const currentLang = window.currentLanguage || 'ru';
        const response = await fetch(`/map/api/search-suggestions/?q=${encodeURIComponent(value)}&lang=${currentLang}`);
        const data = await response.json();
        
        console.log('Autocomplete response:', data); // Debug лог
        
        if (data.suggestions && data.suggestions.length > 0) {
            // Маълумотларни тўғри форматда узатиш
            const formattedSuggestions = formatSuggestions(data.suggestions);
            
            console.log('Formatted suggestions:', formattedSuggestions); // Debug лог
            
            window.SearchUI.showSearchResults(formattedSuggestions);
        } else {
            window.SearchUI.hideSearchResults();
        }
    } catch (error) {
        console.error('Autocomplete error:', error);
        window.SearchUI.hideSearchResults();
    }
}

// Тавсияларни форматлаш
function formatSuggestions(suggestions) {
    return suggestions.map(s => ({
        lat: s.lat,
        lon: s.lon,
        display_name: s.text,
        text: s.text, // text ҳам керак
        type: s.type || 'location', // type ни сақлаш
        region_id: s.region_id, // агар мавжуд бўлса
        area_id: s.area_id, // агар мавжуд бўлса
        source: 'suggestion'
    }));
}

// Қидирув input учун event handler-ларни созлаш
function setupSearchInputHandlers() {
    const searchInput = document.getElementById('location-search');
    
    if (!searchInput) {
        console.warn('Search input element not found');
        return;
    }

    // Обработчик для автокомплита
    searchInput.addEventListener('input', function (e) {
        const value = e.target.value.trim();
        
        // Очищаем предыдущий таймер
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // Устанавливаем новый таймер
        searchTimeout = setTimeout(() => {
            handleSearchInput(value);
        }, 200); // Задержка 200мс для быстрого отклика
    });

    // Очистка при потере фокуса
    searchInput.addEventListener('blur', function (e) {
        // Агар натижаларга босилган бўлса, яширмаймиз
        setTimeout(() => {
            const resultsElement = document.querySelector('.search-results');
            const clickedElement = e.relatedTarget;
            
            // Агар клик натижалар ичида бўлмаса
            if (!resultsElement || (!resultsElement.contains(clickedElement) && !document.activeElement.closest('.search-container'))) {
                window.SearchUI.hideSearchResults();
            }
        }, 300); // 300ms кутамиз
    });

    // Обработчик для Enter в поле поиска
    searchInput.addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            const query = searchInput.value.trim();
            if (query) {
                window.SearchAPI.searchLocation(query);
                window.SearchUI.hideSearchResults();
            }
        }
    });
}

// Таймерни тозалаш
function clearSearchTimeout() {
    if (searchTimeout) {
        clearTimeout(searchTimeout);
        searchTimeout = null;
    }
}

// Экспорт функций
window.SearchAutocomplete = {
    handleSearchInput,
    setupSearchInputHandlers,
    clearSearchTimeout,
    formatSuggestions
}; 