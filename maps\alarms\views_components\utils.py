def format_duration(hours, language='ru'):
    """Соатларни инсон ўқий оладиган форматга ўгириш"""
    if hours < 1:
        minutes = int(hours * 60)
        if language == 'en':
            return f"{minutes} min" if minutes > 0 else "< 1 min"
        else:
            return f"{minutes} мин" if minutes > 0 else "< 1 мин"
    elif hours < 24:
        hours_int = int(hours)
        minutes_int = int((hours % 1) * 60)
        if language == 'en':
            return f"{hours_int} h {minutes_int} min"
        else:
            return f"{hours_int} ч {minutes_int} мин"
    else:
        days = int(hours / 24)
        remaining_hours = int(hours % 24)
        if language == 'en':
            return f"{days} d {remaining_hours} h"
        else:
            return f"{days} д {remaining_hours} ч" 