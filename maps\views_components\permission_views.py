"""
Рухсатларни текшириш модули
"""
from django.http import JsonResponse


def check_rcmu_permission(user):
    """
    Проверка прав доступа к РЦМУ функциям
    Доступ имеют: РЦМУ (id 10), Администратор (id 1), О<PERSON>ератор (id 2)
    """
    if not hasattr(user, 'privilege_id') or not user.privilege_id:
        return False
    
    # Получаем название привилегии
    privilege_name = getattr(user.privilege, 'privilege', '') if user.privilege else ''
    
    # Разрешенные роли по ID
    allowed_privilege_ids = [1, 2, 10]  # Администратор, Оператор, РЦМУ
    
    # Разрешенные роли по названию (для совместимости)
    allowed_privilege_names = ['РЦМУ', 'RCMU', 'rcmu', 'Администратор', 'Оператор']
    
    return (user.privilege_id in allowed_privilege_ids or 
            privilege_name in allowed_privilege_names)


def check_rcmu_permission_view(request):
    """
    API для проверки прав доступа к РЦМУ функциям
    """
    try:
        # Проверка авторизации
        if not request.user.is_authenticated:
            return JsonResponse({
                'hasPermission': False,
                'reason': 'Пользователь не авторизован'
            })

        # Проверка прав доступа
        has_permission = check_rcmu_permission(request.user)

        return JsonResponse({
            'hasPermission': has_permission,
            'user': {
                'username': request.user.username,
                'privilege_id': getattr(request.user, 'privilege_id', None),
                'privilege_name': getattr(request.user.privilege, 'privilege', '') if hasattr(request.user, 'privilege') and request.user.privilege else ''
            }
        })

    except Exception as e:
        return JsonResponse({
            'hasPermission': False,
            'error': f'Ошибка при проверке прав: {str(e)}'
        }, status=500) 