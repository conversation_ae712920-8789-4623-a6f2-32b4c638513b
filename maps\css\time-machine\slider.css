/* Вақт машинаси слайдер элементлари */

/* Слайдер времени */
.time-slider-container {
    margin: 8px 0;
    padding: 6px 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.time-slider-wrapper {
    display: flex;
    align-items: center;
    gap: 6px;
}

.time-slider-arrow {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 50%;
    background: #007bff;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.time-slider-arrow:hover {
    background: #0056b3;
    transform: scale(1.1);
}

.time-slider-arrow:active {
    transform: scale(0.95);
}

.time-slider-track {
    flex: 1;
    position: relative;
    height: 6px;
}

#time-slider {
    width: 100%;
    height: 4px;
    border-radius: 2px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
    cursor: pointer;
}

#time-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    transition: all 0.2s ease;
}

#time-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 0 6px rgba(0, 123, 255, 0.5);
}

#time-slider::-moz-range-thumb {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
}

#time-slider::-moz-range-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 0 6px rgba(0, 123, 255, 0.5);
}

/* Слайдер ва вақт танлаш бир қаторда */
.time-slider-with-inputs {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 6px;
    padding: 8px;
    background: linear-gradient(to right, #f0f5ff, #f9f9f9);
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

/* Вақт киритиш элементлари inline */
.time-inputs-inline {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-left: 10px;
    flex-shrink: 0;
}

.time-inputs-inline input[type="date"],
.time-inputs-inline input[type="text"] {
    width: 95px;
    padding: 3px 5px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 10px;
    height: 22px;
    background-color: rgba(255, 255, 255, 0.8);
}

.time-inputs-inline select {
    width: 40px;
    padding: 3px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 10px;
    height: 22px;
    background-color: rgba(255, 255, 255, 0.8);
}

.time-separator {
    font-weight: bold;
    color: #666;
    font-size: 14px;
}

.time-slider-track-horizontal {
    flex: 1;
    position: relative;
    margin-right: 10px;
}

.time-slider-track-horizontal input[type="range"] {
    width: 100%;
    height: 5px;
    border-radius: 3px;
    background: #e0e0e0;
    outline: none;
    -webkit-appearance: none;
    cursor: pointer;
}

.time-slider-track-horizontal input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #ff9800;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.time-slider-track-horizontal input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 2px 6px rgba(255, 152, 0, 0.4);
}

.time-slider-track-horizontal input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #ff9800;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.time-slider-track-horizontal input[type="range"]::-moz-range-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 2px 6px rgba(255, 152, 0, 0.4);
}

.time-slider-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 2px;
    font-size: 8px;
    color: #999;
    opacity: 0.8;
}

.time-slider-arrow {
    width: 22px;
    height: 22px;
    border: none;
    border-radius: 50%;
    background: #007bff;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.time-slider-arrow:hover {
    background: #0056b3;
    transform: scale(1.1);
}

.time-inputs-inline input:focus,
.time-inputs-inline select:focus {
    outline: none;
    border-color: #ff9800;
    box-shadow: 0 0 0 2px rgba(255, 152, 0, 0.25);
} 