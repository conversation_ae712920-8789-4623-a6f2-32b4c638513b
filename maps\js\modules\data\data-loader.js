// Модуль загрузки данных

// Глобальные переменные для данных
window.globalStations = window.globalStations || [];
window.globalRegions = window.globalRegions || [];

// Загрузка данных карты
window.loadMapData = function () {
    // Показываем индикатор загрузки
    window.showLoader();

    fetch('/map/api/map-data/')
        .then(response => response.json())
        .then(data => {
            // Сохраняем данные
            window.globalStations = data.points || [];
            window.globalRegions = data.regions || [];

            // Подготавливаем таблицу регионов (создаем строки)
            window.prepareRegionsStats();

            // Очищаем сохраненное состояние фильтров
            localStorage.removeItem('filterState');

            // Сбрасываем все фильтры при загрузке
            window.resetAllFilters();

            // Применяем фильтры (будут применены фильтры по умолчанию после сброса)
            window.applyCurrentFilters();

            // Обновляем статистику регионов
            window.updateRegionsStats();

            // Рассчитываем статистику продолжительности для всех БС
            if (window.calculateDurationStats) {
                window.calculateDurationStats(window.globalStations);
            }
            
            // Off Stations Panel ни янгилаш
            if (window.updateOffStations) {
                window.updateOffStations();
            }

            // Скрываем индикатор загрузки
            window.hideLoader();

            // Запускаем автообновление
            window.startAutoRefresh();
        })
        .catch(error => {
            console.error('Error loading map data:', error);
            window.hideLoader();
        });
}

// Автообновление данных
window.startAutoRefresh = function startAutoRefresh() {
    // Останавливаем предыдущий интервал если есть
    if (window.refreshInterval) {
        clearInterval(window.refreshInterval);
    }

    // Запускаем новый интервал обновления каждые 10 секунд
    window.refreshInterval = setInterval(() => {
        if (!window.isTimeMachineEnabled) {
            // Other alarms режимида алоҳида refresh логика
            if (window.AlarmManager && typeof window.AlarmManager.isEnabled === 'function' && window.AlarmManager.isEnabled()) {
                window.AlarmManager.refresh();
            } else {
                refreshMapData();
            }
        }
    }, 10000);
}

// Auto-refresh ни тўхтатиш
window.stopAutoRefresh = function() {
    if (window.refreshInterval) {
        clearInterval(window.refreshInterval);
        window.refreshInterval = null;
    }
}

// Обновление данных
function refreshMapData() {
    // Машина времени режимида янгиланишни тўхтатиш
    if (window.isTimeMachineEnabled) {
        return;
    }

    fetch('/map/api/map-data/')
        .then(response => response.json())
        .then(data => {
            const serverPoints = data.points || [];
            
            // Сохраняем старые данные для сравнения и сохранения calcTime
            const oldStations = [...window.globalStations];
            const oldStationsMap = {};
            oldStations.forEach(station => {
                const stationName = station.bsName || station.name;
                oldStationsMap[stationName] = station;
            });

            // ВАЖНО: Серверда келган маълумотларни тўғридан-тўғри ишлатамиз
            // Фақат calcTime ни сақлаймиз, статусни ўзгартирмаймиз
            window.globalStations = serverPoints.map(newStation => {
                const stationName = newStation.bsName || newStation.name;
                const oldStation = oldStationsMap[stationName];

                // Если точка была аварийной и имеет calcTime, сохраняем старый calcTime
                if (oldStation && oldStation.status === true && oldStation.calcTime && newStation.status === true) {
                    // Проверяем, получаем ли мы новое значение calcTime с сервера
                    if (!newStation.calcTime) {
                        // Если новое значение не пришло, используем старое
                        return { ...newStation, calcTime: oldStation.calcTime };
                    }
                }

                // Серверда келган маълумотни ўзгартирмасдан қайтариш
                return newStation;
            });
            
            window.globalRegions = data.regions || [];

            // Сохраняем текущие фильтры
            const currentRegionId = document.getElementById('region').value;
            const currentAreaId = document.getElementById('area').value;
            const currentBsSearch = document.getElementById('bs-search').value;
            const currentStatusFilter = document.getElementById('status').value;
            const currentDurationFilter = document.getElementById('duration-filter').value;

            // Применяем фильтры
            let filteredPoints = [...window.globalStations];

            // Фильтр по региону
            if (currentRegionId) {
                filteredPoints = filteredPoints.filter(point => point.region_id == currentRegionId);
            }

            // Фильтр по району
            if (currentAreaId) {
                filteredPoints = filteredPoints.filter(point => point.area_id == currentAreaId);
            }

            // Фильтр по имени БС
            if (currentBsSearch) {
                filteredPoints = window.filterByBsName(filteredPoints, currentBsSearch);
            }

            // Применяем фильтр статуса с обновлением маркеров
            window.applyStatusFilter(filteredPoints, currentStatusFilter, false);

            // Обновляем статистику регионов (один раз!)
            window.updateRegionsStats();

            // Обновляем статистику продолжительности с учетом текущих фильтров
            if (window.calculateDurationStats) {
                let pointsForDurationStats = [...window.globalStations];

                // Фильтруем по региону если выбран
                if (currentRegionId) {
                    pointsForDurationStats = pointsForDurationStats.filter(point => point.region_id == currentRegionId);
                }

                // Фильтруем по району если выбран
                if (currentAreaId) {
                    pointsForDurationStats = pointsForDurationStats.filter(point => point.area_id == currentAreaId);
                }

                window.calculateDurationStats(pointsForDurationStats);
            }

            // Обновляем открытые попапы (реал вақт режимида downtime ни янгилаш учун)
            if (window.updateOpenPopups) {
                window.updateOpenPopups();
            }
            
            // Off Stations Panel ни янгилаш
            if (window.updateOffStations) {
                window.updateOffStations();
            }

        })
        .catch(error => {
            console.error('❌ Error refreshing map data:', error);
        });
}

// Загрузка районов для региона
window.loadAreasForRegion = function (regionId) {
            return fetch(`/map/api/areas/${regionId}/`)
        .then(response => response.json())
        .catch(error => {
            console.error('Error loading areas:', error);
            return [];
        });
}

// Показать индикатор загрузки
window.showLoader = function showLoader() {
    const loader = document.getElementById('loader');
    if (loader) {
        loader.style.display = 'flex';
        loader.style.opacity = '1';
        loader.style.visibility = 'visible';
    } else {
        console.error('❌ Loader element not found!');
    }
}

// Скрыть индикатор загрузки
window.hideLoader = function hideLoader() {
    const loader = document.getElementById('loader');
    if (loader) {
        loader.style.opacity = '0';
        setTimeout(() => {
            loader.style.visibility = 'hidden';
            loader.style.display = 'none';
        }, 300);
    } else {
        console.error('❌ Loader element not found!');
    }
}

// Синхронизация станций с глобальным статусом
function syncStationsWithGlobalStatus(stations, points) {
    const stationStatusMap = {};
    points.forEach(point => {
        stationStatusMap[point.bsName || point.name] = point.status;
    });

    return stations.map(station => {
        const stationName = station.bsName || station.name;
        const statusFromGlobal = stationStatusMap[stationName];
        return {
            ...station,
            status: statusFromGlobal !== undefined ? statusFromGlobal : (station.status === 'offline')
        };
    });
}

// Расчет центра по станциям
window.calculateCenter = function (stations) {
    if (!stations || stations.length === 0) return null;

    let totalLat = 0;
    let totalLon = 0;
    let validStations = 0;

    stations.forEach(station => {
        if (station.lat && station.lon && !isNaN(parseFloat(station.lat)) && !isNaN(parseFloat(station.lon))) {
            totalLat += parseFloat(station.lat);
            totalLon += parseFloat(station.lon);
            validStations++;
        }
    });

    if (validStations === 0) return null;

    return {
        lat: totalLat / validStations,
        lon: totalLon / validStations
    };
} 