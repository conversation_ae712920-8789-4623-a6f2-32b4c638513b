"""
RCMU рухсатларни текшириш функциялари
"""


def check_rcmu_permission(user):
    """
    Проверка прав доступа к РЦМУ функциям
    Доступ имеют: РЦ<PERSON>У (id 10), Ад<PERSON><PERSON><PERSON>истратор (id 1), О<PERSON><PERSON><PERSON><PERSON>тор (id 2)
    """
    if not hasattr(user, 'privilege_id') or not user.privilege_id:
        return False
    
    # Получаем название привилегии
    privilege_name = getattr(user.privilege, 'privilege', '') if user.privilege else ''
    
    # Разрешенные роли по ID
    allowed_privilege_ids = [1, 2, 10]  # Администратор, Оператор, РЦМУ
    
    # Разрешенные роли по названию (для совместимости)
    allowed_privilege_names = ['РЦМУ', 'RCMU', 'rcmu', 'Администратор', 'Оператор']
    
    return (user.privilege_id in allowed_privilege_ids or 
            privilege_name in allowed_privilege_names) 