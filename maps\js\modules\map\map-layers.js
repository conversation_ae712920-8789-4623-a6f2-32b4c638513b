// Модуль управления слоями карты

// Глобальные переменные для слоев карты
window.currentMapLayer = null;
window.currentSatelliteLayer = null;
window.isMapMode = true; // true = карта, false = спутник
window.isYandexMode = false;

// Яндекс карта
window.yandexMap = null;
window.yandexMapContainer = null;
window.isYandexAPILoaded = false;
window.yandexMapLayer = null;
window.yandexSatelliteLayer = null;

// OSM слои
window.osmMapLayer = null;
window.osmSatelliteLayer = null;

// Google слои
window.googleMapLayer = null;
window.googleSatelliteLayer = null;

// Инициализация слоев карты
window.initializeMapLayers = function(map) {
    // OSM слои
    window.osmMapLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors',
        maxZoom: 19
    });

    window.osmSatelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
        attribution: '© Esri, Maxar, GeoEye, Earthstar Geographics, CNES/Airbus DS, USDA, USGS, AeroGRID, IGN, and the GIS User Community',
        maxZoom: 19
    });

    // Google слои - тўғридан-тўғри тайллар орқали
    window.googleMapLayer = L.tileLayer('https://mt{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}', {
        subdomains: ['0', '1', '2', '3'],
        attribution: '© Google Maps',
        maxZoom: 20
    });

    window.googleSatelliteLayer = L.tileLayer('https://mt{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}', {
        subdomains: ['0', '1', '2', '3'],
        attribution: '© Google Maps Satellite',
        maxZoom: 20
    });

    // Установка начальных слоев (OSM по умолчанию)
    window.currentMapLayer = window.osmMapLayer;
    window.currentSatelliteLayer = window.osmSatelliteLayer;

    // Добавление начального слоя на карту
    window.currentMapLayer.addTo(map);

    // Активация кнопки OSM по умолчанию
    const osmButton = document.getElementById('osm-source-button');
    if (osmButton) {
        osmButton.classList.add('active');
    }

    // Активация кнопки Map по умолчанию
    const mapButton = document.getElementById('map-button');
    if (mapButton) {
        mapButton.classList.add('active');
    }
};

// Переключение на OSM слои
window.switchToOSM = function() {
    window.isYandexMode = false;
    
    // Скрыть Яндекс контейнер если есть
    if (window.yandexMapContainer) {
        window.yandexMapContainer.style.display = 'none';
    }

    // Удалить все слои
    removeAllLayers();

    // Установить OSM слои
    window.currentMapLayer = window.osmMapLayer;
    window.currentSatelliteLayer = window.osmSatelliteLayer;

    // Добавить нужный слой
    if (window.isMapMode) {
        window.currentMapLayer.addTo(window.mymap);
    } else {
        window.currentSatelliteLayer.addTo(window.mymap);
    }

    // Перерисовать маркеры
    if (window.applyCurrentFilters) {
        window.applyCurrentFilters();
    }
};

// Переключение на Яндекс слои
window.switchToYandex = function() {
    window.isYandexMode = true;

    // Удалить все слои
    removeAllLayers();

    // Проверка готовности Яндекс API
    if (!window.isYandexAPILoaded || !window.yandexMap) {
        console.warn('Yandex Maps API not ready');
        return;
    }

    // Установить тип Яндекс карты
    if (window.isMapMode) {
        window.yandexMap.setType('yandex#map');
    } else {
        window.yandexMap.setType('yandex#satellite');
    }

    // Показать Яндекс контейнер
    if (window.yandexMapContainer) {
        window.yandexMapContainer.style.display = 'block';
        window.syncYandexMapWithLeaflet();
    }

    // Перерисовать маркеры
    if (window.applyCurrentFilters) {
        window.applyCurrentFilters();
    }
};

// Переключение на Google слои
window.switchToGoogle = function() {
    window.isYandexMode = false;

    // Скрыть Яндекс контейнер если есть
    if (window.yandexMapContainer) {
        window.yandexMapContainer.style.display = 'none';
    }

    // Удалить все слои
    removeAllLayers();

    // Установить Google слои
    window.currentMapLayer = window.googleMapLayer;
    window.currentSatelliteLayer = window.googleSatelliteLayer;

    // Добавить нужный слой
    if (window.isMapMode) {
        window.currentMapLayer.addTo(window.mymap);
    } else {
        window.currentSatelliteLayer.addTo(window.mymap);
    }

    // Перерисовать маркеры
    if (window.applyCurrentFilters) {
        window.applyCurrentFilters();
    }
};

// Переключение между картой и спутником
window.switchMapMode = function(isMap) {
    window.isMapMode = isMap;

    if (window.isYandexMode) {
        // Для Яндекс карты
        if (window.yandexMap) {
            if (isMap) {
                window.yandexMap.setType('yandex#map');
            } else {
                window.yandexMap.setType('yandex#satellite');
            }
        }
    } else {
        // Для OSM/Google
        // Удалить текущий слой
        if (window.mymap.hasLayer(window.currentMapLayer)) {
            window.mymap.removeLayer(window.currentMapLayer);
        }
        if (window.mymap.hasLayer(window.currentSatelliteLayer)) {
            window.mymap.removeLayer(window.currentSatelliteLayer);
        }

        // Добавить нужный слой
        if (isMap) {
            window.currentMapLayer.addTo(window.mymap);
        } else {
            window.currentSatelliteLayer.addTo(window.mymap);
        }
    }
};

// Удаление всех слоев
function removeAllLayers() {
    if (window.mymap.hasLayer(window.osmMapLayer)) {
        window.mymap.removeLayer(window.osmMapLayer);
    }
    if (window.mymap.hasLayer(window.osmSatelliteLayer)) {
        window.mymap.removeLayer(window.osmSatelliteLayer);
    }
    if (window.mymap.hasLayer(window.googleMapLayer)) {
        window.mymap.removeLayer(window.googleMapLayer);
    }
    if (window.mymap.hasLayer(window.googleSatelliteLayer)) {
        window.mymap.removeLayer(window.googleSatelliteLayer);
    }
}

// Синхронизация Яндекс карты с Leaflet (если не определена в main.js)
window.syncYandexMapWithLeaflet = function() {
    if (!window.yandexMap || !window.mymap) return;

    const center = window.mymap.getCenter();
    const zoom = window.mymap.getZoom();

    window.yandexMap.setCenter([center.lat, center.lng], zoom, {
        duration: 0
    });
}; 