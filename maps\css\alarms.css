/* Alarm Mode Styles */

/* Alarm toggle button - zoom tugmalari tagida */
.alarm-mode-toggle {
    position: absolute;
    top: 175px; /* Auto-center tugmasi tagida */
    left: 311px;
    z-index: 1000;
}

.alarm-toggle-btn {
    width: 31px;
    height: 31px;
    border-radius: 4px;
    background: white;
    border: 2px solid rgba(0, 0, 0, 0.2);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: #666;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
}

.alarm-toggle-btn:hover {
    background: #fff5e6;
    border-color: #ff9800;
    color: #ff9800;
}

.alarm-toggle-btn.active {
    background: #ff9800;
    border-color: #ff9800;
    color: white;
}

.alarm-toggle-btn svg,
.alarm-toggle-btn img {
    width: 18px;
    height: 18px;
}

/* Мобил учун адаптация */
@media (max-width: 768px) {
    .alarm-mode-toggle {
        top: 150px;
        left: 10px;
    }
    
    .alarm-toggle-btn {
        width: 30px;
        height: 30px;
    }
}

/* Alarm selector panel - yanada ixchamroq dizayn */
.alarm-selector-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 6px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.12);
    width: 85%;
    max-width: 350px;
    max-height: 60vh;
    z-index: 10000;
    display: flex;
    flex-direction: column;
}

.alarm-selector-header {
    padding: 12px 15px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.alarm-selector-header h3 {
    margin: 0;
    font-size: 15px;
    color: #333;
    font-weight: 500;
}

.close-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #999;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s;
}

.close-btn:hover {
    background: #f5f5f5;
    color: #333;
}

.alarm-selector-body {
    flex: 1;
    overflow-y: auto;
    padding: 10px 15px;
}

.alarm-types-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.alarm-types-loading .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #ff9800;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.alarm-select-all {
    padding: 6px 0;
    border-bottom: 2px solid #e0e0e0;
    margin-bottom: 6px;
}

.alarm-select-all label {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 13px;
    margin: 0;
    cursor: pointer;
}

.alarm-select-all input[type="checkbox"] {
    margin-right: 6px;
    width: 14px;
    height: 14px;
    cursor: pointer;
}

.alarm-types-list {
    max-height: 300px;
    overflow-y: auto;
}

.alarm-type-item {
    padding: 4px 0;
    border-bottom: 1px solid #f5f5f5;
}

.alarm-type-item:last-child {
    border-bottom: none;
}

.alarm-type-item label {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 3px 0;
}

.alarm-type-item input[type="checkbox"] {
    margin-right: 6px;
    width: 14px;
    height: 14px;
    cursor: pointer;
}

.alarm-type-item span {
    color: #333;
    font-size: 12px;
    line-height: 1.2;
}

.alarm-selector-footer {
    padding: 12px 15px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
    gap: 6px;
}

.btn {
    padding: 5px 14px;
    border-radius: 4px;
    border: none;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary {
    background: #ff9800;
    color: white;
}

.btn-primary:hover {
    background: #f57c00;
}

.btn-secondary {
    background: #f5f5f5;
    color: #666;
}

.btn-secondary:hover {
    background: #e0e0e0;
}

/* Alarm mode indicator */
.alarm-mode-indicator {
    position: fixed;
    top: 80px;
    right: 20px;
    background: #ff9800;
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 14px;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(255, 152, 0, 0.3);
    display: none;
}

.alarm-mode-indicator.active {
    display: block;
}

/* Responsive */
@media (max-width: 768px) {
    .alarm-selector-panel {
        width: 95%;
        max-height: 90vh;
    }
    
    .alarm-selector-body {
        padding: 15px;
    }
    
    .alarm-types-list {
        max-height: 300px;
    }
}

/* Блокланган тугма стили */
.alarm-toggle-btn:disabled {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
    pointer-events: none;
}

.alarm-toggle-btn:disabled:hover {
    transform: none !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
}

/* Time machine checkbox блокланганда */
#time-machine-enabled:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#time-machine-enabled:disabled + label {
    opacity: 0.5;
    cursor: not-allowed;
} 