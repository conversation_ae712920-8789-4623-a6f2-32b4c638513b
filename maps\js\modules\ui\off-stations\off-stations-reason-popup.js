// Off Stations Reason Popup модули - причиналарни чап томонда кўрсатиш
(function() {
    'use strict';
    
    // Глобал ўзгарувчилар
    let reasonPopup = null;
    let currentGroupId = null;
    let hideTimeout = null;

    // Попап ойнасини яратиш
    function createReasonPopup() {
        const popup = document.createElement('div');
        popup.className = 'reason-popup-container';
        popup.id = 'reason-popup-container';
        
        // Ҳақиқий тилни олиш
        const lang = window.currentLanguage || localStorage.getItem('mapLanguage') || 'ru';
        const title = lang === 'en' ? 'Outage Reason' : 'Причина аварии';
        
        popup.innerHTML = `
            <div class="reason-popup">
                <div class="reason-popup-header">
                    <h4><i class="fas fa-info-circle"></i> <span id="reason-popup-title">${title}</span></h4>
                    <button class="reason-popup-close" onclick="window.OffStationsReasonPopup.hideReasonPopup()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="reason-popup-body">
                    <div class="reason-popup-content" id="reason-popup-content">
                        <!-- Причина мазмуни бу ерда кўрсатилади -->
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(popup);
        return popup;
    }

    // Попап ойнасини кўрсатиш
    function showReasonPopup(groupId, reason, element) {
        // Агар попап мавжуд бўлмаса, яратиш
        if (!reasonPopup) {
            reasonPopup = createReasonPopup();
        }

        // Тилни янгилаш (ҳар сафар кўрсатилганда)
        updatePopupLanguage();

        currentGroupId = groupId;
        
        // Попап позициясини ҳисоблаш
        const rect = element.getBoundingClientRect();
        const popup = reasonPopup.querySelector('.reason-popup');
        
        // Попап контентини янгилаш
        updatePopupContent(reason);
        
        // Попапни кўрсатиш
        reasonPopup.style.display = 'block';
        reasonPopup.style.left = `${rect.left - 235}px`; // Чап томонда кўрсатиш
        reasonPopup.style.top = `${rect.top}px`;
        
        // Экран чегараларини текшириш
        const popupRect = popup.getBoundingClientRect();
        if (popupRect.left < 10) {
            reasonPopup.style.left = '10px';
        }
        if (popupRect.top < 10) {
            reasonPopup.style.top = '10px';
        }
        if (popupRect.bottom > window.innerHeight - 10) {
            reasonPopup.style.top = `${window.innerHeight - popupRect.height - 10}px`;
        }
        
        // Автоматик яшириш таймерини тўхтатиш
        if (hideTimeout) {
            clearTimeout(hideTimeout);
            hideTimeout = null;
        }
        
        // Анимация
        setTimeout(() => {
            popup.classList.add('show');
        }, 10);
    }

    // Попап контентини янгилаш
    function updatePopupContent(reason) {
        const content = document.getElementById('reason-popup-content');
        const lang = window.currentLanguage || localStorage.getItem('mapLanguage') || 'ru';
        
        if (reason) {
            content.innerHTML = `
                <div class="reason-text">
                    <p>${reason}</p>
                </div>
            `;
        } else {
            content.innerHTML = `
                <div class="no-reason-text">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>${lang === 'en' ? 'No reason specified' : 'Причина не указана'}</p>
                </div>
            `;
        }
    }

    // Попап ойнасини яшириш
    function hideReasonPopup() {
        if (reasonPopup) {
            const popup = reasonPopup.querySelector('.reason-popup');
            popup.classList.remove('show');
            
            setTimeout(() => {
                reasonPopup.style.display = 'none';
                currentGroupId = null;
            }, 300);
        }
    }

    // Автоматик яшириш (300ms кечикиш билан)
    function scheduleHidePopup() {
        if (hideTimeout) {
            clearTimeout(hideTimeout);
        }
        
        hideTimeout = setTimeout(() => {
            hideReasonPopup();
        }, 300);
    }

    // Попап устида турганда яшириш ни тўхтатиш
    function cancelHidePopup() {
        if (hideTimeout) {
            clearTimeout(hideTimeout);
            hideTimeout = null;
        }
    }

    // Попап event listener лар
    function attachPopupEventListeners() {
        // Попап яратилгандан сўнг event listener қўшиш
        setTimeout(() => {
            if (reasonPopup) {
                reasonPopup.addEventListener('mouseenter', cancelHidePopup);
                reasonPopup.addEventListener('mouseleave', scheduleHidePopup);
            }
        }, 100);
    }

    // Тил ўзгарганда попапни янгилаш
    function updatePopupLanguage() {
        const title = document.getElementById('reason-popup-title');
        const lang = window.currentLanguage || localStorage.getItem('mapLanguage') || 'ru';
        
        if (title) {
            title.textContent = lang === 'en' ? 'Outage Reason' : 'Причина аварии';
        }
        
        // Агар попап очиқ бўлса, контентни ҳам янгилаш
        if (reasonPopup && reasonPopup.style.display === 'block' && currentGroupId) {
            // Хозирги очиқ попап учун причина олиш
            const visibleGroups = document.querySelectorAll('.station-group[data-reason]');
            for (let group of visibleGroups) {
                if (group.matches(':hover')) {
                    const reason = group.getAttribute('data-reason');
                    updatePopupContent(reason);
                    break;
                }
            }
        }
    }

    // Initialization
    function init() {
        // Тил ўзгарганда попапни янгилаш
        document.addEventListener('languageChanged', updatePopupLanguage);
        
        // Дастлабки тил ҳолатини янгилаш (браузер юкланишида)
        setTimeout(() => {
            updatePopupLanguage();
        }, 100);
    }

    // Глобал объектга экспорт қилиш
    window.OffStationsReasonPopup = {
        showReasonPopup: showReasonPopup,
        hideReasonPopup: hideReasonPopup,
        scheduleHidePopup: scheduleHidePopup,
        cancelHidePopup: cancelHidePopup,
        updateLanguage: updatePopupLanguage,
        init: init
    };

    // Инициализация
    init();
})(); 