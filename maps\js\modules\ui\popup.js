// Модуль для управления popup'ами

// Создание содержимого popup'а для БС
window.createPopupContent = function(point) {
    // Хавфсизлик текшируви - currentLanguage ва translations мавжудлигини текшириш
    if (!window.currentLanguage) {
        window.currentLanguage = 'ru'; // Default значение
    }
    if (!window.translations || !window.translations[window.currentLanguage]) {
        console.warn('Translations not loaded, using fallback');
        return createFallbackPopupContent(point);
    }
    
    const pointName = point.bsName || point.name;
    let popupContent = "<div style='min-width: 200px;'>";
    popupContent += "<b>" + pointName + "</b><br>";

    // Информация о регионе и районе
    if (point.region_name) {
        let regionName = window.currentLanguage === 'ru' ?
            point.region_name : window.translateRegionName(point.region_name);
        popupContent += "<b>" + window.translations[window.currentLanguage]['region_label'] + ":</b> " + regionName + "<br>";
    }

    if (point.area_name) {
        // Таржима функциясини доимо ишлатамиз (инглиз тилида таржима, рус тилида оригинал)
        let areaName = window.translateAreaName(point.area_name);
        popupContent += "<b>" + window.translations[window.currentLanguage]['area_label'] + ":</b> " + areaName + "<br>";
    }

    // Other alarms режими учун
    if (point.other_alarms && point.other_alarms.length > 0) {
        popupContent += "<hr style='margin: 5px 0;'>";
        popupContent += "<b>" + window.translations[window.currentLanguage]['other_alarms'] + "</b><br>";
        
        // Ҳар бир авария учун маълумот
        point.other_alarms.forEach(alarm => {
            popupContent += "<div style='margin: 3px 0; padding: 3px; background-color: #f5f5f5; border-radius: 3px;'>";
            popupContent += "<b>" + alarm.alarm_name + "</b><br>";
            
            // Бошланиш вақти
            if (alarm.appear_time) {
                const appearDate = new Date(alarm.appear_time);
                popupContent += "<small>" + window.translations[window.currentLanguage]['alarm_started'] + " " +
                    window.formatDateTime(appearDate) + "</small><br>";
            }
            
            // Давомийлик
            if (alarm.duration_text) {
                popupContent += "<small style='color: red; font-weight: bold;'>" +
                    window.translations[window.currentLanguage]['downtime_duration'] + " " + alarm.duration_text + "</small><br>";
            }
            
            popupContent += "</div>";
        });
    }
    // Информация об авариях по технологиям (обычный режим)
    else if (point.alarms_by_tech && Object.keys(point.alarms_by_tech).length > 0) {
        popupContent += "<hr style='margin: 5px 0;'>";
        popupContent += "<b>" + window.translations[window.currentLanguage]['alarms_by_tech'] + "</b><br>";

        // Показываем информацию по каждой технологии
        Object.keys(point.alarms_by_tech).sort().forEach(tech => {
            const alarmInfo = point.alarms_by_tech[tech];
            popupContent += "<div style='margin: 3px 0; padding: 3px; background-color: #f5f5f5; border-radius: 3px;'>";
            popupContent += "<b>" + tech + ":</b> " + alarmInfo.alarmname + "<br>";

            // Время начала аварии
            let appearDate = null;
            if (alarmInfo.appeartime) {
                appearDate = new Date(alarmInfo.appeartime);
                popupContent += "<small>" + window.translations[window.currentLanguage]['alarm_started'] + " " +
                    window.formatDateTime(appearDate) + "</small><br>";
            }

            // Время окончания аварии (если есть)
            if (alarmInfo.cleartime) {
                const clearDate = new Date(alarmInfo.cleartime);

                // В режиме машины времени
                if (window.isTimeMachineEnabled && window.selectedDateTime) {
                    popupContent += "<small>" + window.translations[window.currentLanguage]['alarm_ended'] + " " +
                        window.formatDateTime(clearDate) + "</small><br>";

                    // Продолжительность простоя
                    const downtimeMinutes = calculateDowntimeMinutes(alarmInfo.appeartime, alarmInfo.cleartime);
                    const downtimeText = formatDowntime(downtimeMinutes);
                    popupContent += "<small style='color: red; font-weight: bold;'>" +
                        window.translations[window.currentLanguage]['downtime_duration'] + " " + downtimeText + "</small><br>";
                } else {
                    // В режиме реального времени
                    popupContent += "<small>" + window.translations[window.currentLanguage]['alarm_ended'] + " " +
                        window.formatDateTime(clearDate) + "</small><br>";
                }
            } else {
                // Продолжающаяся авария
                if (window.isTimeMachineEnabled && window.selectedDateTime) {
                    popupContent += "<small>" + window.translations[window.currentLanguage]['continuing'] + "</small><br>";
                } else {
                    // В режиме реального времени - показываем продолжительность
                    const downtimeMinutes = calculateDowntimeMinutes(alarmInfo.appeartime);
                    const downtimeText = formatDowntime(downtimeMinutes);

                    popupContent += "<small style='color: red; font-weight: bold;'>" +
                        window.translations[window.currentLanguage]['downtime_duration'] + " " + downtimeText + "</small><br>";
                }
            }

            popupContent += "</div>";
        });
    } else {
        // Старый формат отображения (если нет новых данных)
        if (point.typeG) {
            popupContent += "<b>" + window.translations[window.currentLanguage]['technology_label'] + ":</b> " + point.typeG + "<br>";
        }

        // Продолжительность аварии (только в режиме реального времени)
        if (point.calcTime && !point.historical) {
            popupContent += "<b>" + window.translations[window.currentLanguage]['downtime_label'] + ":</b> " + point.calcTime + "<br>";
        }
    }

    popupContent += "</div>";
    return popupContent;
};

// Fallback функция агар translations юкланмаган бўлса
function createFallbackPopupContent(point) {
    const pointName = point.bsName || point.name;
    let popupContent = "<div style='min-width: 200px;'>";
    popupContent += "<b>" + pointName + "</b><br>";

    // Информация о регионе и районе
    if (point.region_name) {
        popupContent += "<b>Регион:</b> " + point.region_name + "<br>";
    }

    if (point.area_name) {
        popupContent += "<b>Район:</b> " + point.area_name + "<br>";
    }

    // Информация об авариях по технологиям
    if (point.alarms_by_tech && Object.keys(point.alarms_by_tech).length > 0) {
        popupContent += "<hr style='margin: 5px 0;'>";
        popupContent += "<b>Авария по технологиям:</b><br>";

        Object.keys(point.alarms_by_tech).sort().forEach(tech => {
            const alarmInfo = point.alarms_by_tech[tech];
            popupContent += "<div style='margin: 3px 0; padding: 3px; background-color: #f5f5f5; border-radius: 3px;'>";
            popupContent += "<b>" + tech + ":</b> " + alarmInfo.alarmname + "<br>";

            if (alarmInfo.appeartime) {
                const appearDate = new Date(alarmInfo.appeartime);
                popupContent += "<small>Начало аварии: " + window.formatDateTime(appearDate) + "</small><br>";
            }

            if (alarmInfo.cleartime) {
                const clearDate = new Date(alarmInfo.cleartime);
                popupContent += "<small>Окончание аварии: " + window.formatDateTime(clearDate) + "</small><br>";
            } else {
                const downtimeMinutes = calculateDowntimeMinutes(alarmInfo.appeartime);
                const downtimeText = formatDowntime(downtimeMinutes);
                popupContent += "<small style='color: red; font-weight: bold;'>Продолжительность: " + downtimeText + "</small><br>";
            }

            popupContent += "</div>";
        });
    } else {
        if (point.typeG) {
            popupContent += "<b>Технология:</b> " + point.typeG + "<br>";
        }

        if (point.calcTime && !point.historical) {
            popupContent += "<b>Продолжительность аварии:</b> " + point.calcTime + "<br>";
        }
    }

    popupContent += "</div>";
    return popupContent;
}

// Функция для обновления открытых popup'ов (для real-time обновления)
window.updateOpenPopups = function() {
    if (window.isTimeMachineEnabled) return; // Машина времени режимида янгиламаймиз

    // Барча маркерларни текшириб, очиқ попапи бор маркерларни топиш
    window.mymap.eachLayer(function(marker) {
        if (marker instanceof L.CircleMarker && marker._hoverPopup && marker._hoverPopup.isOpen()) {
            // Маркерда очиқ попап бор
            const point = marker.options.pointData;
            if (point && point.status === true && !point.historical) {
                // Аварияли БС учун попапни янгилаш
                updateMarkerPopup(marker, point);
            }
        }
    });
};

// Маркер попапини янгилаш функцияси
function updateMarkerPopup(marker, point) {
    if (marker._hoverPopup && marker._hoverPopup.isOpen()) {
        // Янги контентни яратиш
        const popupContent = window.createPopupContent(point);
        
        // Попап контентини янгилаш
        marker._hoverPopup.setContent(popupContent);
    }
}

// Функция форматирования времени простая (если не определена в utils)
function formatDowntime(minutes) {
    const isRussian = window.currentLanguage === 'ru';
    
    if (minutes < 60) {
        return `${minutes} ${isRussian ? 'мин' : 'min'}`;
    } else if (minutes < 1440) { // менее 24 часов
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        if (remainingMinutes === 0) {
            return `${hours} ${isRussian ? 'ч' : 'h'}`;
        } else {
            return `${hours} ${isRussian ? 'ч' : 'h'} ${remainingMinutes} ${isRussian ? 'мин' : 'min'}`;
        }
    } else { // более 24 часов
        const days = Math.floor(minutes / 1440);
        const hours = Math.floor((minutes % 1440) / 60);
        const remainingMinutes = minutes % 60;
        
        let result = `${days} ${isRussian ? 'д' : 'd'}`;
        if (hours > 0) result += ` ${hours} ${isRussian ? 'ч' : 'h'}`;
        if (remainingMinutes > 0) result += ` ${remainingMinutes} ${isRussian ? 'мин' : 'min'}`;
        
        return result;
    }
}

// Функция расчета downtime в минутах (если не определена в utils)
function calculateDowntimeMinutes(startTime, endTime = null) {
    if (!startTime) return 0;
    
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    
    return Math.floor((end - start) / 60000); // миллисекунды в минуты
} 