/* Карта тури ва манба тугмалари */

/* Кнопки источника карты */
.map-source-buttons {
    display: flex;
    gap: 3px;
}

.map-source-buttons button {
    padding: 6px;
    background: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.map-source-buttons button:first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.map-source-buttons button:last-child {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.map-source-buttons button.active {
    background: #4285F4;
}

.map-source-buttons button:hover:not(.active) {
    background: #f4f4f4;
}

.map-source-logo {
    width: 24px;
    height: 24px;
    object-fit: contain;
}

/* Кнопки типа карты */
.map-type-buttons {
    display: flex;
    gap: 3px;
}

.map-type-buttons button {
    padding: 6px 12px;
    background: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    color: #555;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.map-type-buttons button:first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.map-type-buttons button:last-child {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.map-type-buttons button.active {
    background: #4285F4;
    color: white;
}

.map-type-buttons button:hover:not(.active) {
    background: #f4f4f4;
} 