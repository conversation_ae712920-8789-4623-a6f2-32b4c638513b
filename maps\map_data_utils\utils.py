"""
Карта маълумотлари учун йўрдамчи функциялар
"""
import re


def getNumberBS(bsname):
    """BS номидан рақамни ажратиб олиш"""
    return bsname.split('_')[-1] if bsname and '_' in bsname else re.search(r'\d+$', bsname or '').group(0) if bsname and re.search(r'\d+$', bsname) else None


def get_cabinet_type(bsname):
    """BS номидан кабинет турини аниқлаш"""
    return 'LTE' if bsname and 'lte' in bsname.lower() else 'BTS' if bsname and 'bts' in bsname.lower() else 'NodeB' if bsname and 'node' in bsname.lower() else None 