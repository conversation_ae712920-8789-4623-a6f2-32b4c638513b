/* Кластер<PERSON>ар учун CSS стиллар */

/* Асосий кластер стиллари */
.custom-cluster-icon {
    background: transparent !important;
    border: none !important;
}

.cluster-icon {
    background: #2d89ef;
    border: 2px solid #fff;
    border-radius: 50%;
    text-align: center;
    color: white;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    transition: all 0.3s ease;
}

/* Кластер турлари */
.cluster-branch {
    background: #1e3a5f;  /* Тўқроқ кўк-кулранг */
    width: 70px;
    height: 70px;
    font-size: 12px;
    border: 3px solid #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.4);
}

.cluster-region {
    background: #7b4397;  /* Тўқроқ бинафша */
    width: 55px;
    height: 55px;
    font-size: 14px;
    border: 2px solid #fff;
    box-shadow: 0 2px 6px rgba(0,0,0,0.35);
}

.cluster-area {
    background: #2874a6;  /* Тўқроқ кўк */
    width: 48px;
    height: 48px;
    font-size: 13px;
    border: 2px solid #fff;
}

.cluster-default {
    background: #2d89ef;
    width: 40px;
    height: 40px;
    font-size: 14px;
}

/* Аварияли кластерлар */
.cluster-has-offline {
    background: #e74c3c !important;
    animation: pulse-red 2s infinite;
}

.cluster-has-offline.cluster-branch {
    background: #922b21 !important;
    border: 3px solid #ffcccc;
}

.cluster-has-offline.cluster-region {
    background: #a93226 !important;
    border: 2px solid #ffcccc;
}

.cluster-has-offline.cluster-area {
    background: #e74c3c !important;
}

/* Кластер матнлари */
.cluster-name {
    font-size: 11px;
    font-weight: bold;
    line-height: 1;
    margin-bottom: 2px;
    text-transform: uppercase;
    color: #ffffff;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8), -1px -1px 2px rgba(0,0,0,0.8);
    letter-spacing: 0.5px;
}

.cluster-branch .cluster-name {
    font-size: 12px;
    margin-bottom: 3px;
    font-weight: 900;
}

.cluster-region .cluster-name {
    font-size: 11px;
    font-weight: bold;
}

.cluster-area .cluster-name {
    font-size: 10px;
    font-weight: bold;
}

.cluster-count {
    line-height: 1;
    font-weight: bold;
    font-size: 16px;
    color: #ffffff;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
}

.cluster-branch .cluster-count {
    font-size: 20px;
    font-weight: 900;
}

.cluster-region .cluster-count {
    font-size: 17px;
    font-weight: bold;
}

.cluster-area .cluster-count {
    font-size: 15px;
    font-weight: bold;
}

.cluster-offline-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ff6b6b;
    color: white;
    border-radius: 50%;
    font-size: 11px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    font-weight: bold;
    box-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

/* Анимациялар */
@keyframes pulse-red {
    0% { 
        box-shadow: 0 2px 5px rgba(0,0,0,0.3), 0 0 0 0 rgba(231, 76, 60, 0.7);
    }
    70% {
        box-shadow: 0 2px 5px rgba(0,0,0,0.3), 0 0 0 10px rgba(231, 76, 60, 0);
    }
    100% {
        box-shadow: 0 2px 5px rgba(0,0,0,0.3), 0 0 0 0 rgba(231, 76, 60, 0);
    }
}

/* Hover эффектлари */
.cluster-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0,0,0,0.4);
    z-index: 1000;
}

/* Кластер tooltip стиллари - кора фон муаммосини ҳал қилиш */
.cluster-tooltip {
    background: rgba(255, 255, 255, 0.95) !important;
    color: #333 !important;
    border: 1px solid #ccc !important;
    border-radius: 4px !important;
    padding: 6px 10px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
    z-index: 10000 !important;
}

.cluster-tooltip::before {
    border-top-color: rgba(255, 255, 255, 0.95) !important;
}



/* Leaflet кластер анимациялари */
.leaflet-cluster-anim .leaflet-marker-icon, 
.leaflet-cluster-anim .leaflet-marker-shadow {
    transition: transform 0.3s ease-out, opacity 0.3s ease-in;
}

/* Зум даражасига қараб кластер ўлчамлари */
.leaflet-zoom-level-3 .cluster-region,
.leaflet-zoom-level-4 .cluster-region,
.leaflet-zoom-level-5 .cluster-region {
    width: 55px;
    height: 55px;
    font-size: 17px;
}

.leaflet-zoom-level-6 .cluster-area,
.leaflet-zoom-level-7 .cluster-area,
.leaflet-zoom-level-8 .cluster-area,
.leaflet-zoom-level-9 .cluster-area {
    width: 48px;
    height: 48px;
    font-size: 16px;
}

/* Responsive дизайн */
@media (max-width: 768px) {
    .cluster-icon {
        width: 35px;
        height: 35px;
        font-size: 12px;
    }
    
    .cluster-region {
        width: 42px;
        height: 42px;
        font-size: 14px;
    }
    
    .cluster-area {
        width: 38px;
        height: 38px;
        font-size: 13px;
    }
    
    .cluster-offline-count {
        width: 16px;
        height: 16px;
        font-size: 9px;
        top: -6px;
        right: -6px;
    }
    
    .cluster-tooltip {
        font-size: 11px !important;
        padding: 5px 8px !important;
    }
}

/* Кластер spiderfy анимацияси */
.leaflet-cluster-spider-leg {
    stroke-width: 1.5;
    stroke: #222;
    fill: none;
}

/* Махсус кластер маркерлари */
.cluster-marker-shadow {
    background: rgba(0,0,0,0.2);
    border-radius: 50%;
    position: absolute;
    transform: translate(-50%, -50%);
    z-index: -1;
}

/* Активитет индикатори */
.cluster-activity-indicator {
    position: absolute;
    top: -2px;
    left: -2px;
    width: 8px;
    height: 8px;
    background: #00ff00;
    border-radius: 50%;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
} 