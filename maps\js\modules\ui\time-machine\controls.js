// Машина времени - интерфейс бошқаруви модули

window.isTimeMachineEnabled = false;
let selectedDateTime = null;
let timeMachineInterval = null;
window.originalBsData = null; // Сохраняем оригинальные данные БС глобально

// Инициализация машины времени
window.initTimeMachine = function () {
    // Янги time machine тугмаси ва контейнери
    const timeMachineBtn = document.getElementById('time-machine-btn');
    const timeMachineContainer = document.getElementById('time-machine-floating-container');
    
    // Tooltip элементини қўшиш
    if (timeMachineBtn && !timeMachineBtn.querySelector('.tooltip-text')) {
        const tooltip = document.createElement('span');
        tooltip.className = 'tooltip-text';
        tooltip.textContent = window.translations[window.currentLanguage]['time_machine'];
        timeMachineBtn.appendChild(tooltip);
    }
    
    // Пробуем оба варианта ID для совместимости
    let timeMachineCheckbox = document.getElementById('time-machine-checkbox');
    if (!timeMachineCheckbox) {
        timeMachineCheckbox = document.getElementById('time-machine-enabled');
    }
    
    // Если элемент не найден, выходим без ошибки
    if (!timeMachineCheckbox) {
        console.warn('Time Machine checkbox элементи топилмади');
        return;
    }
    
    const timeMachineControls = document.getElementById('time-machine-controls');
    const timeMachineDateInput = document.getElementById('time-machine-date');
    const timeMachineHourInput = document.getElementById('time-machine-hour');
    const timeMachineMinuteInput = document.getElementById('time-machine-minute');

    // Time machine тугмаси учун обработчик
    if (timeMachineBtn) {
        timeMachineBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            const isShowing = timeMachineContainer.classList.contains('show');
            
            if (!isShowing) {
                // Очишдан олдин тайм машинани актив қилиш
                if (!window.isTimeMachineEnabled) {
                    timeMachineCheckbox.checked = true;
                    timeMachineCheckbox.dispatchEvent(new Event('change'));
                }
                timeMachineContainer.style.display = 'block';
                setTimeout(() => {
                    timeMachineContainer.classList.add('show');
                }, 10);
            } else {
                // Ёпиш ва деактив қилиш
                timeMachineContainer.classList.remove('show');
                setTimeout(() => {
                    timeMachineContainer.style.display = 'none';
                }, 300);
                
                // Time machine ни деактив қилиш
                if (window.isTimeMachineEnabled) {
                    timeMachineCheckbox.checked = false;
                    timeMachineCheckbox.dispatchEvent(new Event('change'));
                }
            }
        });
    }
    
    // Контейнер ичидаги элементларга босилганда event propagation ни тўхтатиш
    if (timeMachineContainer) {
        timeMachineContainer.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }

    // Навигация тугмалари текстини танланган тилга мос қилиш
    const lang = window.currentLanguage || 'ru';
    document.getElementById('time-back-hour').textContent = lang === 'ru' ? '-1ч' : '-1h';
    document.getElementById('time-back-hour').title = lang === 'ru' ? '-1 час' : '-1 hour';
    document.getElementById('time-back-minute').textContent = lang === 'ru' ? '-5м' : '-5m';
    document.getElementById('time-back-minute').title = lang === 'ru' ? '-5 минут' : '-5 minutes';
    document.getElementById('time-forward-minute').textContent = lang === 'ru' ? '+5м' : '+5m';
    document.getElementById('time-forward-minute').title = lang === 'ru' ? '+5 минут' : '+5 minutes';
    document.getElementById('time-forward-hour').textContent = lang === 'ru' ? '+1ч' : '+1h';
    document.getElementById('time-forward-hour').title = lang === 'ru' ? '+1 час' : '+1 hour';
    document.getElementById('time-now').title = lang === 'ru' ? 'Сейчас' : 'Now';
    
    // Заполнение селекта часов
    for (let i = 0; i < 24; i++) {
        const option = document.createElement('option');
        option.value = i;
        option.textContent = String(i).padStart(2, '0');
        timeMachineHourInput.appendChild(option);
    }

    // Заполнение селекта минут (с шагом 5 минут)
    for (let i = 0; i < 60; i += 5) {
        const option = document.createElement('option');
        option.value = i;
        option.textContent = String(i).padStart(2, '0');
        timeMachineMinuteInput.appendChild(option);
    }

    // Установка текущей даты по умолчанию
    const now = new Date();
    timeMachineDateInput.value = now.toISOString().split('T')[0];
    timeMachineHourInput.value = now.getHours();
    timeMachineMinuteInput.value = Math.floor(now.getMinutes() / 5) * 5;
    
    // Bootstrap datepicker инициализацияси
    if (timeMachineDateInput) {
        $(timeMachineDateInput).datepicker({
            format: 'yyyy-mm-dd',
            language: window.currentLanguage === 'ru' ? 'ru' : 'en',
            autoclose: true,
            todayHighlight: true,
            todayBtn: 'linked',
            clearBtn: false,
            container: '.time-machine-floating-container',
            orientation: 'bottom auto'
        });
    }

    // Обработчик включения/выключения
    timeMachineCheckbox.addEventListener('change', function () {
        // Авария режими фаол бўлса, time machine ни ёқишга рухсат бермаймиз
        if (this.checked && window.AlarmManager && window.AlarmManager.isAlarmModeActive()) {
            alert(window.translations[window.currentLanguage]['cannot_enable_time_machine_with_alarm']);
            this.checked = false;
            return;
        }
        
        window.isTimeMachineEnabled = this.checked;
        // Time machine controls ҳар доим кўринади
        timeMachineControls.style.display = 'block';

        // Time machine тугмасини актив/неактив қилиш
        if (timeMachineBtn) {
            if (this.checked) {
                timeMachineBtn.classList.add('active');
            } else {
                timeMachineBtn.classList.remove('active');
            }
        }

        if (this.checked) {
            // Авария режими тугмасини блоклаш
            const alarmToggleBtn = document.querySelector('.alarm-toggle-btn');
            if (alarmToggleBtn) {
                alarmToggleBtn.disabled = true;
                alarmToggleBtn.style.opacity = '0.5';
                alarmToggleBtn.style.cursor = 'not-allowed';
            }
            
            // Сохраняем оригинальные данные БС
            if (!window.originalBsData && window.globalStations) {
                window.originalBsData = [...window.globalStations];
            }

            // Машина времени режимида downtime legend ни кўрсатиб қолдириш
            const downtimeLegend = document.querySelector('.downtime-legend');
            if (downtimeLegend) {
                downtimeLegend.style.display = 'block';
            }

            // Останавливаем автообновление
            if (window.refreshInterval) {
                clearInterval(window.refreshInterval);
            }
            
            // Применяем машину времени
            if (window.applyTimeMachine) {
                window.applyTimeMachine();
            }
        } else {
            // Возврат к реальному времени
            handleTimeMachineDisable();
        }
    });

    // Обработчики изменения времени
    timeMachineDateInput.addEventListener('change', () => {
        if (window.applyTimeMachine) window.applyTimeMachine();
    });
    
    // Bootstrap datepicker change event
    $(timeMachineDateInput).on('changeDate', function() {
        if (window.applyTimeMachine) window.applyTimeMachine();
    });
    timeMachineHourInput.addEventListener('change', () => {
        if (window.applyTimeMachine) window.applyTimeMachine();
    });
    timeMachineMinuteInput.addEventListener('change', () => {
        if (window.applyTimeMachine) window.applyTimeMachine();
    });

    // Кнопки навигации
    document.getElementById('time-back-hour').addEventListener('click', () => {
        if (window.adjustTime) window.adjustTime(-60);
    });
    document.getElementById('time-back-minute').addEventListener('click', () => {
        if (window.adjustTime) window.adjustTime(-5);
    });
    document.getElementById('time-now').addEventListener('click', () => {
        if (window.setCurrentTime) window.setCurrentTime();
    });
    document.getElementById('time-forward-minute').addEventListener('click', () => {
        if (window.adjustTime) window.adjustTime(5);
    });
    document.getElementById('time-forward-hour').addEventListener('click', () => {
        if (window.adjustTime) window.adjustTime(60);
    });

    // Инициализация слайдера времени
    if (window.initializeTimeSlider) {
        window.initializeTimeSlider();
    }
}

// Обработка отключения машины времени
function handleTimeMachineDisable() {
    // Time machine тугмасини неактив қилиш
    const timeMachineBtn = document.getElementById('time-machine-btn');
    if (timeMachineBtn) {
        timeMachineBtn.classList.remove('active');
    }

    // Авария режими тугмасининг блокини олиб ташлаш
    const alarmToggleBtn = document.querySelector('.alarm-toggle-btn');
    if (alarmToggleBtn) {
        alarmToggleBtn.disabled = false;
        alarmToggleBtn.style.opacity = '';
        alarmToggleBtn.style.cursor = '';
    }

    selectedDateTime = null;
    window.selectedDateTime = null;
    document.getElementById('current-selected-time').textContent = window.translations[window.currentLanguage]['real_time'];

    // Очистка интервала машины времени
    if (timeMachineInterval) {
        clearInterval(timeMachineInterval);
        timeMachineInterval = null;
    }

    // Показываем обратно легенду продолжительности
    const downtimeLegend = document.querySelector('.downtime-legend');
    if (downtimeLegend) {
        downtimeLegend.style.display = 'block';
    }

    // Сбрасываем оригинальные данные
    window.originalBsData = null;

    // Загружаем свежие данные с сервера
    if (window.loadMapData) {
        console.log('🔄 Time Machine ўчирилди - реал вақт маълумотларини қайта юклаш...');
        window.loadMapData();
    } else {
        // Если loadMapData не доступна, используем старый подход
        if (window.originalBsData) {
            window.globalStations = [...window.originalBsData];
            window.points = [...window.originalBsData];
        }

        // Применяем фильтры с восстановленными данными
        if (window.applyCurrentFilters) {
            window.applyCurrentFilters();
        }

        // Обновляем обычную статистику
        if (window.updateRegionsStats) {
            window.updateRegionsStats();
        }

        // Перезапускаем автообновление
        if (window.startAutoRefresh) {
            window.startAutoRefresh();
        }
    }
} 