// Модуль работы с API поиска и геокодинга

// Функция поиска локации через прокси API
async function searchLocationProxy(query, service = 'osm') {
    try {
        const currentLang = window.currentLanguage || 'ru';
        const response = await fetch(`/map/api/geocode/?q=${encodeURIComponent(query)}&service=${service}&lang=${currentLang}`);
        const data = await response.json();
        
        if (data.results && data.results.length > 0) {
            return data.results;
        }
        return null;
    } catch (error) {
        console.error(`${service} geocoding error:`, error);
        return null;
    }
}

// Функция поиска локации
async function searchLocation(query) {
    // Очистка при пустом запросе
    if (!query || query.trim() === '') {
        if (window.searchMarker) {
            window.mymap.removeLayer(window.searchMarker);
            window.searchMarker = null;
        }
        window.SearchUI.hideSearchResults();
        return;
    }

    // Показываем индикатор загрузки
    showSearchLoading();

    let results = null;
    let selectedService = '';

    // Определяем активный источник карты
    if (window.isYandexMode) {
        selectedService = 'yandex';
    } else if (document.getElementById('google-source-button').classList.contains('active')) {
        selectedService = 'google';
    } else {
        selectedService = 'osm';
    }

    // Поиск через выбранный сервис
    results = await searchLocationProxy(query, selectedService);

    // Если результаты не найдены, пробуем другие сервисы
    if (!results || results.length === 0) {
        const otherServices = ['osm', 'yandex', 'google'].filter(s => s !== selectedService);
        
        for (const service of otherServices) {
            results = await searchLocationProxy(query, service);
            if (results && results.length > 0) {
                break;
            }
        }
    }

    hideSearchLoading();

    if (results && results.length > 0) {
        if (results.length === 1) {
            // Если найден только один результат, сразу показываем его
            window.SearchMarker.displaySearchResult(results[0].lat, results[0].lon, results[0].display_name);
        } else {
            // Если найдено несколько результатов, показываем список
            window.SearchUI.showSearchResults(results);
        }
    } else {
        alert(window.translations[window.currentLanguage || 'ru']['location_not_found'] || 'Location not found');
    }
}

// Показать индикатор загрузки
function showSearchLoading() {
    const searchButton = document.getElementById('search-button');
    searchButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    searchButton.disabled = true;
}

// Скрыть индикатор загрузки
function hideSearchLoading() {
    const searchButton = document.getElementById('search-button');
    searchButton.innerHTML = '<i class="fas fa-search"></i>';
    searchButton.disabled = false;
}

// Экспорт функций
window.SearchAPI = {
    searchLocation,
    searchLocationProxy,
    showSearchLoading,
    hideSearchLoading
}; 