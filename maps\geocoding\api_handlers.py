"""
Геокодинг API ҳандлерлари - OSM, Yandex, Google
"""
import requests
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status


@api_view(['GET'])
def geocode_proxy(request):
    """Геокодинг учун прокси эндпоинт - CORS хатосини ҳал қилиш учун"""
    try:
        query = request.query_params.get('q', '')
        service = request.query_params.get('service', 'osm')  # osm, yandex, google
        lang = request.query_params.get('lang', 'ru')
        
        if not query:
            return Response({"error": "Query parameter 'q' is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        results = []
        
        if service == 'osm':
            # OpenStreetMap Nominatim API
            url = "https://nominatim.openstreetmap.org/search"
            params = {
                'format': 'json',
                'q': query,
                'countrycodes': 'uz',
                'limit': 5,
                'addressdetails': 1,
                'accept-language': 'en' if lang == 'en' else 'ru'
            }
            
            headers = {
                'User-Agent': 'Django QNP Map Application/1.0'
            }
            
            response = requests.get(url, params=params, headers=headers, timeout=10)
            if response.status_code == 200:
                osm_results = response.json()
                for item in osm_results:
                    results.append({
                        'lat': float(item['lat']),
                        'lon': float(item['lon']),
                        'display_name': item['display_name'],
                        'type': item.get('type', ''),
                        'source': 'osm'
                    })
        
        elif service == 'yandex':
            # Яндекс Геокодер API
            api_key = 'a14b0e94-914e-40ce-a166-f32fd55744cb'  # Сизнинг API калитингиз
            url = "https://geocode-maps.yandex.ru/1.x/"
            params = {
                'apikey': api_key,
                'geocode': query + ', Узбекистан',
                'format': 'json',
                'results': 5,
                'lang': 'ru_RU' if lang == 'ru' else 'en_US'
            }
            
            response = requests.get(url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                features = data.get('response', {}).get('GeoObjectCollection', {}).get('featureMember', [])
                
                for feature in features:
                    geo_object = feature.get('GeoObject', {})
                    pos = geo_object.get('Point', {}).get('pos', '').split()
                    if len(pos) == 2:
                        results.append({
                            'lat': float(pos[1]),
                            'lon': float(pos[0]),
                            'display_name': geo_object.get('name', '') + ', ' + geo_object.get('description', ''),
                            'type': geo_object.get('metaDataProperty', {}).get('GeocoderMetaData', {}).get('kind', ''),
                            'source': 'yandex'
                        })
        
        elif service == 'google':
            # Google Geocoding API
            api_key = 'AIzaSyBFw0Qbyq9zTFTd-tUY6dOWTgaJ-gOqOVA'  # Сизнинг API калитингиз
            url = "https://maps.googleapis.com/maps/api/geocode/json"
            params = {
                'address': query + ', Uzbekistan',
                'key': api_key,
                'region': 'uz',
                'language': 'ru' if lang == 'ru' else 'en'
            }
            
            response = requests.get(url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data['status'] == 'OK':
                    for item in data['results'][:5]:
                        location = item['geometry']['location']
                        results.append({
                            'lat': location['lat'],
                            'lon': location['lng'],
                            'display_name': item['formatted_address'],
                            'type': ', '.join(item.get('types', [])),
                            'source': 'google'
                        })
        
        return Response({
            'results': results,
            'query': query,
            'service': service
        })
        
    except requests.exceptions.Timeout:
        return Response({"error": "Request timeout"}, status=status.HTTP_408_REQUEST_TIMEOUT)
    except requests.exceptions.RequestException as e:
        return Response({"error": f"Request failed: {str(e)}"}, status=status.HTTP_502_BAD_GATEWAY)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 