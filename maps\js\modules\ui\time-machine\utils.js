// Машина времени - утилита функциялари модули

// Расчет продолжительности простоя
window.calculateDowntimeMinutes = function (appearTime, clearTime = null) {
    const startDate = new Date(appearTime);
    const endDate = clearTime ? new Date(clearTime) : (window.selectedDateTime || new Date());

    const diffMs = endDate - startDate;
    return Math.floor(diffMs / (1000 * 60));
}

// Форматирование продолжительности простоя
window.formatDowntime = function (minutes) {
    if (minutes < 60) {
        return minutes + ' ' + (window.currentLanguage === 'ru' ? 'мин' : 'min');
    }

    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;

    if (hours < 24) {
        return mins > 0 ?
            hours + ' ' + (window.currentLanguage === 'ru' ? 'ч' : 'h') + ' ' + mins + ' ' + (window.currentLanguage === 'ru' ? 'мин' : 'min') :
            hours + ' ' + (window.currentLanguage === 'ru' ? 'ч' : 'h');
    }

    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;

    let result = days + ' ' + (window.currentLanguage === 'ru' ? 'д' : 'd');
    if (remainingHours > 0) {
        result += ' ' + remainingHours + ' ' + (window.currentLanguage === 'ru' ? 'ч' : 'h');
    }

    return result;
}

// Обновление статистики для машины времени
window.updateRegionsStatsForTimeMachine = function () {
    // Регионлар статистикасини ҳисоблаш
    const regionStats = {};
    const totalStats = { total: 0, active: 0, inactive: 0 };

    // Барча БС ларни кўриб чиқиш
    window.globalStations.forEach(station => {
        const regionId = station.region_id;
        if (!regionStats[regionId]) {
            regionStats[regionId] = { total: 0, active: 0, inactive: 0 };
        }

        regionStats[regionId].total++;
        totalStats.total++;

        if (station.status === true || station.status === 'true' || station.status === 1 || station.status === '1') {
            regionStats[regionId].inactive++;
            totalStats.inactive++;
        } else {
            regionStats[regionId].active++;
            totalStats.active++;
        }
    });

    // Жадвалдаги регион қаторларини янгилаш
    const regionRows = document.querySelectorAll('.region-row');
    regionRows.forEach(row => {
        const regionId = row.getAttribute('data-region-id');
        const stats = regionStats[regionId] || { total: 0, active: 0, inactive: 0 };

        // Қаторни янгилаш
        const cells = row.querySelectorAll('td');
        if (cells.length >= 4) {
            cells[1].textContent = stats.total;
            cells[2].textContent = stats.active;
            cells[3].textContent = stats.inactive;
        }
    });

    // Умумий статистикани янгилаш
    const totalBsCountElement = document.getElementById('total-bs-count');
    const activeBsCountElement = document.getElementById('active-bs-count');
    const inactiveBsCountElement = document.getElementById('inactive-bs-count');
    
    if (totalBsCountElement) totalBsCountElement.textContent = totalStats.total;
    if (activeBsCountElement) activeBsCountElement.textContent = totalStats.active;
    if (inactiveBsCountElement) inactiveBsCountElement.textContent = totalStats.inactive;

    // Downtime duration статистикасини ҳисоблаш ва янгилаш
    if (window.calculateDurationStatsForTimeMachine) {
        window.calculateDurationStatsForTimeMachine(window.globalStations);
    } else if (window.calculateDurationStats) {
        // Агар махсус функция йўқ бўлса, умумий функцияни ишлатиш
        window.calculateDurationStats(window.globalStations);
    }
}

// Машина времени учун downtime duration статистикасини ҳисоблаш
window.calculateDurationStatsForTimeMachine = function (stations) {
    // Танланган вилоят ва туманни олиш
    const regionElement = document.getElementById('region');
    const areaElement = document.getElementById('area');
    const regionId = regionElement ? regionElement.value : '';
    const areaId = areaElement ? areaElement.value : '';
    
    // Фильтрлаш
    let filteredStations = [...stations];
    if (regionId) {
        filteredStations = filteredStations.filter(station => station.region_id == regionId);
    }
    if (areaId) {
        filteredStations = filteredStations.filter(station => station.area_id == areaId);
    }
    
    // Сброс статистики
    const durationStats = { '1': 0, '2': 0, '3': 0, '4': 0, '6': 0, '7': 0, '8': 0 };

    // Фильтрация только аварийных БС
    const offlineStations = filteredStations.filter(station => {
        return station.status === true || station.status === 'true' || station.status === 1 || station.status === '1';
    });

    // Группировка по продолжительности
    offlineStations.forEach(station => {
        const durationGroup = window.getDowntimeDurationForTimeMachine(station);
        if (durationGroup > 0) {
            durationStats[durationGroup]++;
        }
    });

    // Обновление отображения
    [1, 2, 3, 4, 6, 7, 8].forEach(i => {
        const countElement = document.getElementById('downtime-count-' + i);
        if (countElement) {
            countElement.textContent = durationStats[i] || 0;
        }
    });

    // Общее количество аварийных БС
    const totalOffline = Object.values(durationStats).reduce((acc, val) => acc + val, 0);
    const countAllElement = document.getElementById('downtime-count-all');
    if (countAllElement) {
        countAllElement.textContent = totalOffline;
    }

    return durationStats;
}

// Машина времени учун downtime duration ни аниқлаш
window.getDowntimeDurationForTimeMachine = function (station) {
    if (!station.status || !station.alarms_by_tech) return 0;

    // Энг узоқ давом этган аварияни топиш
    let maxDurationHours = 0;

    Object.values(station.alarms_by_tech).forEach(alarm => {
        let durationHours = 0;
        
        // duration_hours майдони мавжуд бўлса
        if (alarm.duration_hours !== undefined && alarm.duration_hours !== null) {
            durationHours = parseFloat(alarm.duration_hours);
        }
        // duration майдони мавжуд бўлса
        else if (alarm.duration !== undefined && alarm.duration !== null) {
            if (typeof alarm.duration === 'object' && alarm.duration.total_seconds) {
                durationHours = alarm.duration.total_seconds() / 3600;
            } else if (typeof alarm.duration === 'string') {
                // Агар string форматда бўлса парс қилиш
                const match = alarm.duration.match(/(\d+)\s*hours?\s*(\d+)?\s*minutes?/i);
                if (match) {
                    durationHours = parseInt(match[1]) + (parseInt(match[2] || 0) / 60);
                }
            } else if (typeof alarm.duration === 'number') {
                durationHours = alarm.duration;
            }
        }
        // Агар duration йўқ бўлса, appeartime дан ҳисоблаш
        else if (alarm.appeartime && window.selectedDateTime) {
            const appearTime = new Date(alarm.appeartime);
            const duration = window.selectedDateTime - appearTime;
            durationHours = duration / (1000 * 60 * 60);
        }

        maxDurationHours = Math.max(maxDurationHours, durationHours);
    });

    // Группага ажратиш
    if (maxDurationHours <= 1) return 1;
    else if (maxDurationHours <= 2) return 2;
    else if (maxDurationHours <= 3) return 3;
    else if (maxDurationHours <= 4) return 4;
    else if (maxDurationHours <= 24) return 6;  // 4 соат - 1 кун
    else if (maxDurationHours <= 168) return 7; // 1 кун - 7 кун (168 соат = 7 кун)
    else return 8; // 7 кундан кўп
} 