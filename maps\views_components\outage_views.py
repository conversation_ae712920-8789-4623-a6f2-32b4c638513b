"""
Авария сабаблари билан ишлаш модули
"""
import json
from datetime import datetime
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from ..models import OutageReason


@csrf_exempt
@require_http_methods(["POST"])
@login_required
def save_outage_reason(request):
    """
    API для сохранения причины аварии в зоне ≥3 БС
    """
    try:
        data = json.loads(request.body)

        # Валидация обязательных полей
        required_fields = ['group_id', 'reason', 'stations_count', 'timestamp']
        for field in required_fields:
            if field not in data:
                return JsonResponse({
                    'success': False,
                    'error': f'Отсутствует обязательное поле: {field}'
                }, status=400)

        # Извлечение данных
        group_id = data['group_id']
        reason = data['reason'].strip()
        stations_count = data['stations_count']
        timestamp = datetime.fromtimestamp(data['timestamp'] / 1000)  # JavaScript timestamp в миллисекундах

        # Данные о местоположении
        location = data.get('location', {})
        area_name = location.get('area', '')
        region_name = location.get('region', '')

        # Данные о станциях
        stations_data = data.get('stations', [])

        # Проверка длины причины
        if len(reason) < 5:
            return JsonResponse({
                'success': False,
                'error': 'Причина должна содержать минимум 5 символов'
            }, status=400)

        # Создание или обновление записи
        outage_reason, created = OutageReason.objects.update_or_create(
            group_id=group_id,
            defaults={
                'reason': reason,
                'area_name': area_name,
                'region_name': region_name,
                'stations_count': stations_count,
                'stations_data': json.dumps(stations_data) if stations_data else None,
                'incident_timestamp': timestamp,
                'created_by': request.user
            }
        )

        return JsonResponse({
            'success': True,
            'message': 'Причина успешно сохранена' if created else 'Причина успешно обновлена',
            'data': {
                'id': outage_reason.id,
                'group_id': outage_reason.group_id,
                'reason': outage_reason.reason,
                'location': outage_reason.get_location_display(),
                'stations_count': outage_reason.stations_count,
                'created_at': outage_reason.created_at.isoformat(),
                'updated_at': outage_reason.updated_at.isoformat()
            }
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Неверный формат JSON'
        }, status=400)
    except ValueError as e:
        return JsonResponse({
            'success': False,
            'error': f'Ошибка валидации: {str(e)}'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Внутренняя ошибка сервера: {str(e)}'
        }, status=500)


@login_required
def get_outage_reasons(request):
    """
    API для получения списка причин аварий
    """
    try:
        # Параметры фильтрации
        group_id = request.GET.get('group_id')
        area_name = request.GET.get('area')
        region_name = request.GET.get('region')
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')

        # Базовый запрос
        queryset = OutageReason.objects.all()

        # Применение фильтров
        if group_id:
            queryset = queryset.filter(group_id=group_id)
        if area_name:
            queryset = queryset.filter(area_name__icontains=area_name)
        if region_name:
            queryset = queryset.filter(region_name__icontains=region_name)
        if date_from:
            date_from_parsed = datetime.strptime(date_from, '%Y-%m-%d')
            queryset = queryset.filter(incident_timestamp__gte=date_from_parsed)
        if date_to:
            date_to_parsed = datetime.strptime(date_to, '%Y-%m-%d')
            queryset = queryset.filter(incident_timestamp__lte=date_to_parsed)

        # Сериализация данных
        reasons = []
        for reason in queryset[:100]:  # Ограничение на 100 записей
            # JSON десериализация для stations_data
            try:
                stations_data = json.loads(reason.stations_data) if reason.stations_data else []
            except (json.JSONDecodeError, TypeError):
                stations_data = []

            reasons.append({
                'id': reason.id,
                'group_id': reason.group_id,
                'reason': reason.reason,
                'area_name': reason.area_name,
                'region_name': reason.region_name,
                'location': reason.get_location_display(),
                'stations_count': reason.stations_count,
                'stations_data': stations_data,
                'incident_timestamp': reason.incident_timestamp.isoformat(),
                'created_at': reason.created_at.isoformat(),
                'updated_at': reason.updated_at.isoformat(),
                'created_by': reason.created_by.username if reason.created_by else None
            })

        return JsonResponse({
            'success': True,
            'data': reasons,
            'count': len(reasons)
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Ошибка при получении данных: {str(e)}'
        }, status=500) 