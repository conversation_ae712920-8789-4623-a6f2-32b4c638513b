/* Сти<PERSON><PERSON> <PERSON>ader */
.loader-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s, visibility 0.5s;
}

.loader {
    width: 120px;
    height: 120px;
    border: 5px solid transparent;
    border-top-color: #F9A825;
    border-radius: 50%;
    animation: spin 1.5s linear infinite;
    position: relative;
}

.loader:before,
.loader:after {
    content: "";
    position: absolute;
    border: 5px solid transparent;
    border-radius: 50%;
}

.loader:before {
    top: 5px;
    left: 5px;
    right: 5px;
    bottom: 5px;
    border-top-color: #E53935;
    animation: spin 2s linear infinite;
}

.loader:after {
    top: 15px;
    left: 15px;
    right: 15px;
    bottom: 15px;
    border-top-color: #64B5F6;
    animation: spin 1s linear infinite;
}

.loader-text {
    margin-top: 30px;
    font-family: 'Arial', sans-serif;
    font-size: 28px;
    color: white;
    letter-spacing: 3px;
    position: relative;
}

.loader-text span {
    display: inline-block;
    opacity: 0;
    animation: fadeIn 1s ease-in-out infinite;
}

.loader-text span:nth-child(n+1) {
    animation-delay: calc(0.05s * var(--n));
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes fadeIn {

    0%,
    100% {
        opacity: 0;
        transform: translateY(5px);
    }

    50% {
        opacity: 1;
        transform: translateY(0);
    }
} 