/* Стили легенды продолжительности аварий */
.downtime-legend {
    position: absolute;
    bottom: 20px;
    left: 310px;
    z-index: 1000;
    background-color: rgba(255, 255, 255, 0.7);
    padding: 8px;
    border-radius: 6px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
    max-width: 240px;
    font-size: 11px;
}

.downtime-legend h5 {
    margin: 0 0 6px 0;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
}

/* Downtime duration items */
.downtime-item {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 3px;
    background: transparent;
    border: 1px solid transparent;
}

.downtime-item:hover {
    background: rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.1);
}

.downtime-item.active {
    background: rgba(33, 150, 243, 0.15);
    border-color: #2196F3;
}

/* Авария режимида duration filter стиллари */
.alarm-mode .downtime-item {
    opacity: 1;
    pointer-events: auto;
}

.alarm-mode .downtime-item:hover {
    background: rgba(255, 165, 0, 0.1);
    border-color: rgba(255, 165, 0, 0.2);
}

.alarm-mode .downtime-item.active {
    background: rgba(255, 165, 0, 0.15);
    border-color: #FFA500;
}

.color-box {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    margin-right: 6px;
    border: 1px solid rgba(0, 0, 0, 0.2);
}

.downtime-label {
    flex: 1;
}

.downtime-count {
    font-weight: bold;
    margin-left: 5px;
}

.show-all-item {
    border-top: 1px solid #ddd;
    padding-top: 5px;
    margin-top: 5px;
} 