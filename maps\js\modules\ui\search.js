// Модуль работы с поиском - основной координирующий модуль

// Маркер для результата поиска
window.searchMarker = null;

// Инициализация поиска
window.initSearch = function () {
    const searchInput = document.getElementById('location-search');
    const searchButton = document.getElementById('search-button');
    const container = searchInput.parentElement;
    
    if (!searchInput || !searchButton) {
        console.warn('Search elements not found');
        return;
    }
    
    container.classList.add('search-container');

    // Обработчик для кнопки поиска
    searchButton.addEventListener('click', function () {
        const query = searchInput.value.trim();
        if (query) {
            window.SearchAPI.searchLocation(query);
        }
    });

    // Настройка обработчиков автокомплита
    window.SearchAutocomplete.setupSearchInputHandlers();
} 