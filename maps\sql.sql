-- Керакли ўзгарувчиларни ўрнатамиз
SET @db_name = 'beedb';
SET @user_table_name = 'accounts_customuser';
SET @constraint_name = 'outage_reasons_updated_by_id_fk_accounts_customuser_id';
SET @old_created_by_constraint = 'outage_reasons_created_by_id_fk_auth_user_id'; -- Эски ном (тахминий)

-- 1. center_lat устунини қўшиш (агар йўқ бўлса)
SET @sql = IF(
    NOT EXISTS(
        SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = @db_name AND TABLE_NAME = 'outage_reasons' AND COLUMN_NAME = 'center_lat'
    ),
    'ALTER TABLE `outage_reasons` ADD COLUMN `center_lat` FLOAT NULL DEFAULT NULL;',
    'SELECT "Column center_lat already exists." AS status;'
);
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 2. center_lon устунини қўшиш (агар йўқ бўлса)
SET @sql = IF(
    NOT EXISTS(
        SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = @db_name AND TABLE_NAME = 'outage_reasons' AND COLUMN_NAME = 'center_lon'
    ),
    'ALTER TABLE `outage_reasons` ADD COLUMN `center_lon` FLOAT NULL DEFAULT NULL;',
    'SELECT "Column center_lon already exists." AS status;'
);
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 3. updated_by_id устунини қўшиш (агар йўқ бўлса)
SET @sql = IF(
    NOT EXISTS(
        SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = @db_name AND TABLE_NAME = 'outage_reasons' AND COLUMN_NAME = 'updated_by_id'
    ),
    'ALTER TABLE `outage_reasons` ADD COLUMN `updated_by_id` BIGINT NULL DEFAULT NULL;',
    'SELECT "Column updated_by_id already exists." AS status;'
);
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 4. Эски, нотўғри FOREIGN KEY алоқасини (updated_by_id учун) ўчиришга уриниш
SET @sql = IF(
    EXISTS(
        SELECT 1 FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
        WHERE CONSTRAINT_SCHEMA = @db_name AND TABLE_NAME = 'outage_reasons' AND CONSTRAINT_NAME = @constraint_name
    ),
    CONCAT('ALTER TABLE `outage_reasons` DROP FOREIGN KEY `', @constraint_name, '`;'),
    'SELECT "No existing constraint to drop for updated_by_id." AS status;'
);
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 5. `updated_by_id` устунининг турини `BIGINT`га ўзгартириш (агар мавжуд бўлса)
SET @sql = IF(
    EXISTS(
        SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = @db_name AND TABLE_NAME = 'outage_reasons' AND COLUMN_NAME = 'updated_by_id'
    ),
    'ALTER TABLE `outage_reasons` MODIFY COLUMN `updated_by_id` BIGINT NULL DEFAULT NULL;',
    'SELECT "Column updated_by_id does not exist." AS status;'
);
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 6. `created_by_id` устунининг турини `BIGINT`га ўзгартириш
-- Аввал эски алоқани ўчирамиз (агар бўлса)
SET @sql = IF(
    EXISTS(
        SELECT 1 FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
        WHERE CONSTRAINT_SCHEMA = @db_name AND TABLE_NAME = 'outage_reasons' AND CONSTRAINT_NAME = @old_created_by_constraint
    ),
    CONCAT('ALTER TABLE `outage_reasons` DROP FOREIGN KEY `', @old_created_by_constraint, '`;'),
    'SELECT "No existing constraint to drop for created_by_id." AS status;'
);
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Энди устун турини ўзгартирамиз
ALTER TABLE `outage_reasons` MODIFY COLUMN `created_by_id` BIGINT NULL DEFAULT NULL;

-- 7. `updated_by_id` учун янги, тўғри FOREIGN KEY алоқасини қўшиш
SET @sql = IF(
    NOT EXISTS(
        SELECT 1 FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
        WHERE CONSTRAINT_SCHEMA = @db_name AND TABLE_NAME = 'outage_reasons' AND CONSTRAINT_NAME = @constraint_name
    ),
    CONCAT(
        'ALTER TABLE `outage_reasons` ADD CONSTRAINT `', @constraint_name, '` ',
        'FOREIGN KEY (`updated_by_id`) REFERENCES `', @user_table_name, '` (`id`) ON DELETE SET NULL;'
    ),
    CONCAT('SELECT "Constraint for updated_by_id already exists." AS status;')
);
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 8. `created_by_id` учун янги, тўғри FOREIGN KEY алоқасини қўшиш
SET @new_created_by_constraint = 'outage_reasons_created_by_id_fk_accounts_customuser_id';
SET @sql = IF(
    NOT EXISTS(
        SELECT 1 FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
        WHERE CONSTRAINT_SCHEMA = @db_name AND TABLE_NAME = 'outage_reasons' AND CONSTRAINT_NAME = @new_created_by_constraint
    ),
    CONCAT(
        'ALTER TABLE `outage_reasons` ADD CONSTRAINT `', @new_created_by_constraint, '` ',
        'FOREIGN KEY (`created_by_id`) REFERENCES `', @user_table_name, '` (`id`) ON DELETE SET NULL;'
    ),
    CONCAT('SELECT "Constraint for created_by_id already exists." AS status;')
);
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
