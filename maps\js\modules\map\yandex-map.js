// Яндекс карта модули

// Инициализация Яндекс карты
function initYandexMap() {
    if (window.yandexMap) return;

    // Создание контейнера для Яндекс карты
    window.yandexMapContainer = document.createElement('div');
    window.yandexMapContainer.id = 'yandex-map-container';
    window.yandexMapContainer.style.display = 'none';
    window.yandexMapContainer.style.width = '100%';
    window.yandexMapContainer.style.height = '100%';
    window.yandexMapContainer.style.position = 'absolute';
    window.yandexMapContainer.style.top = '0';
    window.yandexMapContainer.style.left = '0';
    window.yandexMapContainer.style.zIndex = '50';
    document.getElementById('mapid').appendChild(window.yandexMapContainer);

    // Инициализация Яндекс карты при готовности API
    if (typeof ymaps !== 'undefined') {
        ymaps.ready(function () {
                    window.yandexMap = new ymaps.Map('yandex-map-container', {
            center: [41.3, 69.3],
            zoom: 6,
            controls: []
        });

            window.yandexGeocoder = new ymaps.geocode;
            window.isYandexAPILoaded = true;

            // Плейсхолдер слои для синхронизации
            window.yandexMapLayer = {
                addTo: function (map) {
                    window.yandexMapContainer.style.display = 'block';
                    window.syncYandexMapWithLeaflet();
                    return this;
                },
                removeFrom: function (map) {
                    window.yandexMapContainer.style.display = 'none';
                    return this;
                }
            };

            window.yandexSatelliteLayer = {
                addTo: function (map) {
                    if (window.yandexMap) {
                        window.yandexMap.setType('yandex#satellite');
                        window.yandexMapContainer.style.display = 'block';
                        window.syncYandexMapWithLeaflet();
                    }
                    return this;
                },
                removeFrom: function (map) {
                    window.yandexMapContainer.style.display = 'none';
                    return this;
                }
            };

            window.syncYandexMapWithLeaflet();
        });
    }
}

// Синхронизация Яндекс карты с Leaflet
function syncYandexMapWithLeaflet() {
    if (!window.yandexMap) return;

    const center = window.mymap.getCenter();
    const zoom = window.mymap.getZoom();

    window.yandexMap.setCenter([center.lat, center.lng], zoom, {
        duration: 0
    });
}

// Export функции для использования в других модулях
window.initYandexMap = initYandexMap;
window.syncYandexMapWithLeaflet = syncYandexMapWithLeaflet; 