/**
 * РЦМУ List модули JavaScript кодлари
 * РЦМУ маълумотларини юклаш, фильтрлаш ва бошқариш
 */

// Переменные для модальных окон
let currentEditId = null;
let detailModal = null;
let editModal = null;
let uploadModal = null; // Янги модал учун ўзгарувчи

// Модуль инициализацияси
document.addEventListener('DOMContentLoaded', function() {
    // Bootstrap modal ларини инициализация қилиш
    if (typeof bootstrap !== 'undefined') {
        detailModal = new bootstrap.Modal(document.getElementById('detailModal'));
        editModal = new bootstrap.Modal(document.getElementById('editModal'));
        uploadModal = new bootstrap.Modal(document.getElementById('uploadModal')); // Янги модални инициализация қилиш
    }

    // Event listener лар
    setupEventListeners();
    setupColumnFilters(); // Фильтрлар учун янги функция
});

// Устунлар учун фильтрларни созлаш
function setupColumnFilters() {
    const table = document.getElementById('rcmuDataTable');
    if (!table) return;

    const filterElements = table.querySelectorAll('thead .filter-row input, thead .filter-row select');
    const tableRows = table.querySelectorAll('tbody tr');

    filterElements.forEach(element => {
        element.addEventListener('keyup', filterTable); // input учун
        element.addEventListener('change', filterTable); // select учун
    });

    function filterTable() {
        const filterValues = Array.from(filterElements).map(el => el.value.toLowerCase());

        tableRows.forEach(row => {
            let visible = true;
            const cells = row.querySelectorAll('td');

            filterValues.forEach((filterValue, index) => {
                if (filterValue !== '' && cells[index]) {
                    const cellValue = cells[index].textContent || cells[index].innerText;
                    if (cellValue.toLowerCase().indexOf(filterValue) === -1) {
                        visible = false;
                    }
                }
            });
            row.style.display = visible ? '' : 'none';
        });
    }
}

// Event listener лар қўшиш
function setupEventListeners() {
    // Файл юклаш
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                uploadFile(e.target.files[0]);
            }
        });
    }

    // Drag & Drop функциялари
    const uploadArea = document.getElementById('uploadArea');
    if (uploadArea) {
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                uploadFile(files[0]);
            }
        });
    }
}

// Файл юклаш функцияси
function uploadFile(file) {
    const formData = new FormData();
    formData.append('excel_file', file);
    
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
    if (csrfToken) {
        formData.append('csrfmiddlewaretoken', csrfToken.value);
    }

    const progressDiv = document.getElementById('uploadProgress');
    const progressBar = progressDiv ? progressDiv.querySelector('.progress-bar') : null;

    if (progressDiv) {
        progressDiv.style.display = 'block';
    }
    if (progressBar) {
        progressBar.style.width = '0%';
    }

    fetch('/map/rcmu/upload/', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (progressBar) {
            progressBar.style.width = '100%';
        }

        if (data.success) {
            alert('Файл успешно загружен! Загружено записей: ' + data.created_count);
            if (uploadModal) {
                uploadModal.hide(); // Модални ёпиш
            }
            location.reload();
        } else {
            alert('Ошибка: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Произошла ошибка при загрузке файла');
    })
    .finally(() => {
        if (progressDiv) {
            setTimeout(() => {
                progressDiv.style.display = 'none';
            }, 1000);
        }
    });
}

// Детали кўрсатиш
function viewDetails(id) {
    fetch(`/map/rcmu/detail/${id}/`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Ошибка: ' + data.error);
                return;
            }

            const modalBody = document.getElementById('detailModalBody');
            if (modalBody) {
                modalBody.innerHTML = generateDetailHTML(data);
            }

            if (detailModal) {
                detailModal.show();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Произошла ошибка при загрузке данных');
        });
}

// Детали HTML яратиш
function generateDetailHTML(data) {
    return `
        <div class="row">
            <div class="col-md-6">
                <h6>Основная информация</h6>
                <table class="table table-sm">
                    <tr><td><strong>Site:</strong></td><td>${data.site || '—'}</td></tr>
                    <tr><td><strong>Название БС:</strong></td><td>${data.bs_name || '—'}</td></tr>
                    <tr><td><strong>Регион:</strong></td><td>${data.region || '—'}</td></tr>
                    <tr><td><strong>Филиал:</strong></td><td>${data.branch || '—'}</td></tr>
                    <tr><td><strong>Частота:</strong></td><td>${data.freq || '—'}</td></tr>
                    <tr><td><strong>Координаты:</strong></td><td>${data.coordinates || '—'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Даты и ответственность</h6>
                <table class="table table-sm">
                    <tr><td><strong>Дата (LEGO):</strong></td><td>${data.date_lego || '—'}</td></tr>
                    <tr><td><strong>Дата (performance):</strong></td><td>${data.date_performance || '—'}</td></tr>
                    <tr><td><strong>Ответственный:</strong></td><td>${data.responsible || '—'}</td></tr>
                    <tr><td><strong>Предполагаемая дата:</strong></td><td>${data.estimated_air_date || '—'}</td></tr>
                    <tr><td><strong>Время простоя:</strong></td><td>${data.total_down_time_days || '—'}</td></tr>
                </table>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <h6>Причины</h6>
                <p><strong>Причина (рус):</strong> ${data.cause_ru || '—'}</p>
                <p><strong>Причина (англ):</strong> ${data.cause_eng || '—'}</p>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <h6>Комментарии</h6>
                <p><strong>Комментарии:</strong> ${data.comments || '—'}</p>
                <p><strong>Комментарий качественника:</strong> ${data.quality_comment || '—'}</p>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <small class="text-muted">
                    Создано: ${data.created_at} ${data.created_by ? 'пользователем ' + data.created_by : ''}
                </small>
            </div>
        </div>
    `;
}

// Комментарий таҳрирлаш
function editComment(id) {
    currentEditId = id;

    // Жорий комментарийни юклаш
    fetch(`/map/rcmu/detail/${id}/`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Ошибка: ' + data.error);
                return;
            }

            const commentField = document.getElementById('qualityComment');
            if (commentField) {
                commentField.value = data.quality_comment || '';
            }

            if (editModal) {
                editModal.show();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Произошла ошибка при загрузке данных');
        });
}

// Комментарий сақлаш
function saveComment() {
    if (!currentEditId) return;

    const commentField = document.getElementById('qualityComment');
    const comment = commentField ? commentField.value : '';

    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
    if (!csrfToken) {
        alert('CSRF token не найден');
        return;
    }

    fetch(`/map/rcmu/update/${currentEditId}/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken.value
        },
        body: JSON.stringify({
            quality_comment: comment
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Комментарий успешно сохранен');
            if (editModal) {
                editModal.hide();
            }
            location.reload();
        } else {
            alert('Ошибка: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Произошла ошибка при сохранении');
    });
}

// Ёзувни ўчириш
function deleteRecord(id) {
    if (!confirm('Вы уверены, что хотите удалить эту запись?')) {
        return;
    }

    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
    if (!csrfToken) {
        alert('CSRF token не найден');
        return;
    }

    fetch(`/map/rcmu/delete/${id}/`, {
        method: 'DELETE',
        headers: {
            'X-CSRFToken': csrfToken.value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Запись успешно удалена');
            location.reload();
        } else {
            alert('Ошибка: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Произошла ошибка при удалении');
    });
}

