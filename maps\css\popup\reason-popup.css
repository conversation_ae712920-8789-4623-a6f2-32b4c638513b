/* Reason Popup - чап томонда кўрсатиладиган попап */
.reason-popup-container {
    position: fixed;
    z-index: 4000;
    pointer-events: none;
    display: none;
}

.reason-popup {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
    width: 220px;
    min-height: 70px;
    pointer-events: auto;
    transform: translateX(-10px);
    opacity: 0;
    transition: all 0.3s ease;
    border: 1px solid rgba(220, 53, 69, 0.3);
    overflow: hidden;
}

.reason-popup.show {
    transform: translateX(0);
    opacity: 1;
}

.reason-popup-header {
    background: rgba(220, 53, 69, 0.85);
    color: white;
    padding: 4px 8px;
    border-radius: 6px 6px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(176, 42, 55, 0.7);
}

.reason-popup-header h4 {
    margin: 0;
    font-size: 11px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 3px;
}

.reason-popup-header i {
    font-size: 10px;
    color: #fff3cd;
}

.reason-popup-close {
    background: none;
    border: none;
    color: white;
    font-size: 12px;
    cursor: pointer;
    padding: 1px 4px;
    border-radius: 3px;
    transition: background 0.2s;
}

.reason-popup-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.reason-popup-body {
    padding: 12px 10px;
    max-height: 150px;
    overflow-y: auto;
}

.reason-popup-content {
    line-height: 1.4;
}

.reason-text {
    background: rgba(248, 249, 250, 0.8);
    padding: 10px;
    border-radius: 4px;
    border: 1px solid rgba(222, 226, 230, 0.6);
}

.reason-text p {
    margin: 0;
    color: #333;
    font-size: 12px;
    line-height: 1.4;
}

.no-reason-text {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 243, 205, 0.8);
    padding: 10px;
    border-radius: 4px;
    border: 1px solid rgba(255, 234, 167, 0.6);
    color: #856404;
}

.no-reason-text i {
    font-size: 14px;
    color: #ffc107;
}

.no-reason-text p {
    margin: 0;
    font-size: 12px;
    font-weight: 500;
}

/* Скроллбар стили */
.reason-popup-body::-webkit-scrollbar {
    width: 4px;
}

.reason-popup-body::-webkit-scrollbar-track {
    background: #f1f3f4;
    border-radius: 2px;
}

.reason-popup-body::-webkit-scrollbar-thumb {
    background: #c1c8cd;
    border-radius: 2px;
}

.reason-popup-body::-webkit-scrollbar-thumb:hover {
    background: #a8b3ba;
} 