/* Вақт машинаси мобил адаптация ва датepicker стиллари */

/* Адаптив дизайн */
@media (max-width: 768px) {
    .time-machine-toggle-btn {
        top: 70px;
        left: 10px;
        width: 32px;
        height: 32px;
    }
    
    .time-machine-floating-container {
        top: 70px;
        left: 50px;
        width: calc(100vw - 60px);
        max-width: 350px;
        padding: 6px;
    }

    .time-slider-with-inputs {
        flex-direction: column;
        gap: 8px;
        padding: 6px;
    }

    .time-inputs-inline {
        margin-left: 0;
        justify-content: center;
    }

    .time-inputs-inline input[type="date"],
    .time-inputs-inline input[type="text"] {
        width: 80px;
        font-size: 9px;
    }

    .time-inputs-inline select {
        width: 35px;
        font-size: 9px;
    }
}

/* Вақт машинаси контейнери */
.time-machine-container {
    margin-bottom: 8px;
}

.time-machine-container.disabled {
    opacity: 0.6;
    pointer-events: none;
}

/* Datepicker стиллари */
.time-machine-floating-container .datepicker {
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.time-machine-floating-container .datepicker table {
    margin: 0;
    width: 100%;
}

.time-machine-floating-container .datepicker table tr td,
.time-machine-floating-container .datepicker table tr th {
    text-align: center;
    width: 30px;
    height: 30px;
    border: none;
    border-radius: 4px;
}

.time-machine-floating-container .datepicker table tr td.day {
    cursor: pointer;
}

.time-machine-floating-container .datepicker table tr td.day:hover,
.time-machine-floating-container .datepicker table tr td.day.focused {
    background: #eee;
}

.time-machine-floating-container .datepicker table tr td.active,
.time-machine-floating-container .datepicker table tr td.active:hover,
.time-machine-floating-container .datepicker table tr td.active.focused {
    background-color: #ff9800;
    color: white;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

.time-machine-floating-container .datepicker table tr td.today {
    background: #ffecb3;
    color: #333;
}

.time-machine-floating-container .datepicker table tr td.old,
.time-machine-floating-container .datepicker table tr td.new {
    color: #999;
}

.time-machine-floating-container .datepicker table tr td span {
    display: inline-block;
    width: 54px;
    height: 54px;
    line-height: 54px;
    float: left;
    margin: 2px;
    cursor: pointer;
    border-radius: 4px;
}

.time-machine-floating-container .datepicker-days .datepicker-switch,
.time-machine-floating-container .datepicker-months .datepicker-switch,
.time-machine-floating-container .datepicker-years .datepicker-switch {
    width: 145px;
    background: #ff9800;
    color: white;
    font-weight: bold;
}

.time-machine-floating-container .datepicker .prev,
.time-machine-floating-container .datepicker .next {
    cursor: pointer;
    background: transparent;
}

.time-machine-floating-container .datepicker-dropdown {
    margin-top: 2px;
}

.time-machine-floating-container .datepicker-dropdown:before,
.time-machine-floating-container .datepicker-dropdown:after {
    display: none;
}

.time-machine-floating-container .datepicker-months table tr td span {
    width: 54px;
    height: 54px;
    line-height: 54px;
    margin: 2px;
    cursor: pointer;
    border-radius: 4px;
    color: #333;
}

.time-machine-floating-container .datepicker-years table tr td span {
    width: 54px;
    height: 54px;
    line-height: 54px;
    margin: 2px;
    cursor: pointer;
    border-radius: 4px;
    color: #333;
} 