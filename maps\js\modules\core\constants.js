// Модуль констант
// Координаты центров регионов - ФАКТИЧЕСКИЕ ID
window.regionCenters = {
    1: [41.3775, 69.5901],   // Андижанская область
    2: [39.7681, 64.4556],   // Бухарская область
    3: [40.4424, 71.7853],   // город Коканд
    4: [41.2995, 69.2401],   // город Ташкент
    5: [40.1172, 67.8420],   // Джиззахская область
    6: [38.8627, 65.7885],   // Кашкадарьинская область
    7: [42.4534, 59.6104],   // Наваийская область
    8: [40.9983, 71.6726],   // Наманганская область
    9: [42.4534, 59.6104],   // Рес.Каракалпакстан
    10: [39.6542, 66.9597],  // Самаркандская область
    11: [40.8154, 68.7799],  // Сырдарьинская область
    12: [37.2313, 67.2783],  // Сурхандарьинская область
    13: [41.0510, 69.9083],  // Ташкентская область
    14: [40.3864, 71.7825],  // Ферганская область
    15: [41.5597, 60.6333]   // Хорезмская область
};

// Филиаллар структураси - ФАКТИК ID лар билан (логлардан олинган)
window.branches = {
    'VRF': {  // Восточный региональный филиал - ВРФ
        name: 'ВРФ',
        nameEn: 'ERB',
        regions: [14, 1, 8, 3],  // Фергана=14, Андижан=1, Наманган=8, Коканд=3
        center: [40.8, 71.5],
        shortName: 'ВРФ'
    },
    'TRF': {  // Ташкентский региональный филиал - ТРФ
        name: 'ТРФ',
        nameEn: 'TRB',
        regions: [4, 13, 11],  // город Ташкент=4, Ташкентская обл=13, Сырдарья=11
        center: [41.2, 69.5],
        shortName: 'ТРФ'
    },
    'CRF': {  // Центральный региональный филиал - ЦРФ
        name: 'ЦРФ',
        nameEn: 'CRB',
        regions: [10, 5],  // Самарканд=10, Джизак=5
        center: [39.9, 67.3],
        shortName: 'ЦРФ'
    },
    'SWRF': {  // Юго-западный региональный филиал - ЮЗРФ
        name: 'ЮЗРФ',
        nameEn: 'SWRB',
        regions: [2, 7],  // Бухара=2, Навои=7
        center: [40.1, 63.9],
        shortName: 'ЮЗРФ'
    },
    'SRF': {  // Южный региональный филиал - ЮРФ
        name: 'ЮРФ',
        nameEn: 'SRB',
        regions: [6, 12],  // Кашкадарья=6, Сурхандарья=12
        center: [38.0, 66.5],
        shortName: 'ЮРФ'
    },
    'WRF': {  // Западный региональный филиал - ЗРФ
        name: 'ЗРФ',
        nameEn: 'WRB',
        regions: [9, 15],  // Каракалпакстан=9, Хорезм=15
        center: [42.0, 60.1],
        shortName: 'ЗРФ'
    }
};

// Вилоят ID -> Филиал мослиги
window.regionToBranch = {};
Object.keys(window.branches).forEach(branchKey => {
    const branch = window.branches[branchKey];
    branch.regions.forEach(regionId => {
        window.regionToBranch[regionId] = branchKey;
    });
});

// Вилоят номларини қисқартириш
window.getShortRegionName = function(regionName) {
    // Керакмас сўзларни олиб ташлаш (рус ва инглиз тилларида)
    return regionName
        .replace(' область', '')
        .replace('ская', '')
        .replace('инская', '')
        .replace('город ', 'г.')
        .replace('Рес.', '')
        .replace(' Region', '')
        .replace(' region', '')
        .replace(' City', '')
        .replace(' city', '')
        .replace('Republic of ', '');
};

// Туман номларини қисқартириш
window.getShortAreaName = function(areaName) {
    // Керакмас сўзларни олиб ташлаш (рус ва инглиз тилларида)
    return areaName
        .replace(' район', '')
        .replace(' district', '')
        .replace(' District', '')
        .replace(' area', '')
        .replace(' Area', '')
        .replace('город ', 'г.')
        .replace(' city', '')
        .replace(' City', '');
};

// Соответствие названий регионов и их ФАКТИЧЕСКИХ ID (из БД)
const regionNameToId = {
    'Андижанская область': 1,
    'Андижан': 1,
    'Бухарская область': 2,
    'Бухара': 2,
    'город Коканд': 3,
    'Коканд': 3,
    'Джиззахская область': 5,
    'Джизак': 5,
    'Кашкадарьинская область': 6,
    'Кашкадарья': 6,
    'Наваийская область': 7,
    'Навои': 7,
    'Наманганская область': 8,
    'Наманган': 8,
    'Рес.Каракалпакстан': 9,
    'Каракалпакстан': 9,
    'Самаркандская область': 10,
    'Самарканд': 10,
    'Сырдарьинская область': 11,
    'Сырдарья': 11,
    'Сурхандарьинская область': 12,
    'Сурхандарья': 12,
    'Ташкентская область': 13,
    'Ташкент область': 13,
    'Ферганская область': 14,
    'Фергана': 14,
    'Хорезмская область': 15,
    'Хорезм': 15
};

// Цвета для продолжительности аварий
window.downtimeColors = {
    1: '#3388ff', // До 1 часа
    2: '#ffcc00', // До 2 часов
    3: '#a52a2a', // До 3 часов
    4: '#ff0000', // До 4 часов
    5: '#000000'  // Более 4 часов
};

// Цвета по типам технологий
window.techColors = {
    gsm900: '#ff0000',    // Красный
    gsm1800: '#00ff00',   // Зеленый
    umts900: '#0000ff',   // Синий
    umts2100: '#ffff00',  // Желтый
    lte800: '#ff00ff',    // Фиолетовый
    lte1800: '#00ffff',   // Голубой
    lte2100: '#ff8000',   // Оранжевый
    lte2300: '#80ff00',   // Светло-зеленый
    lte2600: '#ff0080'    // Розовый
};

// Конфигурация карты
const mapConfig = {
    defaultCenter: [41.3, 69.3],
    defaultZoom: 6,
    regionZoom: 10,
    areaZoom: 12,
    markerZoomLevel: 15,
    animationDuration: 0.3
};

// Размеры маркеров в зависимости от зума
const markerSizes = {
    default: {
        online: { small: 3, medium: 4, large: 5 },
        offline: { small: 5, medium: 7, large: 9 }
    },
    hover: {
        online: { small: 4, medium: 5, large: 6 },
        offline: { small: 6, medium: 8, large: 10 }
    }
};

// Настройки обновления данных
const updateConfig = {
    realTimeInterval: 10000, // 10 секунд
    timeMachineStep: 5 // 5 минут
};

// Constants loaded successfully 