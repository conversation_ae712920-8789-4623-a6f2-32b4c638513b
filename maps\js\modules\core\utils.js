// Модуль утилитарных функций

// Текущий язык (по умолчанию русский)
window.currentLanguage = localStorage.getItem('mapLanguage') || 'ru';

// Получение номера БС из названия
function getNumberBS(bsName) {
    if (!bsName) return null;

    // БС номидан рақамни олиш (охирги _ дан кейинги рақам)
    const parts = bsName.split('_');
    if (parts.length > 1) {
        const lastPart = parts[parts.length - 1];
        // Агар охирги қисм рақам бўлса
        if (/^\d+$/.test(lastPart)) {
            return lastPart;
        }
    }

    // Агар _ йўқ бўлса, номдаги рақамларни излаш
    const match = bsName.match(/(\d+)/);
    return match ? match[1] : null;
}

// Форматирование даты и времени (DD/MM/YYYY HH:MM - 24 соатлик формат)
window.formatDateTime = function (date) {
    if (!date) return '';

    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${day}/${month}/${year} ${hours}:${minutes}`;
}

// Краткое форматирование времени (только час:минута)
function formatTimeOnly(date) {
    if (!date) return '';

    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${hours}:${minutes}`;
}

// Расчет продолжительности аварии в минутах
function calculateDowntimeMinutes(startTime, endTime = null) {
    if (!startTime) return 0;

    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();

    return Math.floor((end - start) / 60000); // миллисекунды в минуты
}

// Форматирование продолжительности (из минут в часы и минуты)
function formatDowntime(minutes) {
    const isRussian = window.currentLanguage === 'ru';

    if (minutes < 60) {
        return `${minutes} ${isRussian ? 'мин' : 'min'}`;
    } else if (minutes < 1440) { // менее 24 часов
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        if (remainingMinutes === 0) {
            return `${hours} ${isRussian ? 'ч' : 'h'}`;
        } else {
            return `${hours} ${isRussian ? 'ч' : 'h'} ${remainingMinutes} ${isRussian ? 'мин' : 'min'}`;
        }
    } else { // более 24 часов
        const days = Math.floor(minutes / 1440);
        const hours = Math.floor((minutes % 1440) / 60);
        const remainingMinutes = minutes % 60;

        let result = `${days} ${isRussian ? 'д' : 'd'}`;
        if (hours > 0) result += ` ${hours} ${isRussian ? 'ч' : 'h'}`;
        if (remainingMinutes > 0) result += ` ${remainingMinutes} ${isRussian ? 'мин' : 'min'}`;

        return result;
    }
}

// Вычисление центра массива точек
window.calculateCenter = function (points) {
    if (!points || points.length === 0) return null;

    const validPoints = points.filter(p =>
        p.lat && p.lon &&
        !isNaN(parseFloat(p.lat)) &&
        !isNaN(parseFloat(p.lon))
    );

    if (validPoints.length === 0) return null;

    let sumLat = 0;
    let sumLon = 0;

    validPoints.forEach(point => {
        sumLat += parseFloat(point.lat);
        sumLon += parseFloat(point.lon);
    });

    return {
        lat: sumLat / validPoints.length,
        lon: sumLon / validPoints.length
    };
}

// Определение категории продолжительности
function getDurationCategory(minutes) {
    if (minutes <= 60) return 1;      // До 1 часа
    if (minutes <= 120) return 2;     // До 2 часов
    if (minutes <= 180) return 3;     // До 3 часов
    if (minutes <= 240) return 4;     // До 4 часов
    return 5;                          // Более 4 часов
}

// Смена языка интерфейса
window.changeLanguage = function (lang) {

    window.currentLanguage = lang;
    localStorage.setItem('mapLanguage', lang);

    // Безопасное обновление элементов - проверяем существование перед обновлением
    const safeSetText = (id, text) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = text;
        }
    };

    const safeSetPlaceholder = (id, text) => {
        const element = document.getElementById(id);
        if (element) {
            element.placeholder = text;
        }
    };

    const safeSetTitle = (id, text) => {
        const element = document.getElementById(id);
        if (element) {
            element.title = text;
        }
    };

    // Обновление всех текстовых элементов
    safeSetText('stats-header', translations[lang]['stats_header']);
    safeSetText('region-header', translations[lang]['region_header']);
    safeSetText('area-stats-header', translations[lang]['area_header']);
    safeSetText('area-label', translations[lang]['area_label']);
    safeSetText('bs-search-label', translations[lang]['bs_search_label']);
    safeSetPlaceholder('bs-search', translations[lang]['bs_search_placeholder']);
    safeSetText('reset-filters', translations[lang]['reset_filters']);
    safeSetPlaceholder('location-search', translations[lang]['search_location']);
    safeSetTitle('map-button', translations[lang]['map_title']);
    safeSetTitle('satellite-button', translations[lang]['satellite_title']);
    safeSetTitle('osm-source-button', translations[lang]['osm_title']);
    safeSetTitle('yandex-source-button', translations[lang]['yandex_title']);
    safeSetTitle('google-source-button', translations[lang]['google_title']);
    safeSetText('downtime-legend-header', translations[lang]['downtime_legend_header']);

    // Легенда продолжительности
    safeSetText('downtime-label-1', translations[lang]['downtime_1h']);
    safeSetText('downtime-label-2', translations[lang]['downtime_2h']);
    safeSetText('downtime-label-3', translations[lang]['downtime_3h']);
    safeSetText('downtime-label-4', translations[lang]['downtime_4h']);
    safeSetText('downtime-label-5', translations[lang]['downtime_more_4h']);
    safeSetText('downtime-label-6', translations[lang]['downtime_4h_1d']);
    safeSetText('downtime-label-7', translations[lang]['downtime_1d_7d']);
    safeSetText('downtime-label-8', translations[lang]['downtime_more_7d']);
    safeSetText('downtime-label-all', translations[lang]['all']);

    // Машина времени
    safeSetText('time-machine-title', '⏰ ' + translations[lang]['time_machine'] + ':');
    safeSetText('time-machine-enable-label', translations[lang]['time_machine_enable']);
    safeSetText('time-machine-date-label', translations[lang]['time_machine_date']);
    safeSetText('time-machine-hour-label', translations[lang]['time_machine_hour']);
    safeSetText('time-machine-minute-label', translations[lang]['time_machine_minute']);
    safeSetText('time-back-hour', lang === 'ru' ? '-1ч' : '-1h');
    safeSetText('time-back-minute', lang === 'ru' ? '-5м' : '-5m');
    safeSetText('time-now', translations[lang]['now']);
    safeSetText('time-forward-minute', lang === 'ru' ? '+5м' : '+5m');
    safeSetText('time-forward-hour', lang === 'ru' ? '+1ч' : '+1h');

    // Time machine тугмаси tooltip текстини янгилаш
    const timeMachineTooltip = document.querySelector('.time-machine-btn .tooltip-text');
    if (timeMachineTooltip) {
        timeMachineTooltip.textContent = translations[lang]['time_machine'];
    }

    // Time machine calendar тилини янгилаш
    const timeMachineDateInput = document.getElementById('time-machine-date');
    if (timeMachineDateInput && $.fn.datepicker) {
        $(timeMachineDateInput).datepicker('destroy');
        $(timeMachineDateInput).datepicker({
            format: 'yyyy-mm-dd',
            language: lang === 'ru' ? 'ru' : 'en',
            autoclose: true,
            todayHighlight: true,
            todayBtn: 'linked',
            clearBtn: false,
            container: '.time-machine-floating-container',
            orientation: 'bottom auto'
        });
    }

    // Автоматик марказлаштириш кнопкаси title
    const autoCenterControl = document.querySelector('.auto-center-control');
    if (autoCenterControl) {
        autoCenterControl.title = lang === 'ru' ? 'Автоматическое центрирование' : 'Auto centering';
    }

    // Обновить кнопки языка - исправленная логика
    const langRu = document.getElementById('lang-ru');
    const langEn = document.getElementById('lang-en');

    if (langRu && langEn) {
        langRu.classList.remove('active');
        langEn.classList.remove('active');
        document.getElementById('lang-' + lang).classList.add('active');
    }

    // Перевод названий регионов
    const regionSelect = document.getElementById('region');
    if (regionSelect) {
        Array.from(regionSelect.options).forEach(option => {
            if (option.value !== '') {
                // Первый раз сохраняем оригинальное русское имя
                if (!option.dataset.originalName) {
                    option.dataset.originalName = option.textContent;
                }
                // Всегда используем сохраненное оригинальное имя для перевода
                const originalName = option.dataset.originalName;
                const translatedName = lang === 'ru' ? originalName : window.translateRegionName(originalName);
                option.textContent = translatedName;
            }
        });
    }

    // Перевод названий районов
    const areaSelect = document.getElementById('area');
    if (areaSelect) {
        // Обновить текст "Все районы"
        const allAreasOption = areaSelect.querySelector('option[value=""]');
        if (allAreasOption) {
            allAreasOption.textContent = window.translations[lang]['all_areas'];
        }

        if (!areaSelect.disabled && areaSelect.options.length > 1) {
            Array.from(areaSelect.options).forEach(option => {
                if (option.value !== '') {
                    // Первый раз сохраняем оригинальное русское имя
                    if (!option.dataset.originalName) {
                        option.dataset.originalName = option.textContent;
                    }
                    // Всегда используем сохраненное оригинальное имя для перевода
                    const originalName = option.dataset.originalName;
                    const translatedName = lang === 'ru' ? originalName : window.translateAreaName(originalName);
                    option.textContent = translatedName;
                }
            });
        }
    }

    // Обновить статистику регионов с новым языком
    if (window.updateRegionsStats) {
        window.updateRegionsStats();
    }

    // Обновить статистику районов если они отображаются
    const areasContainer = document.getElementById('areas-stats-container');
    if (areasContainer && areasContainer.style.display !== 'none') {
        const selectedRegionId = document.getElementById('region').value;
        if (selectedRegionId && window.updateAreasStats) {
            window.updateAreasStats(selectedRegionId);
        }
    }

    // Обновить все элементы с атрибутом data-translate
    document.querySelectorAll('[data-translate]').forEach(element => {
        const key = element.getAttribute('data-translate');
        if (translations[lang][key]) {
            element.textContent = translations[lang][key];
        }
    });

    // Обновить заголовки кнопок навигации времени
    safeSetTitle('time-back-hour', lang === 'ru' ? '-1 час' : '-1 hour');
    safeSetTitle('time-back-minute', lang === 'ru' ? '-5 минут' : '-5 minutes');
    safeSetTitle('time-now', lang === 'ru' ? 'Сейчас' : 'Now');
    safeSetTitle('time-forward-minute', lang === 'ru' ? '+5 минут' : '+5 minutes');
    safeSetTitle('time-forward-hour', lang === 'ru' ? '+1 час' : '+1 hour');

    // Off Stations Panel тилини янгилаш (агар мавжуд бўлса)
    if (window.updateOffStationsPanelLanguage) {
        // Кичик кечикиш билан чақирамиз, панел яратилиши учун
        setTimeout(() => {
            window.updateOffStationsPanelLanguage();
        }, 100);
    }

    // РЦМУ маркерлар тилини янгилаш (агар мавжуд бўлса)
    if (window.updateRcmuMarkersLanguage) {
        setTimeout(() => {
            window.updateRcmuMarkersLanguage();
        }, 100);
    }

    // Кластерларни қайта қуриш (тил ўзгарганда филиал/вилоят/туман номлари янгиланиши учун)
    if (window.rebuildClusters && window.currentClusterMode) {
        try {
            window.rebuildClusters();
        } catch (error) {
            // Silent error handling
        }
    }
}

// Сохранение состояния фильтров
window.saveFilterState = function () {
    const filterState = {
        region: document.getElementById('region').value,
        area: document.getElementById('area').value,
        bsSearch: document.getElementById('bs-search').value,
        status: document.getElementById('status').value,
        durationFilter: document.getElementById('duration-filter').value
    };
    localStorage.setItem('filterState', JSON.stringify(filterState));
}

// Сброс всех фильтров
window.resetAllFilters = function () {
    // Сбросить все фильтры
    document.getElementById('region').value = '';
    document.getElementById('area').value = '';
    document.getElementById('area').disabled = true;
    document.getElementById('bs-search').value = '';
    document.getElementById('status').value = 'all';
    document.getElementById('duration-filter').value = 'all';

    // Очистить localStorage
    localStorage.removeItem('filterState');

    // Вернуться к общему виду карты
    if (window.mymap) {
        window.mymap.setView([41.3, 69.3], 6);
    }

    // Применить пустые фильтры (показать все)
    if (window.applyCurrentFilters) {
        window.applyCurrentFilters();
    }

    // Обновить статистику
    if (window.updateRegionsStats) {
        window.updateRegionsStats();
    }

    // Пересчитать статистику продолжительности для всех БС
    if (window.calculateDurationStats && window.globalStations) {
        window.calculateDurationStats(window.globalStations);
    }
}

// Восстановление состояния фильтров
window.restoreFilterState = function () {
    const savedState = localStorage.getItem('filterState');
    if (savedState) {
        const filterState = JSON.parse(savedState);

        // Восстановление фильтров
        document.getElementById('region').value = filterState.region || '';
        document.getElementById('bs-search').value = filterState.bsSearch || '';
        document.getElementById('status').value = filterState.status || 'all';

        // Восстановление фильтра продолжительности
        if (filterState.durationFilter) {
            document.getElementById('duration-filter').value = filterState.durationFilter;
        }

        // Применение фильтров
        if (window.applyCurrentFilters) {
            window.applyCurrentFilters();
        }

        // Пересчет статистики продолжительности для текущего фильтра региона
        if (window.calculateDurationStats && window.globalStations) {
            const regionId = document.getElementById('region').value;
            const areaId = document.getElementById('area').value;

            let pointsForDurationStats = [...window.globalStations];

            // Фильтруем по региону если выбран
            if (regionId) {
                pointsForDurationStats = pointsForDurationStats.filter(point => point.region_id == regionId);
            }

            // Фильтруем по району если выбран
            if (areaId) {
                pointsForDurationStats = pointsForDurationStats.filter(point => point.area_id == areaId);
            }

            // Пересчитываем статистику продолжительности
            window.calculateDurationStats(pointsForDurationStats);
        }
    } else {
        // По умолчанию показать все точки
        if (window.applyStatusFilter && window.points) {
            window.applyStatusFilter(window.points, 'all');
        }

        // Пересчитать статистику для всех БС
        if (window.calculateDurationStats && window.globalStations) {
            window.calculateDurationStats(window.globalStations);
        }
    }
} 