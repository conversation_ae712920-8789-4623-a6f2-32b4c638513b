"""
RCMU авария сабабларини бошқариш модули
"""
import json
import logging
from datetime import datetime, timedelta
from django.db.models import Q
from django.shortcuts import get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils import timezone
from ..models import OutageReason
from .rcmu_permissions import check_rcmu_permission
# -- Янги импортлар --
from bsview.models import Current_Alarms
from ..map_data_utils.utils import getNumberBS

logger = logging.getLogger(__name__)


@csrf_exempt
@login_required
@require_http_methods(["POST"])
def create_outage_group(request):
    """
    Создание записи о группе аварийных БС без указания причины.
    Вызывается сразу при обнаружении кластера на фронтенде.
    """
    if not check_rcmu_permission(request.user):
        return JsonResponse({'error': 'У вас нет прав доступа.'}, status=403)
        
    try:
        data = json.loads(request.body)
        group_id = data.get('group_id')
        
        if not group_id:
            return JsonResponse({'error': 'group_id is required.'}, status=400)

        # Проверяем, существует ли уже запись для этой группы
        existing_outage = OutageReason.objects.filter(group_id=group_id).first()
        
        if existing_outage:
            return JsonResponse({
                'success': True,
                'message': 'Outage group already exists.',
                'outage_id': existing_outage.id,
                'created': False
            })

        # Создаем новую запись без причины
        new_outage = OutageReason(
            group_id=group_id,
            area_name=data.get('area_name', ''),
            region_name=data.get('region_name', ''),
            stations_count=data.get('stations_count', 0),
            stations_data=json.dumps(data.get('stations_data', [])),
            incident_timestamp=timezone.now(),  # Используем текущее время
            created_by=request.user,
            center_lat=data.get('center_lat'),
            center_lon=data.get('center_lon'),
            # Явно указываем пустые строки для полей, которые не могут быть NULL
            reason='',
            reason_ru='',
            reason_en=''
        )
        new_outage.save()
        
        return JsonResponse({
            'success': True,
            'message': 'Outage group created successfully.',
            'outage_id': new_outage.id,
            'created': True
        })
        
    except Exception as e:
        logger.error(f"Error creating outage group: {e}", exc_info=True)
        return JsonResponse({'error': str(e)}, status=500)


@csrf_exempt
@login_required
@require_http_methods(["POST"])
def save_outage_reason(request):
    """
    Обновление причины для существующей группы BS Down.
    Создание новой записи было вынесено в create_outage_group.
    """
    if not check_rcmu_permission(request.user):
        return JsonResponse({'error': 'У вас нет прав доступа.'}, status=403)
    
    try:
        data = json.loads(request.body)
        outage_id = data.get('outage_id')
        reason_text = data.get('reason')
        lang = data.get('lang', 'ru')

        if not outage_id or not reason_text:
            return JsonResponse({'error': 'outage_id and reason are required.'}, status=400)
            
        outage = get_object_or_404(OutageReason, id=outage_id)
        
        outage.set_reason(reason_text, lang)
        outage.updated_by = request.user
        outage.save()
        
        return JsonResponse({
            'success': True,
            'message': 'Причина успешно обновлена.',
            'outage_id': outage.id
        })
            
    except OutageReason.DoesNotExist:
        return JsonResponse({'error': 'Outage with provided ID not found.'}, status=404)
    except Exception as e:
        logger.error(f"Error saving outage reason: {e}", exc_info=True)
        return JsonResponse({'error': f'Ошибка при сохранении: {str(e)}'}, status=500)


@login_required
def get_outage_reasons(request):
    """
    Получение всех причин для BS Down зон
    """
    reasons = OutageReason.objects.all()
    
    data = []
    for reason in reasons:
        data.append({
            'id': reason.id,
            'group_id': reason.group_id,
            'reason': reason.reason,
            'reason_ru': reason.reason_ru,
            'reason_en': reason.reason_en,
            'area_name': reason.area_name,
            'region_name': reason.region_name,
            'stations_count': reason.stations_count,
            'incident_timestamp': reason.incident_timestamp.isoformat(),
            'created_at': reason.created_at.isoformat(),
            'created_by': reason.created_by.username if reason.created_by else None
        })
    
    return JsonResponse({'reasons': data})


@csrf_exempt
@login_required
@require_http_methods(["POST"])
def update_outage_reason(request, pk):
    """
    Обновление причины
    Принимает объект reason_data с ключами 'ru' и/или 'en'
    """
    if not check_rcmu_permission(request.user):
        return JsonResponse({'error': 'У вас нет прав доступа.'}, status=403)
    
    reason_obj = get_object_or_404(OutageReason, pk=pk)
    
    try:
        data = json.loads(request.body)
        reason_data = data.get('reason_data')

        if not reason_data or not isinstance(reason_data, dict):
            return JsonResponse({'error': 'reason_data is missing or not a dictionary.'}, status=400)

        # Обновляем причину на разных языках
        if 'ru' in reason_data:
            reason_obj.set_reason(reason_data['ru'], 'ru')
        if 'en' in reason_data:
            reason_obj.set_reason(reason_data['en'], 'en')
        
        reason_obj.updated_by = request.user
        reason_obj.save()
            
        return JsonResponse({
            'success': True,
            'message': 'Причина успешно обновлена.'
        })
            
    except Exception as e:
        logger.error(f"Error updating outage reason: {e}", exc_info=True)
        return JsonResponse({'error': f'Ошибка при обновлении: {str(e)}'}, status=500)


@login_required
@require_http_methods(["DELETE"])
def delete_outage_reason(request, pk):
    """
    Удаление причины
    """
    # Проверка прав доступа (только администраторы могут удалять)
    if not (hasattr(request.user, 'privilege_id') and request.user.privilege_id == 1):
        return JsonResponse({'error': 'У вас нет прав для удаления.'}, status=403)
    
    reason = get_object_or_404(OutageReason, pk=pk)
    
    try:
        reason.delete()
        return JsonResponse({
            'success': True,
            'message': 'Причина успешно удалена.'
        })
        
    except Exception as e:
        return JsonResponse({'error': f'Ошибка при удалении: {str(e)}'}, status=500) 


@csrf_exempt
@login_required
def get_outage_reasons_updates(request):
    """
    Получение последних обновлений причин (для реального времени)
    """
    try:
        # Получаем параметр последнего обновления от клиента
        last_update_str = request.GET.get('last_update')
        
        if last_update_str:
            try:
                # URL декодировка не нужна, Django автоматически декодирует GET параметры
                # Обрабатываем ISO формат с Z (UTC)
                if last_update_str.endswith('Z'):
                    last_update_str = last_update_str[:-1]
                
                last_update = datetime.fromisoformat(last_update_str)
                
                # Если USE_TZ=False, то работаем с naive datetime'ами
                if timezone.is_aware(last_update):
                    last_update = timezone.make_naive(last_update, timezone.utc)
                    
            except (ValueError, TypeError) as e:
                # Если формат даты неправильный, используем последний час
                last_update = datetime.now() - timedelta(hours=1)
        else:
            # Если параметр не указан, получаем обновления за последний час
            last_update = datetime.now() - timedelta(hours=1)
        
        # Получаем все причины, обновленные после указанного времени
        updated_reasons = OutageReason.objects.filter(
            updated_at__gt=last_update
        ).order_by('-updated_at')
        
        # Формируем данные для отправки
        updates = []
        for reason in updated_reasons:
            updates.append({
                'id': reason.id,
                'group_id': reason.group_id,
                'reason': reason.reason,
                'reason_ru': reason.reason_ru,
                'reason_en': reason.reason_en,
                'area_name': reason.area_name,
                'region_name': reason.region_name,
                'stations_count': reason.stations_count,
                'incident_timestamp': reason.incident_timestamp.isoformat(),
                'created_at': reason.created_at.isoformat(),
                'updated_at': reason.updated_at.isoformat(),
                'created_by': reason.created_by.username if reason.created_by else None
            })
        
        current_time = datetime.now()
        
        return JsonResponse({
            'success': True,
            'updates': updates,
            'current_time': current_time.isoformat(),
            'has_updates': len(updates) > 0
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Ошибка при получении обновлений: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["POST"]) # Changed to POST to accept a request body
def delete_selected_reasons(request):
    """
    Удаляет выбранные записи OutageReason по списку ID.
    Принимает JSON тело: { "ids": [1, 2, 3] }
    Перед удалением проверяет, что ни одна из записей не является активной.
    """
    if not check_rcmu_permission(request.user):
        return JsonResponse({'error': 'У вас нет прав для выполнения этой операции.'}, status=403)

    try:
        data = json.loads(request.body)
        reason_ids = data.get('ids')

        if not isinstance(reason_ids, list) or not reason_ids:
            return JsonResponse({'error': 'Требуется непустой список ID.'}, status=400)

        # 1. Получаем множество имен всех активных аварийных БС для проверки
        active_alarm_bs_names = set(Current_Alarms.objects.values_list('bsname', flat=True))
        
        # 2. Получаем все запрошенные на удаление объекты
        reasons_to_check = OutageReason.objects.filter(pk__in=reason_ids)
        
        final_pks_to_delete = []
        
        # 3. Проверяем каждую запись на неактивность
        for reason in reasons_to_check:
            if not reason.stations_data:
                final_pks_to_delete.append(reason.pk)
                continue
            
            try:
                stations = json.loads(reason.stations_data)
                offline_count = 0
                for station in stations:
                    station_name = station.get('name')
                    if station_name and station_name in active_alarm_bs_names:
                        offline_count += 1
                
                # Только если кластер НЕ активен, добавляем в список на удаление
                if offline_count < 3:
                    final_pks_to_delete.append(reason.pk)

            except json.JSONDecodeError:
                # Если JSON некорректный, считаем запись неактивной
                final_pks_to_delete.append(reason.pk)
                continue
        
        # 4. Удаляем только те записи, которые прошли проверку
        if not final_pks_to_delete:
             return JsonResponse({
                'success': True,
                'message': 'Не найдено неактивных записей для удаления.',
                'deleted_count': 0
            })

        deleted_count, _ = OutageReason.objects.filter(pk__in=final_pks_to_delete).delete()

        return JsonResponse({
            'success': True,
            'message': f'Удалено {deleted_count} записей.',
            'deleted_count': deleted_count
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Неверный формат JSON.'}, status=400)
    except Exception as e:
        logger.error(f"Ошибка при удалении выбранных причин: {e}", exc_info=True)
        return JsonResponse({'error': f'Произошла внутренняя ошибка: {str(e)}'}, status=500) 