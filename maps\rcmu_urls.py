from django.urls import path
from . import rcmu_views

app_name = 'rcmu'

urlpatterns = [
    # Главная страница РЦМУ
    path('', rcmu_views.rcmu_list, name='list'),
    
    # Страница BS Down Zones причин
    path('treez/', rcmu_views.rcmu_treez, name='treez'),
    
    # Загрузка Excel файла
    path('upload/', rcmu_views.rcmu_upload, name='upload'),
    
    # CRUD операции
    path('detail/<int:pk>/', rcmu_views.rcmu_detail, name='detail'),
    path('update/<int:pk>/', rcmu_views.rcmu_update, name='update'),
    path('delete/<int:pk>/', rcmu_views.rcmu_delete, name='delete'),
    
    # API для карты
    path('api/data/', rcmu_views.rcmu_api_data, name='api_data'),
    
    # API для причин BS Down
    path('api/save-reason/', rcmu_views.save_outage_reason, name='save_reason'),
    path('api/create-outage-group/', rcmu_views.create_outage_group, name='create_outage_group'),
    path('api/get-reasons/', rcmu_views.get_outage_reasons, name='get_reasons'),
    path('api/get-reasons-updates/', rcmu_views.get_outage_reasons_updates, name='get_reasons_updates'),
    path('api/update-reason/<int:pk>/', rcmu_views.update_outage_reason, name='update_reason'),
    path('api/delete-reason/<int:pk>/', rcmu_views.delete_outage_reason, name='delete_reason'),
    path('api/reasons/delete_selected/', rcmu_views.delete_selected_reasons, name='delete_selected_reasons'),
]
